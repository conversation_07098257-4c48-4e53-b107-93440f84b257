﻿<%--
 =======================================================================
 FILE: Invoice.aspx (Corrected Version)
 =======================================================================
--%>
<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="Invoice.aspx.cs" Inherits="MediEase.Pharmacist.Invoice" %>

<!DOCTYPE html>
<html lang="en">
<head runat="server">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice - Order #<asp:Literal ID="OrderIDLabel" runat="server"></asp:Literal></title>
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet" />
    <style>
        body { background-color: #f8f9fa; }
        .invoice-container { max-width: 800px; margin: 30px auto; background-color: #fff; padding: 40px; border: 1px solid #dee2e6; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
        .invoice-header { text-align: center; margin-bottom: 40px; }
        .invoice-header h2 { color: #007bff; }
        .invoice-details, .customer-details { margin-bottom: 30px; }
        .invoice-table th { background-color: #e9ecef; }
        .totals-table { width: 40%; margin-left: auto; }
        .footer-note { text-align: center; margin-top: 40px; color: #6c757d; font-size: 0.9em; }
        @media print {
            body { background-color: #fff; }
            .invoice-container { margin: 0; border: 0; box-shadow: none; }
            .no-print { display: none; }
        }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <asp:PlaceHolder ID="InvoiceView" runat="server">
            <div class="invoice-container">
                <div class="text-right no-print mb-3">
                    <button type="button" class="btn btn-primary" onclick="window.print();">Print Invoice</button>
                </div>
                <div class="invoice-header">
                    <h2>MediEase Pharmacy</h2>
                    <p>Dehimandu, Baitadi, Sudurpashchim</p>
                    <p><EMAIL> | +977 9864757868</p>
                    <hr/>
                    <h3>INVOICE</h3>
                </div>
                <div class="row invoice-details">
                    <div class="col-6">
                        <strong>Billed To:</strong><br>
                        <asp:Literal ID="CustomerNameLabel" runat="server"></asp:Literal><br>
                        <asp:Literal ID="ShippingAddressLabel" runat="server"></asp:Literal><br>
                        <asp:Literal ID="CustomerPhoneLabel" runat="server"></asp:Literal><br>
                    </div>
                    <div class="col-6 text-right">
                        <strong>Invoice #</strong> <asp:Literal ID="InvoiceNumberLabel" runat="server"></asp:Literal><br>
                        <strong>Order Date:</strong> <asp:Literal ID="OrderDateLabel" runat="server"></asp:Literal><br>
                    </div>
                </div>
                <table class="table table-bordered invoice-table">
                    <thead>
                        <tr>
                            <th>Item Description</th>
                            <th class="text-right">Quantity</th>
                            <th class="text-right">Unit Price</th>
                            <th class="text-right">Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        <asp:Repeater ID="OrderItemsRepeater" runat="server">
                            <ItemTemplate>
                                <tr>
                                    <td><%# Eval("Name") %></td>
                                    <td class="text-right"><%# Eval("Quantity") %></td>
                                    <td class="text-right"><%# Eval("PricePerUnit", "{0:C}") %></td>
                                    <%-- THIS LINE WAS CORRECTED --%>
                                    <td class="text-right"><%# (Convert.ToDecimal(Eval("Quantity")) * Convert.ToDecimal(Eval("PricePerUnit"))).ToString("C") %></td>
                                </tr>
                            </ItemTemplate>
                        </asp:Repeater>
                    </tbody>
                </table>
                <div class="row">
                    <div class="col-7">
                        <p class="text-muted">Thank you for your business!</p>
                    </div>
                    <div class="col-5">
                        <table class="table totals-table">
                            <tbody>
                                <tr>
                                    <td><strong>Subtotal:</strong></td>
                                    <td class="text-right"><asp:Literal ID="SubtotalLabel" runat="server"></asp:Literal></td>
                                </tr>
                                 <tr>
                                    <td><strong>Discount:</strong></td>
                                    <td class="text-right"><asp:Literal ID="DiscountLabel" runat="server"></asp:Literal></td>
                                </tr>
                                <tr class="font-weight-bold">
                                    <td><strong>Grand Total:</strong></td>
                                    <td class="text-right"><asp:Literal ID="GrandTotalLabel" runat="server"></asp:Literal></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="footer-note">
                    <p>This is a computer-generated invoice and does not require a signature.</p>
                </div>
            </div>
        </asp:PlaceHolder>
         <asp:PlaceHolder ID="ErrorView" runat="server" Visible="false">
             <div class="alert alert-danger text-center container">
                <h2>Order Not Found</h2>
                <p>The order you are trying to generate an invoice for does not exist.</p>
            </div>
        </asp:PlaceHolder>
    </form>
</body>
</html>