﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="OrderHistory.aspx.cs" Inherits="Customer_OrderHistory" %>

<!DOCTYPE html>
<html lang="en">
<head runat="server">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order History - MediEase</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <style>
        body { font-family: 'Inter', sans-serif; }
        .sidebar-link { display: flex; align-items: center; padding: 0.75rem 1rem; border-radius: 0.5rem; transition: background-color 0.2s, color 0.2s; }
        .sidebar-link:hover, .sidebar-link.active { background-color: #0d9488; color: white; }
        .sidebar-link i { width: 1.25rem; margin-right: 0.75rem; }
    </style>
</head>
<body class="bg-gray-100">
    <form id="form1" runat="server">
        <div class="flex h-screen bg-gray-100">
            <!-- Sidebar -->
            <aside class="w-64 bg-teal-600 text-white flex-col p-4 hidden md:flex">
                <div class="flex items-center space-x-2 pb-4 border-b border-teal-500">
                    <i class="fas fa-pills text-3xl"></i><span class="text-2xl font-bold">MediEase</span>
                </div>
                <nav class="mt-6 flex-grow">
                    <a href="Dashboard.aspx" class="sidebar-link"><i class="fas fa-tachometer-alt fa-fw"></i><span>Dashboard</span></a>
                    <a href="Profile.aspx" class="sidebar-link"><i class="fas fa-user-edit fa-fw"></i><span>My Profile</span></a>
                    <a href="OrderHistory.aspx" class="sidebar-link active"><i class="fas fa-history fa-fw"></i><span>Order History</span></a>
                    <a href="Prescriptions.aspx" class="sidebar-link"><i class="fas fa-file-prescription fa-fw"></i><span>Prescriptions</span></a>
                    <a href="Reminders.aspx" class="sidebar-link"><i class="fas fa-bell fa-fw"></i><span>Reminders</span></a>
                </nav>
                <div class="pt-4 border-t border-teal-500">
                    <asp:LinkButton ID="btnLogout" runat="server" OnClick="btnLogout_Click" CssClass="sidebar-link w-full"><i class="fas fa-sign-out-alt fa-fw"></i><span>Logout</span></asp:LinkButton>
                </div>
            </aside>

            <!-- Main Content -->
            <main class="flex-1 flex flex-col">
                <header class="bg-white shadow-sm p-4">
                    <h1 class="text-2xl font-bold text-gray-800">My Order History</h1>
                </header>

                <div class="flex-1 p-6 overflow-y-auto">
                    <div class="bg-white p-6 rounded-lg shadow">
                         <asp:GridView ID="gvOrders" runat="server" AutoGenerateColumns="False"
                             DataKeyNames="OrderId"
                             CssClass="min-w-full divide-y divide-gray-200"
                             HeaderStyle-CssClass="bg-gray-50" PagerStyle-CssClass="bg-white p-4"
                             AllowPaging="True" PageSize="10" OnPageIndexChanging="gvOrders_PageIndexChanging">
                             <Columns>
                                 <asp:BoundField DataField="OrderNumber" HeaderText="Order #" HeaderStyle-CssClass="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" ItemStyle-CssClass="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900" />
                                 <asp:BoundField DataField="OrderDate" HeaderText="Date Placed" DataFormatString="{0:MMM dd, yyyy}" HeaderStyle-CssClass="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" ItemStyle-CssClass="px-6 py-4 whitespace-nowrap text-sm text-gray-500" />
                                 <asp:BoundField DataField="TotalAmount" HeaderText="Total" DataFormatString="{0:C}" HeaderStyle-CssClass="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" ItemStyle-CssClass="px-6 py-4 whitespace-nowrap text-sm text-gray-500" />
                                 
                                 <asp:TemplateField HeaderText="Status" HeaderStyle-CssClass="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" ItemStyle-CssClass="px-6 py-4 whitespace-nowrap">
                                     <ItemTemplate>
                                         <span class='px-2 inline-flex text-xs leading-5 font-semibold rounded-full <%# GetStatusClass(Eval("Status").ToString()) %>'>
                                             <%# Eval("Status") %>
                                         </span>
                                     </ItemTemplate>
                                 </asp:TemplateField>

                                 <asp:TemplateField HeaderText="Action" HeaderStyle-CssClass="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider" ItemStyle-CssClass="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                                     <ItemTemplate>
                                         <a href='CustomerOrderDetail.aspx?id=<%# Eval("OrderId") %>' class="text-teal-600 hover:text-teal-900">View Details</a>
                                     </ItemTemplate>
                                 </asp:TemplateField>
                             </Columns>
                             <EmptyDataTemplate>
                                 <div class="text-center py-12">
                                     <i class="fas fa-receipt text-5xl text-gray-300"></i>
                                     <p class="mt-4 text-gray-500">You have not placed any orders yet.</p>
                                     <a href="/default.aspx" class="mt-4 inline-block px-4 py-2 bg-teal-600 text-white font-semibold rounded-lg shadow-md hover:bg-teal-700">Start Shopping</a>
                                 </div>
                             </EmptyDataTemplate>
                         </asp:GridView>
                    </div>
                </div>
            </main>
        </div>
    </form>
</body>
</html>
