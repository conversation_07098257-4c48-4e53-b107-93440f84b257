<%@ Page Title="Home" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="Default.aspx.cs" Inherits="MediEase._Default" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <!-- Promotional Slider -->
    <div id="heroCarousel" class="carousel slide" data-bs-ride="carousel" data-bs-interval="5000">
        <div class="carousel-indicators">
            <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="0" class="active"></button>
            <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="1"></button>
            <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="2"></button>
        </div>
        <div class="carousel-inner">
            <!-- Slide 1: Main Welcome -->
            <div class="carousel-item active">
                <div class="hero-slide bg-gradient-primary text-white py-5" style="min-height: 500px;">
                    <div class="container">
                        <div class="row align-items-center h-100">
                            <div class="col-lg-6">
                                <h1 class="display-4 fw-bold mb-4">Welcome to MediEase</h1>
                                <p class="lead mb-4">Your trusted smart pharmacy management system with AI-powered features for better healthcare. Get medicines delivered to your doorstep with expert consultation.</p>
                                <div class="d-flex gap-3">
                                    <asp:Button ID="btnGetStarted" runat="server" CssClass="btn btn-light btn-lg" Text="Get Started" OnClick="btnGetStarted_Click" />
                                    <a href="#features" class="btn btn-outline-light btn-lg">Learn More</a>
                                </div>
                            </div>
                            <div class="col-lg-6 text-center">
                                <img src="~/Images/hero-pharmacy.jpg" alt="Modern Pharmacy" class="img-fluid rounded shadow" style="max-height: 350px;" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Slide 2: AI Features -->
            <div class="carousel-item">
                <div class="hero-slide bg-gradient-success text-white py-5" style="min-height: 500px;">
                    <div class="container">
                        <div class="row align-items-center h-100">
                            <div class="col-lg-6">
                                <h1 class="display-4 fw-bold mb-4">AI-Powered Healthcare</h1>
                                <p class="lead mb-4">Get personalized medicine recommendations and instant health support with our advanced AI chatbot. Smart healthcare at your fingertips.</p>
                                <div class="alert alert-success mb-4" style="background: rgba(255,255,255,0.9); color: #155724; border: none;">
                                    <i class="fas fa-check-circle me-2"></i><strong>AI Assistant is Live!</strong> Click the chat button below or the floating icon to start chatting.
                                </div>
                                <div class="d-flex gap-3">
                                    <button type="button" class="btn btn-light btn-lg" onclick="toggleChatbot()">
                                        <i class="fas fa-robot me-2"></i>Try AI Assistant
                                    </button>
                                    <a href="~/Register.aspx" runat="server" class="btn btn-outline-light btn-lg">Sign Up Free</a>
                                </div>
                            </div>
                            <div class="col-lg-6 text-center">
                                <i class="fas fa-robot" style="font-size: 200px; opacity: 0.8;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Slide 3: Delivery -->
            <div class="carousel-item">
                <div class="hero-slide bg-gradient-info text-white py-5" style="min-height: 500px;">
                    <div class="container">
                        <div class="row align-items-center h-100">
                            <div class="col-lg-6">
                                <h1 class="display-4 fw-bold mb-4">Fast & Secure Delivery</h1>
                                <p class="lead mb-4">Free delivery on orders over $50. Secure packaging and temperature-controlled shipping for all medicines. Track your order in real-time.</p>
                                <div class="d-flex gap-3">
                                    <a href="~/Medicines.aspx" runat="server" class="btn btn-light btn-lg">Start Shopping</a>
                                    <a href="#delivery-info" class="btn btn-outline-light btn-lg">Delivery Info</a>
                                </div>
                            </div>
                            <div class="col-lg-6 text-center">
                                <i class="fas fa-shipping-fast" style="font-size: 200px; opacity: 0.8;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <button class="carousel-control-prev" type="button" data-bs-target="#heroCarousel" data-bs-slide="prev">
            <span class="carousel-control-prev-icon"></span>
            <span class="visually-hidden">Previous</span>
        </button>
        <button class="carousel-control-next" type="button" data-bs-target="#heroCarousel" data-bs-slide="next">
            <span class="carousel-control-next-icon"></span>
            <span class="visually-hidden">Next</span>
        </button>
    </div>

    <!-- Quick Actions -->
    <section class="quick-actions py-5">
        <div class="container">
            <div class="row g-4">
                <div class="col-md-3">
                    <div class="card h-100 text-center border-0 shadow-sm">
                        <div class="card-body p-4">
                            <div class="text-primary mb-3">
                                <i class="fas fa-search fa-3x"></i>
                            </div>
                            <h5 class="card-title">Search Medicines</h5>
                            <p class="card-text">Find medicines quickly with our advanced search and AI recommendations.</p>
                            <a href="~/Medicines.aspx" runat="server" class="btn btn-primary">Browse Now</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card h-100 text-center border-0 shadow-sm">
                        <div class="card-body p-4">
                            <div class="text-success mb-3">
                                <i class="fas fa-prescription fa-3x"></i>
                            </div>
                            <h5 class="card-title">Upload Prescription</h5>
                            <p class="card-text">Upload your prescription and get medicines delivered to your home.</p>
                            <a href="~/Prescription.aspx" runat="server" class="btn btn-success">Upload Now</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card h-100 text-center border-0 shadow-sm">
                        <div class="card-body p-4">
                            <div class="text-info mb-3">
                                <i class="fas fa-robot fa-3x"></i>
                            </div>
                            <h5 class="card-title">AI Assistant</h5>
                            <p class="card-text">Get instant help from our AI-powered chatbot for health queries.</p>
                            <button type="button" class="btn btn-info" onclick="toggleChatbot()">Chat Now</button>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card h-100 text-center border-0 shadow-sm">
                        <div class="card-body p-4">
                            <div class="text-warning mb-3">
                                <i class="fas fa-truck fa-3x"></i>
                            </div>
                            <h5 class="card-title">Home Delivery</h5>
                            <p class="card-text">Fast and secure delivery of medicines to your doorstep.</p>
                            <a href="~/About.aspx" runat="server" class="btn btn-warning">Learn More</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Medicines -->
    <section class="featured-medicines py-5 bg-light">
        <div class="container">
            <div class="row mb-5">
                <div class="col-12 text-center">
                    <h2 class="display-5 fw-bold mb-3">Featured Medicines</h2>
                    <p class="lead text-muted">Popular and trusted medicines available at great prices</p>
                </div>
            </div>
            <div class="row g-4">
                <asp:Repeater ID="rptFeaturedMedicines" runat="server">
                    <ItemTemplate>
                        <div class="col-lg-3 col-md-4 col-sm-6">
                            <div class="card medicine-card h-100 border-0 shadow-sm">
                                <div class="position-relative">
                                    <img src='<%# ResolveUrl("~/Images/Products/" + Eval("ImagePath")) %>' 
                                         alt='<%# Eval("Name") %>' 
                                         class="card-img-top medicine-image" 
                                         onerror="this.src='~/Images/no-image.jpg'" />
                                    <%# Convert.ToDecimal(Eval("DiscountPercentage")) > 0 ?
                                        "<span class=\"discount-badge\">" + Eval("DiscountPercentage") + "% OFF</span>" : "" %>
                                </div>
                                <div class="card-body d-flex flex-column">
                                    <h6 class="card-title"><%# Eval("Name") %></h6>
                                    <p class="card-text text-muted small"><%# Eval("GenericName") %></p>
                                    <p class="card-text small"><%# Eval("Strength") %> - <%# Eval("PackSize") %> <%# Eval("Unit") %></p>
                                    <div class="mt-auto">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <div>
                                                <span class="price-tag">$<%# String.Format("{0:F2}", Eval("FinalPrice")) %></span>
                                                <%# Convert.ToDecimal(Eval("DiscountAmount")) > 0 || Convert.ToDecimal(Eval("DiscountPercentage")) > 0 ?
                                                    "<small class=\"original-price ms-1\">$" + String.Format("{0:F2}", Eval("Price")) + "</small>" : "" %>
                                            </div>
                                            <span class="stock-status stock-<%# Convert.ToInt32(Eval("StockQuantity")) > 0 ? "in" : "out" %>">
                                                <%# Convert.ToInt32(Eval("StockQuantity")) > 0 ? "In Stock" : "Out of Stock" %>
                                            </span>
                                        </div>
                                        <%# Convert.ToInt32(Eval("StockQuantity")) > 0 ?
                                            "<button class=\"btn btn-primary btn-sm w-100 add-to-cart\" data-medicine-id=\"" + Eval("MedicineId") + "\"><i class=\"fas fa-cart-plus me-1\"></i>Add to Cart</button>" :
                                            "<button class=\"btn btn-secondary btn-sm w-100\" disabled>Out of Stock</button>" %>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </ItemTemplate>
                </asp:Repeater>
            </div>
            <div class="text-center mt-4">
                <a href="~/Medicines.aspx" runat="server" class="btn btn-outline-primary btn-lg">View All Medicines</a>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features py-5">
        <div class="container">
            <div class="row mb-5">
                <div class="col-12 text-center">
                    <h2 class="display-5 fw-bold mb-3">Why Choose MediEase?</h2>
                    <p class="lead text-muted">Advanced features for modern healthcare management</p>
                </div>
            </div>
            <div class="row g-4">
                <div class="col-lg-4 col-md-6">
                    <div class="feature-item text-center p-4">
                        <div class="feature-icon text-primary mb-3">
                            <i class="fas fa-brain fa-3x"></i>
                        </div>
                        <h5>AI-Powered Recommendations</h5>
                        <p class="text-muted">Get personalized medicine recommendations based on your symptoms and medical history using advanced AI technology.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="feature-item text-center p-4">
                        <div class="feature-icon text-success mb-3">
                            <i class="fas fa-shield-alt fa-3x"></i>
                        </div>
                        <h5>Secure & Compliant</h5>
                        <p class="text-muted">Your health data is protected with enterprise-grade security and full compliance with healthcare regulations.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="feature-item text-center p-4">
                        <div class="feature-icon text-info mb-3">
                            <i class="fas fa-clock fa-3x"></i>
                        </div>
                        <h5>24/7 Support</h5>
                        <p class="text-muted">Round-the-clock customer support and AI chatbot assistance for all your pharmacy needs.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="feature-item text-center p-4">
                        <div class="feature-icon text-warning mb-3">
                            <i class="fas fa-mobile-alt fa-3x"></i>
                        </div>
                        <h5>Mobile Responsive</h5>
                        <p class="text-muted">Access your pharmacy services from any device with our fully responsive design.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="feature-item text-center p-4">
                        <div class="feature-icon text-danger mb-3">
                            <i class="fas fa-heart fa-3x"></i>
                        </div>
                        <h5>Health Monitoring</h5>
                        <p class="text-muted">Track your medication adherence and health progress with intelligent monitoring tools.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="feature-item text-center p-4">
                        <div class="feature-icon text-primary mb-3">
                            <i class="fas fa-users fa-3x"></i>
                        </div>
                        <h5>Family Management</h5>
                        <p class="text-muted">Manage prescriptions and health records for your entire family from one account.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Statistics Section -->
    <section class="statistics py-5 bg-primary text-white">
        <div class="container">
            <div class="row text-center">
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stat-item">
                        <h2 class="display-4 fw-bold">
                            <asp:Literal ID="litTotalMedicines" runat="server" Text="500+" />
                        </h2>
                        <p class="lead">Medicines Available</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stat-item">
                        <h2 class="display-4 fw-bold">
                            <asp:Literal ID="litTotalCustomers" runat="server" Text="1000+" />
                        </h2>
                        <p class="lead">Happy Customers</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stat-item">
                        <h2 class="display-4 fw-bold">
                            <asp:Literal ID="litTotalOrders" runat="server" Text="5000+" />
                        </h2>
                        <p class="lead">Orders Delivered</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stat-item">
                        <h2 class="display-4 fw-bold">99%</h2>
                        <p class="lead">Customer Satisfaction</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action -->
    <section class="cta py-5">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h2 class="display-5 fw-bold mb-3">Ready to Get Started?</h2>
                    <p class="lead mb-4">Join thousands of satisfied customers who trust MediEase for their healthcare needs.</p>
                    <div class="d-flex justify-content-center gap-3">
                        <asp:PlaceHolder ID="phGuestCTA" runat="server" Visible="true">
                            <a href="~/Register.aspx" runat="server" class="btn btn-primary btn-lg">
                                <i class="fas fa-user-plus me-2"></i>Sign Up Now
                            </a>
                            <a href="~/Login.aspx" runat="server" class="btn btn-outline-primary btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i>Login
                            </a>
                        </asp:PlaceHolder>
                        <asp:PlaceHolder ID="phUserCTA" runat="server" Visible="false">
                            <a href="~/Medicines.aspx" runat="server" class="btn btn-primary btn-lg">
                                <i class="fas fa-shopping-cart me-2"></i>Start Shopping
                            </a>
                            <a href="~/Prescription.aspx" runat="server" class="btn btn-outline-primary btn-lg">
                                <i class="fas fa-prescription me-2"></i>Upload Prescription
                            </a>
                        </asp:PlaceHolder>
                    </div>
                </div>
            </div>
        </div>
    </section>
</asp:Content>

<asp:Content ID="ScriptContent" ContentPlaceHolderID="ScriptContent" runat="server">
    <script>
        $(document).ready(function() {
            // Initialize featured medicines carousel if needed
            initializeFeaturedMedicines();
            
            // Animate statistics on scroll
            animateStatistics();
        });

        function initializeFeaturedMedicines() {
            // Add any specific initialization for featured medicines
            $('.medicine-card').hover(
                function() { $(this).addClass('shadow-lg'); },
                function() { $(this).removeClass('shadow-lg'); }
            );
        }

        function animateStatistics() {
            // Animate numbers when they come into view
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        animateNumber(entry.target);
                    }
                });
            });

            $('.stat-item h2').each(function() {
                observer.observe(this);
            });
        }

        function animateNumber(element) {
            const target = parseInt(element.textContent.replace(/\D/g, ''));
            const suffix = element.textContent.replace(/\d/g, '');
            let current = 0;
            const increment = target / 50;
            
            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }
                element.textContent = Math.floor(current) + suffix;
            }, 30);
        }
    </script>
</asp:Content>
