﻿using System;
using System.Configuration;
using System.Data.SqlClient;
using System.Web.UI;

namespace MediEase
{
    public partial class Login : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            RegisterHyperLink.NavigateUrl = ResolveUrl("~/Register.aspx");
            ForgotPasswordHyperLink.NavigateUrl = ResolveUrl("~/ForgotPassword.aspx");
        }

        protected void LogIn_Click(object sender, EventArgs e)
        {
            if (IsValid)
            {
                string email = Email.Text.Trim();
                string password = Password.Text;
                string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    string query = "SELECT Users.UserID, Users.RoleID, Roles.RoleName FROM Users INNER JOIN Roles ON Users.RoleID = Roles.RoleID WHERE Users.Email = @Email AND Users.Password = @Password AND Users.IsActive = 1";
                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        cmd.Parameters.AddWithValue("@Email", email);
                        cmd.Parameters.AddWithValue("@Password", password);
                        try
                        {
                            con.Open();
                            SqlDataReader reader = cmd.ExecuteReader();
                            if (reader.Read())
                            {
                                int userId = Convert.ToInt32(reader["UserID"]);
                                Session["UserID"] = userId.ToString();
                                Session["RoleID"] = reader["RoleID"].ToString();
                                Session["RoleName"] = reader["RoleName"].ToString();
                                AuditLogger.LogAction(userId, "User Login");
                                switch (Session["RoleName"].ToString())
                                {
                                    case "Admin": Response.Redirect("~/Admin/Dashboard.aspx", false); break;
                                    case "Pharmacist": Response.Redirect("~/Pharmacist/Dashboard.aspx", false); break;
                                    case "Customer": Response.Redirect("~/Customer/Dashboard.aspx", false); break;
                                    default: Response.Redirect("~/Default.aspx", false); break;
                                }
                                Context.ApplicationInstance.CompleteRequest();
                            }
                            else
                            {
                                FailureText.Text = "Invalid email or password.";
                                ErrorMessage.Visible = true;
                            }
                        }
                        catch (Exception ex)
                        {
                            FailureText.Text = "An error occurred. Please try again later.";
                            ErrorMessage.Visible = true;
                            System.Diagnostics.Debug.WriteLine("Login Error: " + ex.Message);
                        }
                    }
                }
            }
        }
    }
}