﻿/*
 =======================================================================
 MediEase: Database Schema Update for Notifications
 PURPOSE: Adds a new table to store user-specific notifications.
 =======================================================================
*/
IF OBJECT_ID('dbo.Notifications', 'U') IS NOT NULL 
    DROP TABLE dbo.Notifications;
GO

CREATE TABLE Notifications (
    NotificationID INT PRIMARY KEY IDENTITY(1,1),
    UserID INT NOT NULL, -- The user who receives the notification
    Message NVARCHAR(500) NOT NULL,
    LinkUrl NVARCHAR(500) NULL, -- A URL the user can click to see details
    IsRead BIT NOT NULL DEFAULT 0,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),

    CONSTRAINT FK_Notifications_Users FOREIGN KEY (UserID) REFERENCES Users(UserID) ON DELETE CASCADE
);
GO

PRINT 'Notifications table created successfully.';
GO