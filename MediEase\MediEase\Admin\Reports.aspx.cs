﻿/*
 =======================================================================
 FILE: Reports.aspx.cs
 PURPOSE: Backend logic for the admin reports page. Fetches and
          calculates order data based on selected filters.
 PLACE IN: The 'Admin' folder in your project root.
 =======================================================================
*/
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace MediEase.Admin
{
    public partial class Reports : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (Session["UserID"] == null || Session["RoleName"]?.ToString() != "Admin")
            {
                Response.Redirect("~/Login.aspx");
                return;
            }

            if (!IsPostBack)
            {
                PopulateStatusFilter();
                // Set default date range to the current month
                StartDateBox.Text = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1).ToString("yyyy-MM-dd");
                EndDateBox.Text = DateTime.Now.ToString("yyyy-MM-dd");
            }
        }

        private void PopulateStatusFilter()
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "SELECT StatusID, StatusName FROM OrderStatus ORDER BY StatusID";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    try
                    {
                        con.Open();
                        SqlDataAdapter sda = new SqlDataAdapter(cmd);
                        DataTable dt = new DataTable();
                        sda.Fill(dt);

                        StatusFilter.DataSource = dt;
                        StatusFilter.DataTextField = "StatusName";
                        StatusFilter.DataValueField = "StatusID";
                        StatusFilter.DataBind();

                        StatusFilter.Items.Insert(0, new ListItem("All Statuses", "0"));
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine("Error populating status filter: " + ex.Message);
                    }
                }
            }
        }

        protected void GenerateReportButton_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(StartDateBox.Text) || string.IsNullOrEmpty(EndDateBox.Text)) return;

            DateTime startDate = DateTime.Parse(StartDateBox.Text);
            DateTime endDate = DateTime.Parse(EndDateBox.Text).AddDays(1); // Include the whole end day
            string statusId = StatusFilter.SelectedValue;

            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                var query = @"
                    SELECT 
                        o.OrderID,
                        up.FirstName + ' ' + up.LastName AS CustomerName,
                        o.OrderDate,
                        os.StatusName,
                        o.TotalAmount
                    FROM 
                        Orders o
                    INNER JOIN 
                        UserProfiles up ON o.CustomerID = up.UserID
                    INNER JOIN 
                        OrderStatus os ON o.StatusID = os.StatusID
                    WHERE 
                        o.OrderDate >= @StartDate AND o.OrderDate < @EndDate
                ";

                if (statusId != "0")
                {
                    query += " AND o.StatusID = @StatusID";
                }
                query += " ORDER BY o.OrderDate DESC;";

                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@StartDate", startDate);
                    cmd.Parameters.AddWithValue("@EndDate", endDate);
                    if (statusId != "0")
                    {
                        cmd.Parameters.AddWithValue("@StatusID", statusId);
                    }

                    try
                    {
                        con.Open();
                        SqlDataAdapter sda = new SqlDataAdapter(cmd);
                        DataTable dt = new DataTable();
                        sda.Fill(dt);

                        // Bind the detailed grid
                        OrdersReportGrid.DataSource = dt;
                        OrdersReportGrid.DataBind();

                        // Calculate summary stats from the DataTable
                        decimal totalRevenue = dt.AsEnumerable().Sum(row => row.Field<decimal>("TotalAmount"));
                        int totalOrders = dt.Rows.Count;
                        decimal avgOrderValue = (totalOrders > 0) ? totalRevenue / totalOrders : 0;

                        // Populate summary cards
                        TotalRevenueLabel.Text = totalRevenue.ToString("C");
                        TotalOrdersLabel.Text = totalOrders.ToString();
                        AvgOrderValueLabel.Text = avgOrderValue.ToString("C");

                        DateRangeLabel.Text = $"{startDate:MMM dd, yyyy} to {endDate.AddDays(-1):MMM dd, yyyy}";
                        ReportView.Visible = true;
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine("Error generating admin report: " + ex.Message);
                    }
                }
            }
        }
    }
}