﻿/*
 =======================================================================
 FILE: ViewDiscounts.aspx.cs
 PURPOSE: Backend logic for the pharmacist's read-only view of all
          discount codes in the system.
 PLACE IN: The 'Pharmacist' folder in your project root.
 =======================================================================
*/
using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace MediEase.Pharmacist
{
    public partial class ViewDiscounts : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            // Security Check: Ensure user is logged in and is a Pharmacist
            if (Session["UserID"] == null || Session["RoleName"]?.ToString() != "Pharmacist")
            {
                Response.Redirect("~/Login.aspx");
                return;
            }

            if (!IsPostBack)
            {
                BindDiscountsGrid();
            }
        }

        private void BindDiscountsGrid()
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "SELECT DiscountID, DiscountCode, Description, DiscountPercentage, StartDate, EndDate, IsActive FROM Discounts ORDER BY DiscountID DESC";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    try
                    {
                        con.Open();
                        SqlDataAdapter sda = new SqlDataAdapter(cmd);
                        DataTable dt = new DataTable();
                        sda.Fill(dt);
                        DiscountsGrid.DataSource = dt;
                        DiscountsGrid.DataBind();
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine("Error binding discounts grid for pharmacist: " + ex.Message);
                    }
                }
            }
        }

        protected void DiscountsGrid_PageIndexChanging(object sender, GridViewPageEventArgs e)
        {
            DiscountsGrid.PageIndex = e.NewPageIndex;
            BindDiscountsGrid();
        }
    }
}