﻿/* FILE: Customer/OrderDetails.aspx.cs */
using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Web.UI;

namespace MediEase.Customer
{
    public partial class OrderDetails : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (Session["UserID"] == null || Session["RoleName"]?.ToString() != "Customer") { Response.Redirect("~/Login.aspx"); return; }
            if (!IsPostBack)
            {
                if (int.TryParse(Request.QueryString["OrderID"], out int orderId)) { LoadOrderDetails(orderId); }
                else { ShowErrorView(); }
            }
        }

        private void LoadOrderDetails(int orderId)
        {
            int customerId = Convert.ToInt32(Session["UserID"]);
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                try
                {
                    con.Open();
                    const string orderQuery = "SELECT o.OrderDate, o.TotalAmount, o.ShippingAddress, os.StatusName FROM Orders o INNER JOIN OrderStatus os ON o.StatusID = os.StatusID WHERE o.OrderID = @OrderID AND o.CustomerID = @CustomerID;";
                    bool orderFound = false;
                    using (SqlCommand cmd = new SqlCommand(orderQuery, con))
                    {
                        cmd.Parameters.AddWithValue("@OrderID", orderId);
                        cmd.Parameters.AddWithValue("@CustomerID", customerId);
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                orderFound = true;
                                OrderIDLabel.Text = orderId.ToString();
                                OrderDateLabel.Text = Convert.ToDateTime(reader["OrderDate"]).ToString("MMMM dd, yyyy");
                                OrderTotalLabel.Text = Convert.ToDecimal(reader["TotalAmount"]).ToString("C");
                                ShippingAddressLabel.Text = Server.HtmlEncode(reader["ShippingAddress"].ToString()).Replace("\n", "<br/>");
                                string status = reader["StatusName"].ToString();
                                OrderStatusLabel.Text = status;
                                OrderStatusLabel.CssClass = "badge badge-pill " + GetStatusBadgeClass(status);
                            }
                        }
                    }
                    if (orderFound)
                    {
                        const string itemsQuery = "SELECT p.Name, oi.Quantity, oi.PricePerUnit FROM OrderItems oi INNER JOIN Products p ON oi.ProductID = p.ProductID WHERE oi.OrderID = @OrderID;";
                        using (SqlCommand cmd = new SqlCommand(itemsQuery, con))
                        {
                            cmd.Parameters.AddWithValue("@OrderID", orderId);
                            SqlDataAdapter sda = new SqlDataAdapter(cmd);
                            DataTable dt = new DataTable();
                            sda.Fill(dt);
                            OrderItemsGrid.DataSource = dt;
                            OrderItemsGrid.DataBind();
                        }
                        OrderDetailsView.Visible = true;
                        ErrorView.Visible = false;
                    }
                    else { ShowErrorView(); }
                }
                catch (Exception ex) { System.Diagnostics.Debug.WriteLine("Error loading order details: " + ex.Message); ShowErrorView(); }
            }
        }

        private void ShowErrorView() { OrderDetailsView.Visible = false; ErrorView.Visible = true; }

        public string GetStatusBadgeClass(string status)
        {
            switch (status.ToLower())
            {
                case "pending payment": case "under review": return "badge-warning";
                case "processing": return "badge-info";
                case "shipped": case "delivered": return "badge-success";
                case "cancelled": case "rejected": return "badge-danger";
                default: return "badge-secondary";
            }
        }
    }
}