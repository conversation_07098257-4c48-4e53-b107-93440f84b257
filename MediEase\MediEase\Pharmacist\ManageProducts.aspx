﻿<%--
 =======================================================================
 FILE: ManageProducts.aspx (Final Version)
 =======================================================================
--%>
<%@ Page Title="Manage Products" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="ManageProducts.aspx.cs" Inherits="MediEase.Pharmacist.ManageProducts" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>Manage Products</h1>
            <a href="Dashboard.aspx" class="btn btn-outline-secondary">&larr; Back to Dashboard</a>
        </div>
        <div class="mb-3"><button type="button" class="btn btn-primary" data-toggle="modal" data-target="#addProductModal"><i class="fas fa-plus mr-2"></i>Add New Product</button></div>
        <div class="card shadow-sm">
            <div class="card-header"><h4 class="mb-0">Product Catalog</h4></div>
            <div class="card-body">
                <asp:GridView ID="ProductsGrid" runat="server" CssClass="table table-hover table-striped align-middle" AutoGenerateColumns="False" DataKeyNames="ProductID" GridLines="None" AllowPaging="True" OnPageIndexChanging="ProductsGrid_PageIndexChanging" OnRowDataBound="ProductsGrid_RowDataBound" OnRowEditing="ProductsGrid_RowEditing" OnRowUpdating="ProductsGrid_RowUpdating" OnRowCancelingEdit="ProductsGrid_RowCancelingEdit" OnRowDeleting="ProductsGrid_RowDeleting" PageSize="10">
                    <Columns>
                        <asp:BoundField DataField="ProductID" HeaderText="ID" ReadOnly="true" />
                        <asp:TemplateField HeaderText="Image"><ItemTemplate><asp:Image ID="ProductImage" runat="server" ImageUrl='<%# Eval("ImageUrl") %>' Width="70" Height="70" CssClass="rounded" /></ItemTemplate></asp:TemplateField>
                        <asp:TemplateField HeaderText="Product Name"><ItemTemplate><%# Eval("Name") %></ItemTemplate><EditItemTemplate><asp:TextBox ID="txtName" runat="server" Text='<%# Bind("Name") %>' CssClass="form-control"></asp:TextBox></EditItemTemplate></asp:TemplateField>
                        <asp:BoundField DataField="CategoryName" HeaderText="Category" />
                        <asp:TemplateField HeaderText="Price"><ItemTemplate>$<%# Eval("Price", "{0:N2}") %></ItemTemplate><EditItemTemplate><asp:TextBox ID="txtPrice" runat="server" Text='<%# Bind("Price", "{0:F2}") %>' CssClass="form-control"></asp:TextBox></EditItemTemplate></asp:TemplateField>
                        <asp:TemplateField HeaderText="Stock"><ItemTemplate><%# Eval("QuantityInStock") %></ItemTemplate><EditItemTemplate><asp:TextBox ID="txtStock" runat="server" Text='<%# Bind("QuantityInStock") %>' CssClass="form-control" TextMode="Number"></asp:TextBox></EditItemTemplate></asp:TemplateField>
                        <asp:CommandField ShowEditButton="true" ControlStyle-CssClass="btn btn-sm btn-info" />
                        <asp:TemplateField><ItemTemplate><asp:LinkButton ID="DeleteButton" runat="server" CommandName="Delete" CommandArgument='<%# Eval("ProductID") %>' Text="Delete" CssClass="btn btn-sm btn-danger" OnClientClick="return confirm('Are you sure you want to delete this product?');" /></ItemTemplate></asp:TemplateField>
                    </Columns>
                </asp:GridView>
            </div>
        </div>
    </div>
    <div class="modal fade" id="addProductModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header"><h5 class="modal-title">Add New Product</h5><button type="button" class="close" data-dismiss="modal"><span>&times;</span></button></div>
                <div class="modal-body">
                     <div class="form-group"><label>Product Image</label><asp:FileUpload ID="NewProductImage" runat="server" CssClass="form-control-file" /></div>
                    <div class="form-group"><label>Product Name</label><asp:TextBox ID="NewProductName" runat="server" CssClass="form-control"></asp:TextBox></div>
                     <div class="form-group"><label>Description</label><asp:TextBox ID="NewProductDesc" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="3"></asp:TextBox></div>
                    <div class="form-row">
                        <div class="form-group col-md-6"><label>Category</label><asp:DropDownList ID="NewProductCategory" runat="server" CssClass="form-control"></asp:DropDownList></div>
                         <div class="form-group col-md-6"><label>Brand</label><asp:DropDownList ID="NewProductBrand" runat="server" CssClass="form-control"></asp:DropDownList></div>
                    </div>
                     <div class="form-row">
                        <div class="form-group col-md-6"><label>Form</label><asp:TextBox ID="NewProductForm" runat="server" CssClass="form-control"></asp:TextBox></div>
                         <div class="form-group col-md-6"><label>Strength</label><asp:TextBox ID="NewProductStrength" runat="server" CssClass="form-control"></asp:TextBox></div>
                    </div>
                    <div class="form-row">
                        <div class="form-group col-md-6"><label>Price</label><asp:TextBox ID="NewProductPrice" runat="server" CssClass="form-control" TextMode="Number" step="0.01"></asp:TextBox></div>
                         <div class="form-group col-md-6"><label>Initial Stock</label><asp:TextBox ID="NewProductStock" runat="server" CssClass="form-control" TextMode="Number"></asp:TextBox></div>
                    </div>
                     <div class="form-group"><asp:CheckBox ID="NewProductIsPrescription" runat="server" Text=" Requires Prescription" /></div>
                </div>
                <div class="modal-footer"><button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button><asp:Button ID="AddProductButton" runat="server" Text="Add Product" CssClass="btn btn-primary" OnClick="AddProductButton_Click" /></div>
            </div>
        </div>
    </div>
</asp:Content>