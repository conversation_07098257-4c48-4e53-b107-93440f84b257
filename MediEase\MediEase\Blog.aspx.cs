﻿/*
 =======================================================================
 FILE: Blog.aspx.cs (Final Corrected Version)
 =======================================================================
*/
using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace MediEase
{
    public partial class Blog : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                BindBlogPosts();
            }
        }

        private void BindBlogPosts()
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                // FIX: Changed INNER JOIN to LEFT JOIN and added ISNULL for the author's name.
                // This makes the query more robust and ensures posts will show even if an author profile is missing.
                const string query = @"
                    SELECT 
                        bp.PostID, 
                        bp.Title, 
                        bp.ImageUrl, 
                        ISNULL(up.FirstName + ' ' + up.LastName, 'Admin') AS AuthorName, 
                        bp.PublishDate
                    FROM 
                        BlogPosts bp
                    LEFT JOIN 
                        UserProfiles up ON bp.AuthorID = up.UserID
                    WHERE 
                        bp.IsPublished = 1 
                    ORDER BY 
                        bp.PublishDate DESC;";

                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    try
                    {
                        con.Open();
                        SqlDataAdapter sda = new SqlDataAdapter(cmd);
                        DataTable dt = new DataTable();
                        sda.Fill(dt);

                        if (dt.Rows.Count > 0)
                        {
                            BlogPostsRepeater.DataSource = dt;
                            BlogPostsRepeater.DataBind();
                            NoPostsView.Visible = false;
                        }
                        else
                        {
                            BlogPostsRepeater.DataSource = null;
                            BlogPostsRepeater.DataBind();
                            NoPostsView.Visible = true;
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine("Error binding blog posts: " + ex.Message);
                        NoPostsView.Visible = true;
                    }
                }
            }
        }

        protected void BlogPostsRepeater_ItemDataBound(object sender, RepeaterItemEventArgs e)
        {
            if (e.Item.ItemType == ListItemType.Item || e.Item.ItemType == ListItemType.AlternatingItem)
            {
                Image postImage = (Image)e.Item.FindControl("PostImage");
                if (postImage != null)
                {
                    string imageUrl = DataBinder.Eval(e.Item.DataItem, "ImageUrl") as string;
                    if (string.IsNullOrEmpty(imageUrl))
                    {
                        postImage.ImageUrl = "~/Images/Blog/default_blog_image.png";
                    }
                    else
                    {
                        postImage.ImageUrl = ResolveUrl(imageUrl);
                    }
                }
            }
        }
    }
}