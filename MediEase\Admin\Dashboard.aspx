<%@ Page Language="C#" AutoEventWireup="true" CodeFile="Dashboard.aspx.cs" Inherits="Admin_Dashboard" %>

<!DOCTYPE html>
<html lang="en">
<head runat="server">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - MediEase</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { font-family: 'Inter', sans-serif; }
        .sidebar-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            transition: background-color 0.2s, color 0.2s;
        }
        .sidebar-link:hover, .sidebar-link.active {
            background-color: #0d9488; /* teal-700 */
            color: white;
        }
        .sidebar-link i {
            width: 1.25rem;
            margin-right: 0.75rem;
        }
    </style>
</head>
<body class="bg-gray-100">
    <form id="form1" runat="server">
        <div class="flex h-screen bg-gray-100">
            <!-- Sidebar -->
            <aside class="w-64 bg-teal-600 text-white flex flex-col p-4">
                <div class="flex items-center space-x-2 pb-4 border-b border-teal-500">
                    <i class="fas fa-pills text-3xl"></i>
                    <span class="text-2xl font-bold">MediEase</span>
                </div>
                <nav class="mt-6 flex-grow">
                    <a href="Dashboard.aspx" class="sidebar-link active">
                        <i class="fas fa-tachometer-alt fa-fw"></i>
                        <span>Dashboard</span>
                    </a>
                    <a href="UserManagement.aspx" class="sidebar-link">
                        <i class="fas fa-users-cog fa-fw"></i>
                        <span>User Management</span>
                    </a>
                    <a href="ProductManagement.aspx" class="sidebar-link">
                        <i class="fas fa-capsules fa-fw"></i>
                        <span>Products</span>
                    </a>
                     <a href="Reports.aspx" class="sidebar-link">
                        <i class="fas fa-chart-line fa-fw"></i>
                        <span>Reports</span>
                    </a>
                    <a href="SystemSettings.aspx" class="sidebar-link">
                        <i class="fas fa-cogs fa-fw"></i>
                        <span>System Settings</span>
                    </a>
                     <a href="AuditLog.aspx" class="sidebar-link">
                        <i class="fas fa-clipboard-list fa-fw"></i>
                        <span>Audit Log</span>
                    </a>
                </nav>
                <div class="pt-4 border-t border-teal-500">
                    <asp:LinkButton ID="btnLogout" runat="server" OnClick="btnLogout_Click" CssClass="sidebar-link w-full">
                        <i class="fas fa-sign-out-alt fa-fw"></i>
                        <span>Logout</span>
                    </asp:LinkButton>
                </div>
            </aside>

            <!-- Main Content -->
            <main class="flex-1 flex flex-col">
                <header class="bg-white shadow-sm p-4">
                    <div class="flex justify-between items-center">
                         <h1 class="text-2xl font-bold text-gray-800">
                            <asp:Literal ID="litWelcomeMessage" runat="server"></asp:Literal>
                        </h1>
                         <div class="flex items-center space-x-4">
                             <asp:Literal ID="litUserInitial" runat="server"></asp:Literal>
                         </div>
                    </div>
                </header>

                <div class="flex-1 p-6 overflow-y-auto">
                    <!-- Stats Cards -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                        <div class="bg-white p-6 rounded-lg shadow flex items-center">
                            <i class="fas fa-dollar-sign text-4xl text-green-500"></i>
                            <div class="ml-4">
                                <p class="text-gray-500">Total Revenue</p>
                                <asp:Literal ID="litTotalRevenue" runat="server" Text="$0.00" CssClass="text-2xl font-bold"></asp:Literal>
                            </div>
                        </div>
                         <div class="bg-white p-6 rounded-lg shadow flex items-center">
                            <i class="fas fa-shopping-cart text-4xl text-blue-500"></i>
                            <div class="ml-4">
                                <p class="text-gray-500">Total Sales</p>
                                <asp:Literal ID="litTotalSales" runat="server" Text="0" CssClass="text-2xl font-bold"></asp:Literal>
                            </div>
                        </div>
                        <div class="bg-white p-6 rounded-lg shadow flex items-center">
                            <i class="fas fa-users text-4xl text-yellow-500"></i>
                             <div class="ml-4">
                                <p class="text-gray-500">New Customers (30d)</p>
                                <asp:Literal ID="litNewCustomers" runat="server" Text="0" CssClass="text-2xl font-bold"></asp:Literal>
                            </div>
                        </div>
                        <div class="bg-white p-6 rounded-lg shadow flex items-center">
                            <i class="fas fa-boxes text-4xl text-purple-500"></i>
                             <div class="ml-4">
                                <p class="text-gray-500">Total Products</p>
                                <asp:Literal ID="litTotalProducts" runat="server" Text="0" CssClass="text-2xl font-bold"></asp:Literal>
                            </div>
                        </div>
                    </div>

                    <!-- Charts and Recent Activity -->
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <!-- Sales Chart -->
                        <div class="lg:col-span-2 bg-white p-6 rounded-lg shadow">
                            <h2 class="text-xl font-bold text-gray-800 mb-4">Sales Overview (Last 6 Months)</h2>
                            <canvas id="salesChart"></canvas>
                        </div>

                        <!-- Recent Activity -->
                        <div class="bg-white p-6 rounded-lg shadow">
                             <h2 class="text-xl font-bold text-gray-800 mb-4">Recent Activity</h2>
                             <asp:Repeater ID="rptRecentActivity" runat="server">
                                 <ItemTemplate>
                                     <div class="flex items-start py-3 border-b last:border-b-0">
                                         <div class="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center mr-4">
                                             <i class='<%# GetIconForAction(Eval("Action").ToString()) %> text-gray-600'></i>
                                         </div>
                                         <div>
                                             <p class="text-sm text-gray-800"><%# Eval("ActionDetails") %></p>
                                             <p class="text-xs text-gray-500"><%# Eval("Timestamp", "{0:MMM dd, yyyy hh:mm tt}") %></p>
                                         </div>
                                     </div>
                                 </ItemTemplate>
                                 <AlternatingItemTemplate>
                                       <div class="flex items-start py-3 border-b last:border-b-0">
                                         <div class="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center mr-4">
                                             <i class='<%# GetIconForAction(Eval("Action").ToString()) %> text-gray-600'></i>
                                         </div>
                                         <div>
                                             <p class="text-sm text-gray-800"><%# Eval("ActionDetails") %></p>
                                             <p class="text-xs text-gray-500"><%# Eval("Timestamp", "{0:MMM dd, yyyy hh:mm tt}") %></p>
                                         </div>
                                     </div>
                                 </AlternatingItemTemplate>
                                 <FooterTemplate>
                                      <%# (rptRecentActivity.Items.Count == 0) ? "<div class='text-center py-8 text-gray-500'><i class='fas fa-history text-3xl mb-2'></i><p>No recent activity found.</p></div>" : "" %>
                                 </FooterTemplate>
                             </asp:Repeater>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </form>
    
    <script>
        // This script block will be populated by the C# code-behind with sales data.
        // It renders the sales chart using Chart.js.
        var salesChartData = {
            labels: [],
            datasets: [{
                label: 'Sales Revenue',
                data: [],
                backgroundColor: 'rgba(13, 148, 136, 0.2)', // teal-600 with opacity
                borderColor: 'rgba(13, 148, 136, 1)', // teal-600
                borderWidth: 2,
                tension: 0.4,
                fill: true
            }]
        };

        var salesChartCtx = document.getElementById('salesChart').getContext('2d');
        var mySalesChart = new Chart(salesChartCtx, {
            type: 'line',
            data: salesChartData,
            options: {
                scales: {
                    y: {
                        beginAtZero: true
                    }
                },
                responsive: true,
                maintainAspectRatio: false
            }
        });
    </script>
</body>
</html>
