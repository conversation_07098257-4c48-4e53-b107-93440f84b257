﻿/*
 =======================================================================
 FILE: AutoRefills.aspx.cs (Full & Unabridged)
 PURPOSE: Backend logic for the customer's automatic refills page.
          **UPDATED** to dynamically populate frequencies from settings.
 PLACE IN: The 'Customer' folder in your project root.
 =======================================================================
*/
using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;

namespace MediEase.Customer
{
    public partial class AutoRefills : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (Session["UserID"] == null || Session["RoleName"]?.ToString() != "Customer")
            {
                Response.Redirect("~/Login.aspx?returnUrl=" + Server.UrlEncode(Request.Url.AbsolutePath));
                return;
            }

            if (!IsPostBack)
            {
                PopulatePurchasedProducts();
                PopulateFrequencies(); // New method call
                BindAutoRefillsGrid();
            }
        }

        private void PopulatePurchasedProducts()
        {
            int customerId = Convert.ToInt32(Session["UserID"]);
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = @"
                    SELECT DISTINCT p.ProductID, p.Name 
                    FROM Products p
                    INNER JOIN OrderItems oi ON p.ProductID = oi.ProductID
                    INNER JOIN Orders o ON oi.OrderID = o.OrderID
                    WHERE o.CustomerID = @CustomerID AND p.IsActive = 1
                    ORDER BY p.Name;
                ";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@CustomerID", customerId);
                    try
                    {
                        con.Open();
                        SqlDataAdapter sda = new SqlDataAdapter(cmd);
                        DataTable dt = new DataTable();
                        sda.Fill(dt);
                        ProductDropDown.DataSource = dt;
                        ProductDropDown.DataTextField = "Name";
                        ProductDropDown.DataValueField = "ProductID";
                        ProductDropDown.DataBind();
                        ProductDropDown.Items.Insert(0, new ListItem("-- Select a Medication --", ""));
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine("Error populating purchased products: " + ex.Message);
                    }
                }
            }
        }

        private void PopulateFrequencies()
        {
            // Get the setting from the database, with a safe default value
            string frequenciesSetting = SettingsService.GetStringSetting("AutoRefillFrequencies", "30,60,90");
            var frequencies = frequenciesSetting.Split(',');

            FrequencyDropDown.Items.Clear();
            foreach (var freq in frequencies)
            {
                if (int.TryParse(freq.Trim(), out int days))
                {
                    FrequencyDropDown.Items.Add(new ListItem($"Every {days} Days", days.ToString()));
                }
            }
        }

        private void BindAutoRefillsGrid()
        {
            int customerId = Convert.ToInt32(Session["UserID"]);
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = @"
                    SELECT r.AutoRefillID, p.Name, r.FrequencyInDays, r.NextRefillDate 
                    FROM AutoRefills r
                    INNER JOIN Products p ON r.ProductID = p.ProductID
                    WHERE r.CustomerID = @CustomerID AND r.IsActive = 1
                    ORDER BY r.NextRefillDate;
                ";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@CustomerID", customerId);
                    try
                    {
                        con.Open();
                        SqlDataAdapter sda = new SqlDataAdapter(cmd);
                        DataTable dt = new DataTable();
                        sda.Fill(dt);
                        AutoRefillsGrid.DataSource = dt;
                        AutoRefillsGrid.DataBind();
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine("Error binding auto-refills grid: " + ex.Message);
                    }
                }
            }
        }

        protected void SetupRefillButton_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(ProductDropDown.SelectedValue))
            {
                ShowMessage("Please select a medication to set up a refill.", false);
                return;
            }

            int customerId = Convert.ToInt32(Session["UserID"]);
            int productId = Convert.ToInt32(ProductDropDown.SelectedValue);
            int frequency = Convert.ToInt32(FrequencyDropDown.SelectedValue);
            DateTime nextRefillDate = DateTime.Now.AddDays(frequency);
            string shippingAddress = "";

            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                con.Open();
                const string checkQuery = "SELECT COUNT(*) FROM AutoRefills WHERE CustomerID = @CustomerID AND ProductID = @ProductID AND IsActive = 1";
                using (SqlCommand checkCmd = new SqlCommand(checkQuery, con))
                {
                    checkCmd.Parameters.AddWithValue("@CustomerID", customerId);
                    checkCmd.Parameters.AddWithValue("@ProductID", productId);
                    if ((int)checkCmd.ExecuteScalar() > 0)
                    {
                        ShowMessage("An active auto-refill already exists for this product.", false);
                        return;
                    }
                }

                const string addressQuery = "SELECT Address FROM UserProfiles WHERE UserID = @UserID";
                using (SqlCommand addressCmd = new SqlCommand(addressQuery, con))
                {
                    addressCmd.Parameters.AddWithValue("@UserID", customerId);
                    object result = addressCmd.ExecuteScalar();
                    if (result != null) { shippingAddress = result.ToString(); }
                }

                const string insertQuery = "INSERT INTO AutoRefills (CustomerID, ProductID, FrequencyInDays, NextRefillDate, ShippingAddress, IsActive) VALUES (@CustomerID, @ProductID, @Frequency, @NextRefillDate, @Address, 1)";
                using (SqlCommand cmd = new SqlCommand(insertQuery, con))
                {
                    cmd.Parameters.AddWithValue("@CustomerID", customerId);
                    cmd.Parameters.AddWithValue("@ProductID", productId);
                    cmd.Parameters.AddWithValue("@Frequency", frequency);
                    cmd.Parameters.AddWithValue("@NextRefillDate", nextRefillDate);
                    cmd.Parameters.AddWithValue("@Address", shippingAddress);
                    try
                    {
                        cmd.ExecuteNonQuery();
                        ShowMessage("Auto-refill set up successfully!", true);
                        BindAutoRefillsGrid();
                    }
                    catch (Exception ex)
                    {
                        ShowMessage("Error setting up auto-refill. Please try again.", false);
                        System.Diagnostics.Debug.WriteLine("Error setting auto-refill: " + ex.Message);
                    }
                }
            }
        }

        protected void AutoRefillsGrid_RowDeleting(object sender, GridViewDeleteEventArgs e)
        {
            int autoRefillId = Convert.ToInt32(AutoRefillsGrid.DataKeys[e.RowIndex].Value);
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "UPDATE AutoRefills SET IsActive = 0 WHERE AutoRefillID = @AutoRefillID AND CustomerID = @CustomerID";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@AutoRefillID", autoRefillId);
                    cmd.Parameters.AddWithValue("@CustomerID", Convert.ToInt32(Session["UserID"]));
                    try { con.Open(); cmd.ExecuteNonQuery(); }
                    catch (Exception ex) { System.Diagnostics.Debug.WriteLine("Error cancelling auto-refill: " + ex.Message); }
                }
            }
            BindAutoRefillsGrid();
        }

        private void ShowMessage(string message, bool isSuccess)
        {
            MessageText.Text = message;
            MessageAlert.Attributes["class"] = isSuccess ? "alert alert-success" : "alert alert-danger";
            MessagePlaceholder.Visible = true;
        }
    }
}