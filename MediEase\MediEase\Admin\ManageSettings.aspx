﻿<%--
 =======================================================================
 FILE: ManageSettings.aspx
 PURPOSE: Allows an administrator to configure site-wide settings.
          **UPDATED** to include Auto-Refill options.
 PLACE IN: The 'Admin' folder in your project root.
 =======================================================================
--%>
<%@ Page Title="System Settings" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="ManageSettings.aspx.cs" Inherits="MediEase.Admin.ManageSettings" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>System Settings</h1>
            <a href="Dashboard.aspx" class="btn btn-outline-secondary">&larr; Back to Admin Dashboard</a>
        </div>
        
        <asp:PlaceHolder ID="MessagePlaceholder" runat="server" Visible="false">
            <div class="alert alert-success">
                Settings saved successfully!
            </div>
        </asp:PlaceHolder>

        <div class="row">
            <div class="col-md-6">
                <div class="card shadow-sm mb-4">
                    <div class="card-header">
                        <h4 class="mb-0">Loyalty Program Configuration</h4>
                    </div>
                    <div class="card-body">
                        <p>Define the rules for how customers earn and redeem loyalty points.</p>
                        <div class="form-group">
                            <label>Points Awarded per Dollar Spent</label>
                            <asp:TextBox ID="PointsPerDollarBox" runat="server" CssClass="form-control" TextMode="Number" placeholder="e.g., 1"></asp:TextBox>
                        </div>
                        <div class="form-group">
                            <label>Points Required for a $1 Discount</label>
                            <asp:TextBox ID="PointsValueBox" runat="server" CssClass="form-control" TextMode="Number" placeholder="e.g., 100"></asp:TextBox>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <!-- ADDED: New Card for Auto-Refill Settings -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header">
                        <h4 class="mb-0">Auto-Refill Configuration</h4>
                    </div>
                    <div class="card-body">
                        <p>Define the refill periods available to customers.</p>
                        <div class="form-group">
                            <label>Available Refill Frequencies (in days)</label>
                            <asp:TextBox ID="RefillFrequenciesBox" runat="server" CssClass="form-control" placeholder="e.g., 30,60,90"></asp:TextBox>
                            <small class="form-text text-muted">Enter a comma-separated list of numbers (e.g., 30,60,90,180).</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <asp:Button ID="SaveChangesButton" runat="server" Text="Save All Settings" CssClass="btn btn-primary btn-lg" OnClick="SaveChangesButton_Click" />
    </div>
</asp:Content>