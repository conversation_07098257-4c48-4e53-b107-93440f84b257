﻿/*
 =======================================================================
 MediEase: Database Schema Update for Product Images
 PURPOSE: Adds a new column to the Products table to store the image URL.
 =======================================================================
*/

-- Check if the column already exists before trying to add it
IF NOT EXISTS (SELECT * FROM sys.columns 
               WHERE Name = N'ImageUrl' 
               AND Object_ID = Object_ID(N'Products'))
BEGIN
    ALTER TABLE Products
    ADD ImageUrl NVARCHAR(500) NULL; -- Add the new column, allowing NULL values
    PRINT 'ImageUrl column added to Products table successfully.';
END
ELSE
BEGIN
    PRINT 'ImageUrl column already exists in Products table.';
END
GO
