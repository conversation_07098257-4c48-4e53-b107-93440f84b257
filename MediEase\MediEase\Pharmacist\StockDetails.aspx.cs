﻿/* FILE: Pharmacist/StockDetails.aspx.cs */
using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace MediEase.Pharmacist
{
    public partial class StockDetails : Page
    {
        private int productId;

        protected void Page_Load(object sender, EventArgs e)
        {
            if (Session["UserID"] == null || Session["RoleName"]?.ToString() != "Pharmacist") { Response.Redirect("~/Login.aspx"); return; }
            if (!int.TryParse(Request.QueryString["ProductID"], out productId)) { ShowErrorView(); return; }
            if (!IsPostBack) { LoadProductInfo(); BindBatchesGrid(); }
        }

        private void LoadProductInfo()
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "SELECT Name FROM Products WHERE ProductID = @ProductID";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@ProductID", productId);
                    try
                    {
                        con.Open();
                        object result = cmd.ExecuteScalar();
                        if (result != null) { ProductNameLabel.Text = result.ToString(); }
                        else { ShowErrorView(); }
                    }
                    catch (Exception ex) { System.Diagnostics.Debug.WriteLine("Error loading product name: " + ex.Message); ShowErrorView(); }
                }
            }
        }

        private void BindBatchesGrid()
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "SELECT InventoryID, BatchNumber, ExpiryDate, QuantityInStock FROM Inventory WHERE ProductID = @ProductID ORDER BY ExpiryDate";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@ProductID", productId);
                    try
                    {
                        con.Open();
                        SqlDataAdapter sda = new SqlDataAdapter(cmd);
                        DataTable dt = new DataTable();
                        sda.Fill(dt);
                        BatchesGrid.DataSource = dt;
                        BatchesGrid.DataBind();
                    }
                    catch (Exception ex) { System.Diagnostics.Debug.WriteLine("Error binding batches grid: " + ex.Message); }
                }
            }
        }

        protected void AddBatchButton_Click(object sender, EventArgs e)
        {
            string batchNumber = BatchNumberBox.Text.Trim();
            DateTime expiryDate = DateTime.Parse(ExpiryDateBox.Text);
            int quantity = int.Parse(QuantityBox.Text);
            int pharmacistId = Convert.ToInt32(Session["UserID"]);

            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "INSERT INTO Inventory (ProductID, BatchNumber, ExpiryDate, QuantityInStock, LastUpdatedByUserID, LastUpdatedAt) VALUES (@ProductID, @BatchNumber, @ExpiryDate, @Quantity, @UserID, GETDATE())";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@ProductID", productId);
                    cmd.Parameters.AddWithValue("@BatchNumber", batchNumber);
                    cmd.Parameters.AddWithValue("@ExpiryDate", expiryDate);
                    cmd.Parameters.AddWithValue("@Quantity", quantity);
                    cmd.Parameters.AddWithValue("@UserID", pharmacistId);
                    try
                    {
                        con.Open();
                        cmd.ExecuteNonQuery();
                        BatchNumberBox.Text = ""; ExpiryDateBox.Text = ""; QuantityBox.Text = "";
                    }
                    catch (Exception ex) { System.Diagnostics.Debug.WriteLine("Error adding new batch: " + ex.Message); }
                }
            }
            BindBatchesGrid();
        }

        protected void BatchesGrid_RowEditing(object sender, GridViewEditEventArgs e) { BatchesGrid.EditIndex = e.NewEditIndex; BindBatchesGrid(); }
        protected void BatchesGrid_RowCancelingEdit(object sender, GridViewCancelEditEventArgs e) { BatchesGrid.EditIndex = -1; BindBatchesGrid(); }

        protected void BatchesGrid_RowUpdating(object sender, GridViewUpdateEventArgs e)
        {
            int inventoryId = Convert.ToInt32(BatchesGrid.DataKeys[e.RowIndex].Value);
            TextBox txtBatchNumber = (TextBox)BatchesGrid.Rows[e.RowIndex].FindControl("txtBatchNumber");
            TextBox txtExpiryDate = (TextBox)BatchesGrid.Rows[e.RowIndex].FindControl("txtExpiryDate");
            TextBox txtQuantityInStock = (TextBox)BatchesGrid.Rows[e.RowIndex].FindControl("txtQuantityInStock");
            int pharmacistId = Convert.ToInt32(Session["UserID"]);

            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "UPDATE Inventory SET BatchNumber = @BatchNumber, ExpiryDate = @ExpiryDate, QuantityInStock = @Quantity, LastUpdatedByUserID = @UserID, LastUpdatedAt = GETDATE() WHERE InventoryID = @InventoryID";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@BatchNumber", txtBatchNumber.Text.Trim());
                    cmd.Parameters.AddWithValue("@ExpiryDate", DateTime.Parse(txtExpiryDate.Text));
                    cmd.Parameters.AddWithValue("@Quantity", int.Parse(txtQuantityInStock.Text));
                    cmd.Parameters.AddWithValue("@UserID", pharmacistId);
                    cmd.Parameters.AddWithValue("@InventoryID", inventoryId);
                    try { con.Open(); cmd.ExecuteNonQuery(); }
                    catch (Exception ex) { System.Diagnostics.Debug.WriteLine("Error updating batch: " + ex.Message); }
                }
            }
            BatchesGrid.EditIndex = -1;
            BindBatchesGrid();
        }

        protected void BatchesGrid_RowDeleting(object sender, GridViewDeleteEventArgs e)
        {
            int inventoryId = Convert.ToInt32(BatchesGrid.DataKeys[e.RowIndex].Value);
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "DELETE FROM Inventory WHERE InventoryID = @InventoryID";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@InventoryID", inventoryId);
                    try { con.Open(); cmd.ExecuteNonQuery(); }
                    catch (Exception ex) { System.Diagnostics.Debug.WriteLine("Error deleting batch: " + ex.Message); }
                }
            }
            BindBatchesGrid();
        }

        private void ShowErrorView() { MainView.Visible = false; ErrorView.Visible = true; }
    }
}