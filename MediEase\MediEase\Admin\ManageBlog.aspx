﻿<%--
 =======================================================================
 FILE: ManageBlog.aspx
 PURPOSE: Allows administrators to view a list of all blog posts and
          provides links to add, edit, or delete them.
 PLACE IN: The 'Admin' folder in your project root.
 =======================================================================
--%>
<%@ Page Title="Manage Blog" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="ManageBlog.aspx.cs" Inherits="MediEase.Admin.ManageBlog" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>Manage Blog Posts</h1>
            <a href="Dashboard.aspx" class="btn btn-outline-secondary">&larr; Back to Admin Dashboard</a>
        </div>
        
        <div class="mb-3">
             <a href="EditPost.aspx" class="btn btn-primary"><i class="fas fa-plus mr-2"></i>Write New Post</a>
        </div>

        <div class="card shadow-sm">
            <div class="card-header">
                <h4 class="mb-0">All Blog Posts</h4>
            </div>
            <div class="card-body">
                <asp:GridView ID="BlogPostsGrid" runat="server"
                    CssClass="table table-hover table-striped"
                    AutoGenerateColumns="False"
                    DataKeyNames="PostID"
                    GridLines="None"
                    AllowPaging="True"
                    OnPageIndexChanging="BlogPostsGrid_PageIndexChanging"
                    OnRowCommand="BlogPostsGrid_RowCommand"
                    PageSize="10">
                    <Columns>
                        <asp:BoundField DataField="PostID" HeaderText="ID" />
                        <asp:BoundField DataField="Title" HeaderText="Title" />
                        <asp:BoundField DataField="AuthorName" HeaderText="Author" />
                        <asp:BoundField DataField="PublishDate" HeaderText="Date" DataFormatString="{0:MMM dd, yyyy}" />
                        <asp:TemplateField HeaderText="Status">
                            <ItemTemplate>
                                <span class='badge <%# Convert.ToBoolean(Eval("IsPublished")) ? "badge-success" : "badge-secondary" %>'>
                                    <%# Convert.ToBoolean(Eval("IsPublished")) ? "Published" : "Draft" %>
                                </span>
                            </ItemTemplate>
                        </asp:TemplateField>
                        <asp:TemplateField HeaderText="Actions">
                            <ItemTemplate>
                                <asp:HyperLink ID="EditLink" runat="server"
                                    NavigateUrl='<%# "EditPost.aspx?PostID=" + Eval("PostID") %>'
                                    CssClass="btn btn-sm btn-info">Edit</asp:HyperLink>
                                <asp:LinkButton ID="DeleteButton" runat="server" 
                                    CommandName="DeletePost" 
                                    CommandArgument='<%# Eval("PostID") %>'
                                    Text="Delete" 
                                    CssClass="btn btn-sm btn-danger"
                                    OnClientClick="return confirm('Are you sure you want to permanently delete this post?');" />
                            </ItemTemplate>
                        </asp:TemplateField>
                    </Columns>
                    <EmptyDataTemplate>
                        <div class="alert alert-info text-center">
                            No blog posts have been created yet. Click "Write New Post" to get started.
                        </div>
                    </EmptyDataTemplate>
                </asp:GridView>
            </div>
        </div>
    </div>
</asp:Content>
