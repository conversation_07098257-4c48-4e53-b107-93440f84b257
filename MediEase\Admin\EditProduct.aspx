﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="EditProduct.aspx.cs" Inherits="Admin_EditProduct" %>

<!DOCTYPE html>
<html lang="en">
<head runat="server">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Product - MediEase Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <style>
        body { font-family: 'Inter', sans-serif; }
        .sidebar-link { display: flex; align-items: center; padding: 0.75rem 1rem; border-radius: 0.5rem; transition: background-color 0.2s, color 0.2s; }
        .sidebar-link:hover, .sidebar-link.active { background-color: #0d9488; color: white; }
        .sidebar-link i { width: 1.25rem; margin-right: 0.75rem; }
        .validation-error { color: #D32F2F; font-size: 0.875rem; margin-top: 0.25rem; }
    </style>
</head>
<body class="bg-gray-100">
    <form id="form1" runat="server">
        <div class="flex min-h-screen bg-gray-100">
            <!-- Sidebar -->
            <aside class="w-64 bg-teal-600 text-white flex-col p-4 hidden md:flex">
                <div class="flex items-center space-x-2 pb-4 border-b border-teal-500">
                    <i class="fas fa-pills text-3xl"></i>
                    <span class="text-2xl font-bold">MediEase</span>
                </div>
                <nav class="mt-6 flex-grow">
                    <a href="Dashboard.aspx" class="sidebar-link"><i class="fas fa-tachometer-alt fa-fw"></i><span>Dashboard</span></a>
                    <a href="UserManagement.aspx" class="sidebar-link"><i class="fas fa-users-cog fa-fw"></i><span>User Management</span></a>
                    <a href="ProductManagement.aspx" class="sidebar-link active"><i class="fas fa-capsules fa-fw"></i><span>Products</span></a>
                    <a href="Reports.aspx" class="sidebar-link"><i class="fas fa-chart-line fa-fw"></i><span>Reports</span></a>
                    <a href="SystemSettings.aspx" class="sidebar-link"><i class="fas fa-cogs fa-fw"></i><span>System Settings</span></a>
                    <a href="AuditLog.aspx" class="sidebar-link"><i class="fas fa-clipboard-list fa-fw"></i><span>Audit Log</span></a>
                </nav>
                <div class="pt-4 border-t border-teal-500">
                    <asp:LinkButton ID="btnLogout" runat="server" OnClick="btnLogout_Click" CssClass="sidebar-link w-full"><i class="fas fa-sign-out-alt fa-fw"></i><span>Logout</span></asp:LinkButton>
                </div>
            </aside>

            <!-- Main Content -->
            <main class="flex-1 flex flex-col">
                <header class="bg-white shadow-sm p-4">
                    <h1 class="text-2xl font-bold text-gray-800">
                        <asp:Literal ID="litPageTitle" runat="server">Add New Product</asp:Literal>
                    </h1>
                </header>

                <div class="flex-1 p-6 overflow-y-auto">
                    <div class="max-w-4xl mx-auto bg-white p-8 rounded-lg shadow">
                        <asp:HiddenField ID="hdnMedicineId" runat="server" Value="0" />
                        
                        <!-- Server Message -->
                        <asp:Literal ID="litMessage" runat="server"></asp:Literal>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Product Name -->
                            <div class="md:col-span-2">
                                <label class="block text-sm font-medium text-gray-700">Product Name *</label>
                                <asp:TextBox ID="txtName" runat="server" CssClass="mt-1 block w-full input-style"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvName" runat="server" ControlToValidate="txtName" ErrorMessage="Product name is required." CssClass="validation-error" Display="Dynamic"></asp:RequiredFieldValidator>
                            </div>
                             <!-- Generic Name -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Generic Name</label>
                                <asp:TextBox ID="txtGenericName" runat="server" CssClass="mt-1 block w-full input-style"></asp:TextBox>
                            </div>
                            <!-- Strength -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Strength (e.g., 500mg)</label>
                                <asp:TextBox ID="txtStrength" runat="server" CssClass="mt-1 block w-full input-style"></asp:TextBox>
                            </div>
                            <!-- Category -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Category *</label>
                                <asp:DropDownList ID="ddlCategory" runat="server" DataValueField="CategoryId" DataTextField="Name" CssClass="mt-1 block w-full input-style"></asp:DropDownList>
                                <asp:RequiredFieldValidator ID="rfvCategory" runat="server" ControlToValidate="ddlCategory" InitialValue="" ErrorMessage="Category is required." CssClass="validation-error" Display="Dynamic"></asp:RequiredFieldValidator>
                            </div>
                            <!-- Brand -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Brand</label>
                                <asp:DropDownList ID="ddlBrand" runat="server" DataValueField="BrandId" DataTextField="Name" CssClass="mt-1 block w-full input-style"></asp:DropDownList>
                            </div>

                            <!-- Price & Stock -->
                             <div class="md:col-span-2 grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Price *</label>
                                    <asp:TextBox ID="txtPrice" runat="server" TextMode="Number" step="0.01" CssClass="mt-1 block w-full input-style"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvPrice" runat="server" ControlToValidate="txtPrice" ErrorMessage="Price is required." CssClass="validation-error" Display="Dynamic"></asp:RequiredFieldValidator>
                                    <asp:RangeValidator ID="rvPrice" runat="server" ControlToValidate="txtPrice" Type="Currency" MinimumValue="0" MaximumValue="10000" ErrorMessage="Price must be between 0 and 10,000." CssClass="validation-error" Display="Dynamic"></asp:RangeValidator>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Stock Quantity *</label>
                                    <asp:TextBox ID="txtStockQuantity" runat="server" TextMode="Number" CssClass="mt-1 block w-full input-style"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvStock" runat="server" ControlToValidate="txtStockQuantity" ErrorMessage="Stock is required." CssClass="validation-error" Display="Dynamic"></asp:RequiredFieldValidator>
                                </div>
                             </div>

                             <!-- Description -->
                            <div class="md:col-span-2">
                                <label class="block text-sm font-medium text-gray-700">Description</label>
                                <asp:TextBox ID="txtDescription" runat="server" TextMode="MultiLine" Rows="4" CssClass="mt-1 block w-full input-style"></asp:TextBox>
                            </div>

                            <!-- Image Upload -->
                            <div class="md:col-span-2">
                                <label class="block text-sm font-medium text-gray-700">Product Image</label>
                                <asp:FileUpload ID="fileUploadImage" runat="server" CssClass="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-teal-50 file:text-teal-700 hover:file:bg-teal-100"/>
                                <asp:Image ID="imgCurrentImage" runat="server" Visible="false" CssClass="mt-4 h-32 w-32 object-cover rounded-lg" />
                            </div>

                             <!-- Checkboxes -->
                            <div class="md:col-span-2 space-y-4">
                                <div class="flex items-start">
                                    <div class="flex items-center h-5">
                                        <asp:CheckBox ID="chkIsActive" runat="server" Checked="true" CssClass="focus:ring-teal-500 h-4 w-4 text-teal-600 border-gray-300 rounded" />
                                    </div>
                                    <div class="ml-3 text-sm">
                                        <label for="chkIsActive" class="font-medium text-gray-700">Active</label>
                                        <p class="text-gray-500">Uncheck to hide this product from the store.</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <div class="flex items-center h-5">
                                        <asp:CheckBox ID="chkPrescriptionRequired" runat="server" CssClass="focus:ring-teal-500 h-4 w-4 text-teal-600 border-gray-300 rounded" />
                                    </div>
                                    <div class="ml-3 text-sm">
                                        <label for="chkPrescriptionRequired" class="font-medium text-gray-700">Prescription Required</label>
                                        <p class="text-gray-500">Check if this item requires a prescription for purchase.</p>
                                    </div>
                                </div>
                                 <div class="flex items-start">
                                    <div class="flex items-center h-5">
                                        <asp:CheckBox ID="chkIsFeatured" runat="server" CssClass="focus:ring-teal-500 h-4 w-4 text-teal-600 border-gray-300 rounded" />
                                    </div>
                                    <div class="ml-3 text-sm">
                                        <label for="chkIsFeatured" class="font-medium text-gray-700">Featured Product</label>
                                        <p class="text-gray-500">Check to display this product on the homepage.</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="mt-8 pt-6 border-t border-gray-200 flex justify-end space-x-3">
                            <asp:Button ID="btnCancel" runat="server" Text="Cancel" OnClick="btnCancel_Click" CausesValidation="false" CssClass="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500" />
                            <asp:Button ID="btnSave" runat="server" Text="Save Product" OnClick="btnSave_Click" CssClass="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500" />
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </form>
</body>
</html>
