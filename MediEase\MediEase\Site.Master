﻿<%--
 =======================================================================
 FILE: Site.Master (Final Version with Single-Layer Nav)
 =======================================================================
--%>
<%@ Master Language="C#" AutoEventWireup="true" CodeBehind="Site.Master.cs" Inherits="MediEase.SiteMaster" %>

<!DOCTYPE html>
<html lang="en">
<head runat="server">
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><%: Page.Title %> - MediEase Pharmacy</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>💊</text></svg>">
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />
    <link href="Content/Site.css" rel="stylesheet" />
    <style>
        body.dark-mode { background-color: #121212; color: #e0e0e0; }
        .dark-mode .card, .dark-mode .list-group-item, .dark-mode .modal-content, .dark-mode .table, .dark-mode .dropdown-menu { background-color: #1e1e1e; color: #e0e0e0; border-color: #444; }
        .dark-mode .navbar, .dark-mode footer { background-color: #1e1e1e !important; }
        .dark-mode h1, .dark-mode h2, .dark-mode h4, .dark-mode h5, .dark-mode .dropdown-item, .dark-mode a:not(.btn) { color: #fff; }
        .dark-mode a.btn-primary { color: #fff !important; }
        .dark-mode .nav-link.btn-outline-light { color: #fff !important; border-color: #fff; }
        .dark-mode .text-muted { color: #aaa !important; }
        .dark-mode .form-control { background-color: #333; color: #fff; border-color: #555; }
        .dark-mode .font-size-menu, .dark-mode .notification-menu { background-color: #333; }
        .dark-mode .dropdown-divider { border-top-color: #444; }
        .font-size-menu { padding: 10px; min-width: auto; }
        body.tts-active p:hover, body.tts-active h1:hover, body.tts-active h2:hover, body.tts-active h3:hover, body.tts-active h4:hover, body.tts-active h5:hover, body.tts-active h6:hover { background-color: rgba(0, 123, 255, 0.1); cursor: pointer; }
        
        .chat-widget-button { position: fixed; bottom: 20px; right: 20px; width: 55px; height: 55px; background-color: #007bff; color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 22px; cursor: pointer; box-shadow: 0 4px 8px rgba(0,0,0,0.2); z-index: 1000; }
        .chat-widget { display: none; position: fixed; bottom: 90px; right: 20px; width: 350px; height: 500px; background-color: #fff; border: 1px solid #ccc; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.3); z-index: 1001; flex-direction: column; }
        .chat-widget-header { background-color: #007bff; color: white; padding: 10px; display: flex; justify-content: space-between; align-items: center; border-top-left-radius: 8px; border-top-right-radius: 8px; cursor: move; }
        .chat-widget iframe { width: 100%; height: calc(100% - 40px); border: none; }
        .chat-widget-close { cursor: pointer; font-size: 20px; }
        
        .notification-badge { position: absolute; top: 10px; right: 5px; font-size: 0.6em; }
        .notification-menu { min-width: 350px; padding: 0; }
        .notification-item a { white-space: normal; padding: 10px 15px; display: block; color: #333; }
        .dark-mode .notification-item a { color: #fff; }
        .notification-item a:hover { background-color: #f8f9fa; text-decoration:none; }
        .dark-mode .notification-item a:hover { background-color: #333; }
        .notification-footer { text-align: center; font-weight: bold; }
    </style>
</head>
<body id="pageBody" runat="server">
    <form id="form1" runat="server">
        <header>
            <nav class="navbar navbar-expand-lg navbar-dark bg-dark sticky-top">
                <div class="container">
                    <asp:HyperLink NavigateUrl="~/" CssClass="navbar-brand" runat="server">MediEase</asp:HyperLink>
                    <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#mainNav"><span class="navbar-toggler-icon"></span></button>
                    <div class="collapse navbar-collapse" id="mainNav">
                         <ul class="navbar-nav mr-auto">
                            <li class="nav-item"><asp:HyperLink NavigateUrl="~/" CssClass="nav-link" runat="server">Home</asp:HyperLink></li>
                            <li class="nav-item"><asp:HyperLink NavigateUrl="~/Shop.aspx" CssClass="nav-link" runat="server">Shop</asp:HyperLink></li>
                            <li class="nav-item"><asp:HyperLink NavigateUrl="~/Blog.aspx" CssClass="nav-link" runat="server">Blog</asp:HyperLink></li>
                            <li class="nav-item"><asp:HyperLink NavigateUrl="~/FAQ.aspx" CssClass="nav-link" runat="server">FAQ</asp:HyperLink></li>
                            <li class="nav-item"><asp:HyperLink NavigateUrl="~/Contact.aspx" CssClass="nav-link" runat="server">Contact & Location</asp:HyperLink></li>
                        </ul>
                        <ul class="navbar-nav align-items-center">
                            <asp:PlaceHolder runat="server" ID="GuestView">
                                <li class="nav-item"><asp:HyperLink NavigateUrl="~/Register.aspx" CssClass="nav-link" runat="server">Register</asp:HyperLink></li>
                                <li class="nav-item ml-2"><asp:HyperLink NavigateUrl="~/Login.aspx" CssClass="btn btn-primary" runat="server">Login</asp:HyperLink></li>
                            </asp:PlaceHolder>
                            <asp:PlaceHolder runat="server" ID="UserView" Visible="false">
                                 <li class="nav-item dropdown">
                                    <a class="nav-link dropdown-toggle" href="#" id="notificationDropdown" role="button" data-toggle="dropdown"><i class="fas fa-bell fa-lg"></i><asp:Label ID="NotificationBadge" runat="server" CssClass="badge badge-danger notification-badge" Visible="false"></asp:Label></a>
                                    <div class="dropdown-menu dropdown-menu-right notification-menu" aria-labelledby="notificationDropdown">
                                        <div class="list-group list-group-flush">
                                            <div class="list-group-item d-flex justify-content-between align-items-center"><h6 class="mb-0">Notifications</h6><asp:LinkButton ID="MarkAllAsReadButton" runat="server" OnClick="MarkAllAsReadButton_Click" CssClass="btn btn-sm btn-link p-0">Mark all as read</asp:LinkButton></div>
                                            <asp:Repeater ID="NotificationRepeater" runat="server" OnItemCommand="NotificationRepeater_ItemCommand">
                                                <ItemTemplate><div class="list-group-item notification-item p-0"><asp:LinkButton runat="server" CommandName="View" CommandArgument='<%# Eval("NotificationID") + "|" + Eval("LinkUrl") %>'><small><%# Eval("Message") %></small><br/><small class="text-muted"><%# Eval("CreatedDate", "{0:g}") %></small></asp:LinkButton></div></ItemTemplate>
                                            </asp:Repeater>
                                            <asp:PlaceHolder ID="NoNotificationsPlaceholder" runat="server" Visible="true"><div class="text-center p-3 text-muted">No new notifications.</div></asp:PlaceHolder>
                                            <a href='<%# ResolveUrl("~/Customer/Notifications.aspx") %>' class="list-group-item list-group-item-action notification-footer">View All Notifications</a>
                                        </div>
                                    </div>
                                </li>
                                 <li class="nav-item ml-2"><asp:HyperLink NavigateUrl="~/Cart.aspx" CssClass="nav-link" title="Shopping Cart"><i class="fas fa-shopping-cart"></i> Cart <span id="CartCount" runat="server" class="badge badge-primary">0</span></asp:HyperLink></li>
                                <li class="nav-item dropdown ml-2">
                                    <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-toggle="dropdown"><i class="fas fa-user-circle fa-lg"></i><asp:Label ID="ProfileNameLabel" runat="server" Text="Profile" CssClass="d-none d-lg-inline ml-1"></asp:Label></a>
                                    <div class="dropdown-menu dropdown-menu-right" aria-labelledby="navbarDropdown">
                                        <asp:HyperLink ID="DashboardLink" CssClass="dropdown-item" runat="server">My Dashboard</asp:HyperLink>
                                        <asp:HyperLink ID="ProfileLink" CssClass="dropdown-item" runat="server">My Profile</asp:HyperLink>
                                        <div class="dropdown-divider"></div>
                                        <asp:LinkButton ID="LogoutButton" runat="server" OnClick="LogoutButton_Click" CssClass="dropdown-item"><i class="fas fa-sign-out-alt"></i> Logout</asp:LinkButton>
                                    </div>
                                </li>
                            </asp:PlaceHolder>
                             <li class="nav-item border-left ml-3 pl-3"><asp:LinkButton ID="ToggleDarkMode" runat="server" OnClick="ToggleDarkMode_Click" CssClass="nav-link" ToolTip="Toggle Night Mode"><i id="darkModeIcon" runat="server" class="fas fa-moon"></i></asp:LinkButton></li>
                             <li class="nav-item"><asp:LinkButton ID="ToggleTTS" runat="server" OnClick="ToggleTTS_Click" CssClass="nav-link" ToolTip="Toggle Click to Read"><i id="ttsIcon" runat="server" class="fas fa-volume-mute"></i></asp:LinkButton></li>
                             <li class="nav-item dropdown">
                                 <a class="nav-link dropdown-toggle" href="#" id="fontSizeDropdown" role="button" data-toggle="dropdown"><i class="fas fa-font"></i></a>
                                 <div class="dropdown-menu dropdown-menu-right font-size-menu" aria-labelledby="fontSizeDropdown">
                                     <asp:LinkButton ID="DecreaseFont" runat="server" OnClick="ChangeFontSize_Click" CommandArgument="decrease" CssClass="btn btn-secondary btn-sm">- A</asp:LinkButton>
                                     <asp:LinkButton ID="IncreaseFont" runat="server" OnClick="ChangeFontSize_Click" CommandArgument="increase" CssClass="btn btn-secondary btn-sm">+ A</asp:LinkButton>
                                 </div>
                            </li>
                        </ul>
                    </div>
                </div>
            </nav>
        </header>
        <div class="container body-content mt-4 mb-4"><asp:ContentPlaceHolder ID="MainContent" runat="server" /></div>
        <footer class="footer bg-dark text-white text-center py-3"><p>© <%: DateTime.Now.Year %> - MediEase</p></footer>
    </form>
    <div class="chat-widget" id="chatWidget">
        <div class="chat-widget-header"><h5>AI Assistant</h5><span class="chat-widget-close" onclick="toggleChatWidget(false)">×</span></div>
        <iframe id="chatIframe" data-src="/Chatbot.aspx" title="Chatbot"></iframe>
    </div>
    <div class="chat-widget-button" onclick="toggleChatWidget(true)"><i class="fas fa-comments"></i></div>
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.bundle.min.js"></script>
    <script type="text/javascript">
        function toggleChatWidget(open) {
            const widget = document.getElementById('chatWidget');
            const iframe = document.getElementById('chatIframe');
            if (open) { if (!iframe.src) { iframe.src = iframe.getAttribute('data-src'); } widget.style.display = 'flex'; }
            else { widget.style.display = 'none'; }
        }
        document.addEventListener('DOMContentLoaded', function () {
            if (document.body.classList.contains('tts-active')) {
                document.addEventListener('click', function (e) {
                    if (document.body.classList.contains('tts-active')) {
                        if (e.target.closest('.navbar-nav') || e.target.closest('button') || e.target.closest('.btn') || e.target.closest('.chat-widget-button')) { return; }
                        const readableTarget = e.target.closest('p, h1, h2, h3, h4, h5, h6, li, td, th');
                        if (readableTarget) {
                            e.preventDefault();
                            const textToSpeak = readableTarget.innerText;
                            if ('speechSynthesis' in window && textToSpeak) { const utterance = new SpeechSynthesisUtterance(textToSpeak); window.speechSynthesis.cancel(); window.speechSynthesis.speak(utterance); }
                        }
                    }
                });
            }
        });
    </script>
</body>
</html>