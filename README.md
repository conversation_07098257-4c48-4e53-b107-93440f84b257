# MediEase - Smart Pharmacy Management System

## Overview
MediEase is a comprehensive, modern, and responsive pharmacy management system built with ASP.NET Web Forms, featuring AI-powered medicine recommendations and real-time chatbot support.

## Features

### 🏠 Common Features (All Users)
- **Responsive Design**: Mobile-friendly interface that works on all devices
- **AI-Powered Chatbot**: Real-time assistance using OpenRouter API
- **Guest Search**: Browse medicines without registration
- **Secure Authentication**: Role-based access control
- **Modern UI**: Bootstrap 5 with custom styling

### 👤 Customer Features
- **User Registration & Login**: Secure account management
- **Medicine Catalog**: Browse and search medicines with filters
- **Prescription Upload**: Upload prescription images/PDFs
- **Shopping Cart**: Add medicines to cart and place orders
- **AI Recommendations**: Get personalized medicine suggestions
- **Order Tracking**: Track order status and delivery
- **Loyalty Points**: Earn and redeem points
- **Health Reminders**: Medication adherence tracking
- **Family Profiles**: Manage multiple family members

### 💊 Pharmacist Features
- **Dashboard**: Overview of orders and inventory
- **Order Management**: Process and validate orders
- **Prescription Verification**: Review and approve prescriptions
- **Inventory Management**: Add, update, and manage medicine stock
- **Price Management**: Update prices and create offers
- **Customer Support**: Respond to customer queries
- **Reports**: Sales and inventory reports

### 🔐 Admin Features
- **User Management**: Manage customers and pharmacists
- **Medicine Management**: Add, edit, and categorize medicines
- **System Analytics**: Comprehensive reports and analytics
- **Chatbot Oversight**: Monitor and configure AI responses
- **Security Management**: Audit logs and security policies
- **Bulk Operations**: Import/export data via CSV/Excel

## Technology Stack

| Component | Technology |
|-----------|------------|
| **Frontend** | ASP.NET Web Forms, Bootstrap 5, jQuery |
| **Backend** | C# (.NET Framework 4.8) |
| **Database** | Microsoft SQL Server (LocalDB) |
| **AI Engine** | OpenRouter API (DeepSeek R1 Model) |
| **Authentication** | Forms Authentication with BCrypt |
| **ORM** | Entity Framework 6 |
| **Security** | HTTPS, Input Validation, SQL Injection Protection |

## Project Structure

```
MediEase/
├── Content/                 # CSS files
├── Scripts/                 # JavaScript files
├── Images/                  # Image assets
├── Models/                  # Data models
├── DAL/                     # Data Access Layer
├── BLL/                     # Business Logic Layer
├── Utilities/               # Helper classes
├── Admin/                   # Admin pages
├── Customer/                # Customer pages
├── Pharmacist/              # Pharmacist pages
├── App_Data/                # Database files
└── Web.config               # Configuration
```

## Setup Instructions

### Prerequisites
- Visual Studio 2019/2022
- .NET Framework 4.8
- SQL Server LocalDB
- IIS Express

### Installation Steps

1. **Clone the Repository**
   ```bash
   git clone [repository-url]
   cd MediEase
   ```

2. **Open in Visual Studio**
   - Open `MediEase.sln` in Visual Studio
   - Restore NuGet packages

3. **Configure Database**
   - The application uses LocalDB with automatic database creation
   - Connection string is configured in `Web.config`
   - Database will be created automatically on first run

4. **Configure AI API**
   - Update `OpenRouterApiKey` in `Web.config` with your API key
   - The current key is for demonstration purposes

5. **Build and Run**
   - Build the solution (Ctrl+Shift+B)
   - Run the application (F5)

### Getting Started

After setting up the application, you can:
- Register a new account using the registration page
- Create admin accounts through the database or registration system
- Configure user roles through the admin panel

## Key Features Implementation

### AI Integration
- **OpenRouter API**: Uses DeepSeek R1 model for intelligent responses
- **Medicine Recommendations**: AI-powered suggestions based on symptoms
- **Chatbot Support**: Real-time customer assistance
- **Prescription Analysis**: AI-assisted prescription validation

### Security Features
- **Password Hashing**: BCrypt for secure password storage
- **Input Validation**: Server and client-side validation
- **SQL Injection Protection**: Parameterized queries
- **HTTPS Enforcement**: Secure data transmission
- **Session Management**: Secure session handling
- **Role-based Authorization**: Granular access control

### Database Design
- **Normalized Schema**: Efficient data structure
- **Entity Framework**: Code-first approach
- **Audit Trail**: Track all data changes
- **Soft Deletes**: Maintain data integrity
- **Indexing**: Optimized for performance

## API Endpoints

### Chat API
- `POST /api/Chat/Send` - Send message to AI chatbot
- `GET /api/Chat/History` - Get chat history

### Cart API
- `POST /api/Cart/Add` - Add item to cart
- `GET /api/Cart/Items` - Get cart items
- `DELETE /api/Cart/Remove` - Remove item from cart

### Upload API
- `POST /api/Upload/Prescription` - Upload prescription file
- `POST /api/Upload/ProfilePicture` - Upload profile picture

## Configuration

### Web.config Settings
```xml
<appSettings>
  <!-- AI Configuration -->
  <add key="OpenRouterApiKey" value="your-api-key" />
  <add key="OpenRouterApiUrl" value="https://openrouter.ai/api/v1/chat/completions" />
  <add key="OpenRouterModel" value="deepseek/deepseek-r1-0528-qwen3-8b:free" />
  
  <!-- Application Settings -->
  <add key="MaxFileUploadSize" value="5242880" />
  <add key="SessionTimeout" value="30" />
</appSettings>
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Email: <EMAIL>
- Documentation: [Project Wiki]
- Issues: [GitHub Issues]

## Roadmap

### Phase 1 (Current)
- ✅ Core pharmacy management features
- ✅ AI chatbot integration
- ✅ User authentication and authorization
- ✅ Responsive design

### Phase 2 (Planned)
- 📱 Mobile app development
- 🔔 Push notifications
- 📊 Advanced analytics
- 🌐 Multi-language support
- 💳 Payment gateway integration

### Phase 3 (Future)
- 🤖 Advanced AI features
- 📱 Telemedicine integration
- 🏥 Hospital system integration
- 📈 Predictive analytics
- 🌍 Multi-tenant architecture

---

**MediEase** - Revolutionizing pharmacy management with AI-powered intelligence.
