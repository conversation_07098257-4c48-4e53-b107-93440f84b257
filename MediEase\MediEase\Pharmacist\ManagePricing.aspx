﻿<%--
 =======================================================================
 FILE: ManagePricing.aspx (Final Version)
 =======================================================================
--%>
<%@ Page Title="Manage Product Pricing" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="ManagePricing.aspx.cs" Inherits="MediEase.Pharmacist.ManagePricing" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>Manage Product Pricing</h1>
            <a href="Dashboard.aspx" class="btn btn-outline-secondary">&larr; Back to Dashboard</a>
        </div>
        
        <asp:PlaceHolder ID="MessagePlaceholder" runat="server" Visible="false">
            <div class="alert alert-success">
                Price updated successfully!
            </div>
        </asp:PlaceHolder>

        <div class="card shadow-sm">
            <div class="card-header">
                <h4 class="mb-0">Product List</h4>
            </div>
            <div class="card-body">
                <asp:GridView ID="PricingGrid" runat="server"
                    CssClass="table table-hover table-striped"
                    AutoGenerateColumns="False"
                    DataKeyNames="ProductID"
                    GridLines="None"
                    AllowPaging="True"
                    OnPageIndexChanging="PricingGrid_PageIndexChanging"
                    OnRowEditing="PricingGrid_RowEditing"
                    OnRowUpdating="PricingGrid_RowUpdating"
                    OnRowCancelingEdit="PricingGrid_RowCancelingEdit"
                    PageSize="15">
                    <Columns>
                        <asp:BoundField DataField="ProductID" HeaderText="ID" ReadOnly="True" />
                        <asp:BoundField DataField="Name" HeaderText="Product Name" ReadOnly="True" />
                        <asp:BoundField DataField="CategoryName" HeaderText="Category" ReadOnly="True" />
                        <asp:BoundField DataField="BrandName" HeaderText="Brand" ReadOnly="True" />
                        <asp:TemplateField HeaderText="Current Price">
                            <ItemTemplate>
                                <%# Eval("Price", "{0:C}") %>
                            </ItemTemplate>
                            <EditItemTemplate>
                                <asp:TextBox ID="txtPrice" runat="server" Text='<%# Bind("Price", "{0:F2}") %>' CssClass="form-control" TextMode="Number" step="0.01"></asp:TextBox>
                                <asp:RequiredFieldValidator runat="server" ControlToValidate="txtPrice" ErrorMessage="Price is required." CssClass="text-danger" Display="Dynamic" />
                                <asp:CompareValidator runat="server" ControlToValidate="txtPrice" Type="Currency" Operator="DataTypeCheck" ErrorMessage="Invalid price format." CssClass="text-danger" Display="Dynamic" />
                            </EditItemTemplate>
                        </asp:TemplateField>
                        <asp:CommandField ShowEditButton="true" EditText="Update Price" ControlStyle-CssClass="btn btn-sm btn-info" />
                    </Columns>
                    <EmptyDataTemplate>
                        <div class="alert alert-info text-center">
                            There are no products in the system.
                        </div>
                    </EmptyDataTemplate>
                </asp:GridView>
            </div>
        </div>
    </div>
</asp:Content>