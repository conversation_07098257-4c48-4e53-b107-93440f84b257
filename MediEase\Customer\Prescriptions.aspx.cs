﻿using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.IO;

public partial class Customer_Prescriptions : System.Web.UI.Page
{
    protected void Page_Load(object sender, EventArgs e)
    {
        // --- SECURITY CHECK ---
        if (Session["UserId"] == null || Session["UserRole"]?.ToString() != "Customer")
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        if (!IsPostBack)
        {
            BindPrescriptionsGrid();
        }
    }

    private void BindPrescriptionsGrid()
    {
        int userId = (int)Session["UserId"];
        string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
        using (SqlConnection con = new SqlConnection(connectionString))
        {
            string query = "SELECT PrescriptionId, PrescriptionNumber, DoctorName, PrescriptionDate, Status, FilePath FROM Prescriptions WHERE UserId = @UserId ORDER BY PrescriptionDate DESC";
            using (SqlDataAdapter sda = new SqlDataAdapter(query, con))
            {
                sda.SelectCommand.Parameters.AddWithValue("@UserId", userId);
                DataTable dt = new DataTable();
                sda.Fill(dt);
                gvPrescriptions.DataSource = dt;
                gvPrescriptions.DataBind();
            }
        }
    }

    protected void btnUpload_Click(object sender, EventArgs e)
    {
        if (!Page.IsValid) return;

        if (fileUploadPrescription.HasFile)
        {
            try
            {
                string filename = Path.GetFileName(fileUploadPrescription.FileName);
                string fileExtension = Path.GetExtension(filename).ToLower();
                string[] allowedExtensions = { ".jpg", ".jpeg", ".png", ".pdf" };

                if (Array.IndexOf(allowedExtensions, fileExtension) == -1)
                {
                    ShowMessage("Invalid file type. Please upload a JPG, PNG, or PDF file.", "error");
                    return;
                }

                string uniqueFileName = Guid.NewGuid().ToString() + fileExtension;
                string folderPath = Server.MapPath("~/Uploads/Prescriptions/");
                if (!Directory.Exists(folderPath))
                {
                    Directory.CreateDirectory(folderPath);
                }
                string savedFilePath = folderPath + uniqueFileName;
                fileUploadPrescription.SaveAs(savedFilePath);

                // Now save the record to the database
                SavePrescriptionRecord("Uploads/Prescriptions/" + uniqueFileName);
            }
            catch (Exception ex)
            {
                ShowMessage($"File upload error: {ex.Message}", "error");
            }
        }
    }

    private void SavePrescriptionRecord(string filePath)
    {
        int userId = (int)Session["UserId"];
        string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;

        // Generate a unique prescription number
        string prescriptionNumber = $"RX-{DateTime.Now.Ticks.ToString().Substring(8)}";

        using (SqlConnection con = new SqlConnection(connectionString))
        {
            string query = @"INSERT INTO Prescriptions 
                             (UserId, PrescriptionNumber, DoctorName, PrescriptionDate, FilePath, FileType, FileSize, Status, CreatedDate)
                             VALUES 
                             (@UserId, @PrescriptionNumber, @DoctorName, @PrescriptionDate, @FilePath, @FileType, @FileSize, 'Pending', GETDATE())";

            using (SqlCommand cmd = new SqlCommand(query, con))
            {
                cmd.Parameters.AddWithValue("@UserId", userId);
                cmd.Parameters.AddWithValue("@PrescriptionNumber", prescriptionNumber);
                cmd.Parameters.AddWithValue("@DoctorName", txtDoctorName.Text.Trim());
                cmd.Parameters.AddWithValue("@PrescriptionDate", Convert.ToDateTime(txtPrescriptionDate.Text));
                cmd.Parameters.AddWithValue("@FilePath", filePath);
                cmd.Parameters.AddWithValue("@FileType", fileUploadPrescription.PostedFile.ContentType);
                cmd.Parameters.AddWithValue("@FileSize", fileUploadPrescription.PostedFile.ContentLength);

                try
                {
                    con.Open();
                    cmd.ExecuteNonQuery();
                    ShowMessage("Prescription uploaded successfully. It is now pending verification.", "success");
                    BindPrescriptionsGrid(); // Refresh the list
                    // Clear form fields
                    txtDoctorName.Text = "";
                    txtPrescriptionDate.Text = "";
                }
                catch (Exception ex)
                {
                    ShowMessage($"Database error: {ex.Message}", "error");
                }
            }
        }
    }

    protected void gvPrescriptions_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        gvPrescriptions.PageIndex = e.NewPageIndex;
        BindPrescriptionsGrid();
    }

    protected void btnLogout_Click(object sender, EventArgs e)
    {
        Session.Clear();
        Session.Abandon();
        Response.Redirect("~/Login.aspx");
    }

    public string GetStatusClass(string status)
    {
        switch (status.ToLower())
        {
            case "pending": return "bg-yellow-100 text-yellow-800";
            case "verified": return "bg-green-100 text-green-800";
            case "rejected": return "bg-red-100 text-red-800";
            default: return "bg-gray-100 text-gray-800";
        }
    }

    private void ShowMessage(string message, string type)
    {
        string cssClass = (type == "success")
            ? "bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg relative mb-4"
            : "bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg relative mb-4";

        litMessage.Text = $"<div class='{cssClass}' role='alert'>{message}</div>";
    }
}
