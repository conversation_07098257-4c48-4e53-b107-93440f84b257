﻿<%--
 =======================================================================
 FILE: Reminders.aspx (Final Version)
 =======================================================================
--%>
<%@ Page Title="Medication Reminders" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="Reminders.aspx.cs" Inherits="MediEase.Customer.Reminders" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>Medication Reminders</h1>
            <a href="Dashboard.aspx" class="btn btn-outline-secondary">&larr; Back to Dashboard</a>
        </div>
        <div class="row">
            <div class="col-lg-5">
                <div class="card shadow-sm">
                    <div class="card-header"><h4 class="mb-0">Set a New Reminder</h4></div>
                    <div class="card-body">
                         <asp:PlaceHolder ID="MessagePlaceholder" runat="server" Visible="false">
                            <div class="alert alert-success">Reminder and confirmation email sent successfully!</div>
                        </asp:PlaceHolder>
                        <div class="form-group"><label>Medication</label><asp:DropDownList ID="ProductDropDown" runat="server" CssClass="form-control"></asp:DropDownList></div>
                        <div class="form-group"><label>Reminder Time</label><asp:TextBox ID="TimeBox" runat="server" CssClass="form-control" TextMode="Time"></asp:TextBox></div>
                        <div class="form-group"><label>Frequency</label><asp:DropDownList ID="FrequencyDropDown" runat="server" CssClass="form-control"><asp:ListItem>Daily</asp:ListItem><asp:ListItem>Twice a day</asp:ListItem><asp:ListItem>Three times a day</asp:ListItem><asp:ListItem>Before bed</asp:ListItem></asp:DropDownList></div>
                        <asp:Button ID="SetReminderButton" runat="server" Text="Set Reminder" CssClass="btn btn-primary" OnClick="SetReminderButton_Click" />
                    </div>
                </div>
            </div>
            <div class="col-lg-7">
                <h4>Your Active Reminders</h4><hr />
                <asp:GridView ID="RemindersGrid" runat="server" CssClass="table table-hover table-striped" AutoGenerateColumns="False" DataKeyNames="ReminderID" GridLines="None" OnRowDeleting="RemindersGrid_RowDeleting" EmptyDataText="You have no active reminders.">
                    <Columns>
                        <asp:BoundField DataField="Name" HeaderText="Medication" />
                        <asp:BoundField DataField="ReminderTime" HeaderText="Time" DataFormatString="{0:hh:mm tt}" />
                        <asp:BoundField DataField="Frequency" HeaderText="Frequency" />
                        <asp:TemplateField><ItemTemplate><asp:LinkButton ID="DeleteButton" runat="server" CommandName="Delete" CssClass="btn btn-sm btn-danger" OnClientClick="return confirm('Are you sure you want to cancel this reminder?');">Cancel</asp:LinkButton></ItemTemplate></asp:TemplateField>
                    </Columns>
                </asp:GridView>
            </div>
        </div>
    </div>
</asp:Content>