//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated. 
// </auto-generated>
//------------------------------------------------------------------------------

namespace MediEase.Admin
{
    public partial class UserManagement
    {
        /// <summary>
        /// lblTotalUsers control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblTotalUsers;

        /// <summary>
        /// lblActiveCustomers control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblActiveCustomers;

        /// <summary>
        /// lblActivePharmacists control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblActivePharmacists;

        /// <summary>
        /// lblActiveAdmins control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblActiveAdmins;

        /// <summary>
        /// ddlRoleFilter control.
        /// </summary>
        protected global::System.Web.UI.WebControls.DropDownList ddlRoleFilter;

        /// <summary>
        /// ddlStatusFilter control.
        /// </summary>
        protected global::System.Web.UI.WebControls.DropDownList ddlStatusFilter;

        /// <summary>
        /// ddlEmailVerified control.
        /// </summary>
        protected global::System.Web.UI.WebControls.DropDownList ddlEmailVerified;

        /// <summary>
        /// txtSearch control.
        /// </summary>
        protected global::System.Web.UI.WebControls.TextBox txtSearch;

        /// <summary>
        /// btnSearch control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Button btnSearch;

        /// <summary>
        /// btnRefresh control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Button btnRefresh;

        /// <summary>
        /// gvUsers control.
        /// </summary>
        protected global::System.Web.UI.WebControls.GridView gvUsers;

        /// <summary>
        /// rptUsersCard control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Repeater rptUsersCard;

        /// <summary>
        /// pnlNoUsers control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Panel pnlNoUsers;
    }
}
