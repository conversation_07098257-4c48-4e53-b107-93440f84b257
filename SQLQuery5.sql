﻿ALTER TABLE Products
DROP COLUMN ImageUrl;
IF NOT EXISTS (SELECT * FROM sys.columns 
               WHERE Name = N'ImageUrl' 
               AND Object_ID = Object_ID(N'Products'))
BEGIN
    ALTER TABLE Products
    ADD ImageUrl NVARCHAR(500) NULL; -- Add the new column, allowing NULL values
    PRINT 'ImageUrl column added to Products table successfully.';
END
ELSE
BEGIN
    PRINT 'ImageUrl column already exists in Products table.';
END
GO