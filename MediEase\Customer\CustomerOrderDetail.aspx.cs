﻿using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web.UI;

public partial class Customer_CustomerOrderDetail : System.Web.UI.Page
{
    private int OrderId = 0;

    protected void Page_Load(object sender, EventArgs e)
    {
        // --- SECURITY CHECK ---
        if (Session["UserId"] == null || Session["UserRole"]?.ToString() != "Customer")
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        if (!int.TryParse(Request.QueryString["id"], out OrderId))
        {
            Response.Redirect("OrderHistory.aspx");
            return;
        }

        if (!IsPostBack)
        {
            LoadOrderDetails();
        }
    }

    private void LoadOrderDetails()
    {
        int customerId = (int)Session["UserId"];
        string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
        using (SqlConnection con = new SqlConnection(connectionString))
        {
            // --- Primary Security Check: Ensure the order belongs to the logged-in customer ---
            string orderQuery = "SELECT * FROM Orders WHERE OrderId = @OrderId AND CustomerId = @CustomerId";
            using (SqlCommand cmd = new SqlCommand(orderQuery, con))
            {
                cmd.Parameters.AddWithValue("@OrderId", OrderId);
                cmd.Parameters.AddWithValue("@CustomerId", customerId);

                try
                {
                    con.Open();
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            // Populate header and summary
                            litOrderNumber.Text = reader["OrderNumber"].ToString();
                            litOrderDate.Text = Convert.ToDateTime(reader["OrderDate"]).ToString("MMMM dd, yyyy");
                            litTotalAmount.Text = Convert.ToDecimal(reader["TotalAmount"]).ToString("C");
                            litStatus.Text = $"<span class='px-2 inline-flex text-xs leading-5 font-semibold rounded-full {GetStatusClass(reader["Status"].ToString())}'>{reader["Status"]}</span>";

                            // Populate shipping and summary details
                            litShippingAddress.Text = $"{reader["ShippingAddress"]}<br/>{reader["ShippingCity"]}, {reader["ShippingState"]} {reader["ShippingPostalCode"]}<br/>{reader["ShippingCountry"]}";
                            litSubtotal.Text = Convert.ToDecimal(reader["Subtotal"]).ToString("C");
                            litShippingCost.Text = Convert.ToDecimal(reader["ShippingCost"]).ToString("C");
                            litTax.Text = Convert.ToDecimal(reader["TaxAmount"]).ToString("C");
                            litGrandTotal.Text = Convert.ToDecimal(reader["TotalAmount"]).ToString("C");
                        }
                        else
                        {
                            // If reader is empty, the order does not exist or does not belong to this customer
                            Response.Redirect("OrderHistory.aspx");
                        }
                    }

                    // --- Load Order Items ---
                    string itemsQuery = @"
                        SELECT oi.Quantity, oi.UnitPrice, oi.TotalPrice, m.Name, m.ImageUrl
                        FROM OrderItems oi
                        INNER JOIN Medicines m ON oi.MedicineId = m.MedicineId
                        WHERE oi.OrderId = @OrderId";

                    using (SqlDataAdapter sda = new SqlDataAdapter(itemsQuery, con))
                    {
                        sda.SelectCommand.Parameters.AddWithValue("@OrderId", OrderId);
                        DataTable dt = new DataTable();
                        sda.Fill(dt);
                        rptOrderItems.DataSource = dt;
                        rptOrderItems.DataBind();
                    }
                }
                catch (Exception ex)
                {
                    litMessage.Text = "<div class='bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg relative mb-4' role='alert'>Error loading order details. Please try again.</div>";
                    System.Diagnostics.Debug.WriteLine("Customer Order Detail Error: " + ex.Message);
                }
            }
        }
    }

    protected void btnLogout_Click(object sender, EventArgs e)
    {
        Session.Clear();
        Session.Abandon();
        Response.Redirect("~/Login.aspx");
    }

    public string GetStatusClass(string status)
    {
        switch (status.ToLower())
        {
            case "pending": return "bg-yellow-100 text-yellow-800";
            case "processing": return "bg-blue-100 text-blue-800";
            case "shipped": return "bg-purple-100 text-purple-800";
            case "delivered": return "bg-green-100 text-green-800";
            case "cancelled": return "bg-red-100 text-red-800";
            default: return "bg-gray-100 text-gray-800";
        }
    }
}
