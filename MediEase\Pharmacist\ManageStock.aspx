﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="ManageStock.aspx.cs" Inherits="Pharmacist_ManageStock" %>

<!DOCTYPE html>
<html lang="en">
<head runat="server">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Stock - MediEase Pharmacist</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <style>
        body { font-family: 'Inter', sans-serif; }
        .sidebar-link { display: flex; align-items: center; padding: 0.75rem 1rem; border-radius: 0.5rem; transition: background-color 0.2s, color 0.2s; }
        .sidebar-link:hover, .sidebar-link.active { background-color: #0d9488; color: white; }
        .sidebar-link i { width: 1.25rem; margin-right: 0.75rem; }
        .validation-error { color: #D32F2F; font-size: 0.875rem; }
    </style>
</head>
<body class="bg-gray-100">
    <form id="form1" runat="server">
        <div class="flex h-screen bg-gray-100">
            <!-- Sidebar -->
            <aside class="w-64 bg-teal-600 text-white flex-col p-4 hidden md:flex">
                <div class="flex items-center space-x-2 pb-4 border-b border-teal-500">
                    <i class="fas fa-pills text-3xl"></i>
                    <span class="text-2xl font-bold">MediEase</span>
                </div>
                <nav class="mt-6 flex-grow">
                    <a href="Dashboard.aspx" class="sidebar-link"><i class="fas fa-tachometer-alt fa-fw"></i><span>Dashboard</span></a>
                    <a href="ManageOrders.aspx" class="sidebar-link"><i class="fas fa-tasks fa-fw"></i><span>Manage Orders</span></a>
                    <a href="ManageStock.aspx" class="sidebar-link active"><i class="fas fa-boxes fa-fw"></i><span>Manage Stock</span></a>
                    <a href="ManagePrescriptions.aspx" class="sidebar-link"><i class="fas fa-file-prescription fa-fw"></i><span>Validate Prescriptions</span></a>
                </nav>
                <div class="pt-4 border-t border-teal-500">
                    <asp:LinkButton ID="btnLogout" runat="server" OnClick="btnLogout_Click" CssClass="sidebar-link w-full"><i class="fas fa-sign-out-alt fa-fw"></i><span>Logout</span></asp:LinkButton>
                </div>
            </aside>

            <!-- Main Content -->
            <main class="flex-1 flex flex-col">
                <header class="bg-white shadow-sm p-4">
                    <h1 class="text-2xl font-bold text-gray-800">Manage Stock</h1>
                </header>

                <div class="flex-1 p-6 overflow-y-auto">
                    <!-- Search and Filter -->
                    <div class="bg-white p-4 rounded-lg shadow mb-6">
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
                            <div class="md:col-span-2">
                                <label class="block text-sm font-medium text-gray-700">Search by Product Name</label>
                                <asp:TextBox ID="txtSearch" runat="server" CssClass="mt-1 block w-full input-style"></asp:TextBox>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Stock Status</label>
                                <asp:DropDownList ID="ddlStockFilter" runat="server" CssClass="mt-1 block w-full input-style">
                                    <asp:ListItem Value="" Text="All"></asp:ListItem>
                                    <asp:ListItem Value="InStock" Text="In Stock"></asp:ListItem>
                                    <asp:ListItem Value="LowStock" Text="Low Stock"></asp:ListItem>
                                    <asp:ListItem Value="OutOfStock" Text="Out of Stock"></asp:ListItem>
                                </asp:DropDownList>
                            </div>
                            <div class="flex space-x-2">
                                <asp:Button ID="btnSearch" runat="server" Text="Search" OnClick="btnSearch_Click" CssClass="w-full justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-teal-600 hover:bg-teal-700" />
                                <asp:Button ID="btnClear" runat="server" Text="Clear" OnClick="btnClear_Click" CssClass="w-full justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50" />
                            </div>
                        </div>
                    </div>
                    
                    <asp:Literal ID="litMessage" runat="server"></asp:Literal>

                    <!-- Stock Grid -->
                    <div class="bg-white p-6 rounded-lg shadow overflow-x-auto">
                         <asp:GridView ID="gvStock" runat="server" AutoGenerateColumns="False"
                             DataKeyNames="MedicineId,StockQuantity"
                             OnRowEditing="gvStock_RowEditing" OnRowCancelingEdit="gvStock_RowCancelingEdit" OnRowUpdating="gvStock_RowUpdating"
                             CssClass="min-w-full divide-y divide-gray-200"
                             HeaderStyle-CssClass="bg-gray-50" PagerStyle-CssClass="bg-white p-4"
                             AllowPaging="True" PageSize="15" OnPageIndexChanging="gvStock_PageIndexChanging">
                             <Columns>
                                 <asp:BoundField DataField="MedicineId" HeaderText="ID" ReadOnly="True" HeaderStyle-CssClass="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" ItemStyle-CssClass="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900" />
                                 <asp:BoundField DataField="Name" HeaderText="Product Name" ReadOnly="True" HeaderStyle-CssClass="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" ItemStyle-CssClass="px-6 py-4 whitespace-nowrap text-sm text-gray-700" />
                                 <asp:BoundField DataField="StockQuantity" HeaderText="Current Stock" ReadOnly="True" HeaderStyle-CssClass="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider" ItemStyle-CssClass="px-6 py-4 whitespace-nowrap text-center text-sm" />
                                 <asp:BoundField DataField="ReorderLevel" HeaderText="Reorder Level" ReadOnly="True" HeaderStyle-CssClass="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider" ItemStyle-CssClass="px-6 py-4 whitespace-nowrap text-center text-sm" />
                                 
                                 <asp:TemplateField HeaderText="New Stock Count">
                                     <ItemTemplate>
                                         <%-- Display only in normal mode --%>
                                     </ItemTemplate>
                                     <EditItemTemplate>
                                         <asp:TextBox ID="txtNewStock" runat="server" TextMode="Number" Text='<%# Bind("StockQuantity") %>' CssClass="w-24 px-2 py-1 border border-gray-300 rounded-md"></asp:TextBox>
                                         <asp:RequiredFieldValidator ID="rfvNewStock" runat="server" ControlToValidate="txtNewStock" ErrorMessage="*" CssClass="validation-error" Display="Dynamic"></asp:RequiredFieldValidator>
                                     </EditItemTemplate>
                                 </asp:TemplateField>

                                 <asp:CommandField ShowEditButton="True" ButtonType="Link" ControlStyle-CssClass="text-teal-600 hover:text-teal-900"
                                     EditImageUrl="" EditText="<i class='fas fa-edit'></i>"
                                     UpdateImageUrl="" UpdateText="<i class='fas fa-save text-green-600'></i>"
                                     CancelImageUrl="" CancelText="<i class='fas fa-times text-red-600'></i>">
                                     <HeaderStyle CssClass="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider" />
                                     <ItemStyle CssClass="px-6 py-4 whitespace-nowrap text-center text-sm font-medium" />
                                 </asp:CommandField>
                             </Columns>
                             <EmptyDataTemplate>
                                 <div class="text-center py-8"><p class="text-gray-500">No products found.</p></div>
                             </EmptyDataTemplate>
                         </asp:GridView>
                    </div>
                </div>
            </main>
        </div>
    </form>
</body>
</html>
