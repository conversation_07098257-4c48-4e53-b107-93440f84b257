﻿<%--
 =======================================================================
 FILE: AuditTrail.aspx (Final Version)
 =======================================================================
--%>
<%@ Page Title="System Audit Trail" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="AuditTrail.aspx.cs" Inherits="MediEase.Admin.AuditTrail" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>System Audit Trail</h1>
            <a href="Dashboard.aspx" class="btn btn-outline-secondary">&larr; Back to Admin Dashboard</a>
        </div>

        <div class="card shadow-sm">
            <div class="card-header">
                <h4 class="mb-0">All Recorded Actions</h4>
            </div>
            <div class="card-body">
                <asp:GridView ID="AuditTrailGrid" runat="server"
                    CssClass="table table-striped"
                    AutoGenerateColumns="False"
                    DataKeyNames="AuditID"
                    GridLines="None"
                    AllowPaging="True"
                    OnPageIndexChanging="AuditTrailGrid_PageIndexChanging"
                    PageSize="25">
                    <Columns>
                        <asp:BoundField DataField="AuditID" HeaderText="Log ID" />
                        <asp:BoundField DataField="Timestamp" HeaderText="Date & Time" DataFormatString="{0:g}" />
                        <asp:BoundField DataField="UserName" HeaderText="User" />
                        <asp:BoundField DataField="ActionType" HeaderText="Action Type" />
                        <asp:TemplateField HeaderText="Details">
                            <ItemTemplate>
                                <div style="max-height: 100px; overflow-y: auto;">
                                    <asp:Literal ID="DetailsLiteral" runat="server" Text='<%# FormatDetails(Eval("TableName"), Eval("RecordID"), Eval("OldValues"), Eval("NewValues")) %>'></asp:Literal>
                                </div>
                            </ItemTemplate>
                        </asp:TemplateField>
                    </Columns>
                    <EmptyDataTemplate>
                        <div class="alert alert-info text-center">
                            No actions have been recorded in the audit trail yet.
                        </div>
                    </EmptyDataTemplate>
                </asp:GridView>
            </div>
        </div>
    </div>
</asp:Content>