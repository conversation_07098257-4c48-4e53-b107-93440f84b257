﻿/* FILE: Admin/ManageUsers.aspx.cs */
using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace MediEase.Admin
{
    public partial class ManageUsers : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (Session["UserID"] == null || Session["RoleName"]?.ToString() != "Admin") { Response.Redirect("~/Login.aspx"); return; }
            if (!IsPostBack) { BindUsersGrid(); }
        }

        private void BindUsersGrid()
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "SELECT u.UserID, up.FirstName + ' ' + up.LastName AS FullName, u.Email, r.RoleName, u.IsActive FROM Users u INNER JOIN UserProfiles up ON u.UserID = up.UserID INNER JOIN Roles r ON u.RoleID = r.RoleID ORDER BY u.UserID;";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    try
                    {
                        con.Open();
                        SqlDataAdapter sda = new SqlDataAdapter(cmd);
                        DataTable dt = new DataTable();
                        sda.Fill(dt);
                        UsersGrid.DataSource = dt;
                        UsersGrid.DataBind();
                    }
                    catch (Exception ex) { System.Diagnostics.Debug.WriteLine("Error binding users grid: " + ex.Message); }
                }
            }
        }

        protected void UsersGrid_PageIndexChanging(object sender, GridViewPageEventArgs e)
        {
            UsersGrid.PageIndex = e.NewPageIndex;
            BindUsersGrid();
        }

        protected void UsersGrid_RowCommand(object sender, GridViewCommandEventArgs e)
        {
            if (e.CommandName == "ActivateUser" || e.CommandName == "DeactivateUser")
            {
                int userId = Convert.ToInt32(e.CommandArgument);
                bool newStatus = (e.CommandName == "ActivateUser");
                SetUserStatus(userId, newStatus);
                BindUsersGrid();
            }
        }

        private void SetUserStatus(int userId, bool isActive)
        {
            if (userId == 1 && !isActive) { return; } // Prevent deactivating main admin

            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "UPDATE Users SET IsActive = @IsActive WHERE UserID = @UserID";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@IsActive", isActive);
                    cmd.Parameters.AddWithValue("@UserID", userId);
                    try
                    {
                        con.Open();
                        cmd.ExecuteNonQuery();
                        int adminUserId = Convert.ToInt32(Session["UserID"]);
                        string action = isActive ? "User Activated" : "User Deactivated";
                        AuditLogger.LogAction(adminUserId, action, "Users", userId, newValues: $"IsActive = {isActive}");
                    }
                    catch (Exception ex) { System.Diagnostics.Debug.WriteLine("Error updating user status: " + ex.Message); }
                }
            }
        }
    }
}