<%@ Page Language="C#" AutoEventWireup="true" CodeFile="Register.aspx.cs" Inherits="Register" %>

<!DOCTYPE html>
<html lang="en">
<head runat="server">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - MediEase</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" xintegrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <style>
        body { font-family: 'Inter', sans-serif; }
        .validation-error { color: #D32F2F; font-size: 0.875rem; margin-top: 0.25rem; }
    </style>
</head>
<body class="bg-gray-50">
    <form id="form1" runat="server">
        <div class="min-h-screen flex flex-col items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
            <div class="max-w-4xl w-full space-y-8 p-10 bg-white shadow-lg rounded-xl">
                <div>
                    <a href="default.aspx" class="flex justify-center items-center space-x-2">
                        <i class="fas fa-pills text-4xl text-teal-600"></i>
                        <span class="text-3xl font-bold text-gray-800">MediEase</span>
                    </a>
                    <h2 class="mt-6 text-center text-2xl font-extrabold text-gray-900">
                        Create your new account
                    </h2>
                    <p class="mt-2 text-center text-sm text-gray-600">
                        Or <a href="Login.aspx" class="font-medium text-teal-600 hover:text-teal-500">
                            sign in to your existing account
                        </a>
                    </p>
                </div>
                
                <asp:Literal ID="litMessage" runat="server"></asp:Literal>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <asp:Label For="txtFirstName" runat="server" Text="First Name" CssClass="block text-sm font-medium text-gray-700"></asp:Label>
                        <asp:TextBox ID="txtFirstName" runat="server" CssClass="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-teal-500 focus:border-teal-500 sm:text-sm" placeholder="John"></asp:TextBox>
                        <asp:RequiredFieldValidator ID="rfvFirstName" runat="server" ControlToValidate="txtFirstName" ErrorMessage="First name is required." CssClass="validation-error" Display="Dynamic"></asp:RequiredFieldValidator>
                    </div>

                    <div>
                        <asp:Label For="txtLastName" runat="server" Text="Last Name" CssClass="block text-sm font-medium text-gray-700"></asp:Label>
                        <asp:TextBox ID="txtLastName" runat="server" CssClass="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-teal-500 focus:border-teal-500 sm:text-sm" placeholder="Doe"></asp:TextBox>
                        <asp:RequiredFieldValidator ID="rfvLastName" runat="server" ControlToValidate="txtLastName" ErrorMessage="Last name is required." CssClass="validation-error" Display="Dynamic"></asp:RequiredFieldValidator>
                    </div>

                    <div class="md:col-span-2">
                        <asp:Label For="txtEmail" runat="server" Text="Email Address" CssClass="block text-sm font-medium text-gray-700"></asp:Label>
                        <asp:TextBox ID="txtEmail" runat="server" TextMode="Email" CssClass="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-teal-500 focus:border-teal-500 sm:text-sm" placeholder="<EMAIL>"></asp:TextBox>
                        <asp:RequiredFieldValidator ID="rfvEmail" runat="server" ControlToValidate="txtEmail" ErrorMessage="Email is required." CssClass="validation-error" Display="Dynamic"></asp:RequiredFieldValidator>
                        <asp:RegularExpressionValidator ID="revEmail" runat="server" ControlToValidate="txtEmail" ValidationExpression="\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*" ErrorMessage="Invalid email format." CssClass="validation-error" Display="Dynamic"></asp:RegularExpressionValidator>
                    </div>
                    
                    <div>
                        <asp:Label For="txtPhoneNumber" runat="server" Text="Phone Number" CssClass="block text-sm font-medium text-gray-700"></asp:Label>
                        <asp:TextBox ID="txtPhoneNumber" runat="server" TextMode="Phone" CssClass="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-teal-500 focus:border-teal-500 sm:text-sm" placeholder="******-1234"></asp:TextBox>
                    </div>

                    <div>
                        <asp:Label For="txtDateOfBirth" runat="server" Text="Date of Birth" CssClass="block text-sm font-medium text-gray-700"></asp:Label>
                        <asp:TextBox ID="txtDateOfBirth" runat="server" TextMode="Date" CssClass="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teal-500 focus:border-teal-500 sm:text-sm"></asp:TextBox>
                    </div>

                    <div>
                        <asp:Label For="txtPassword" runat="server" Text="Password" CssClass="block text-sm font-medium text-gray-700"></asp:Label>
                        <asp:TextBox ID="txtPassword" runat="server" TextMode="Password" CssClass="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-teal-500 focus:border-teal-500 sm:text-sm" placeholder="••••••••"></asp:TextBox>
                        <asp:RequiredFieldValidator ID="rfvPassword" runat="server" ControlToValidate="txtPassword" ErrorMessage="Password is required." CssClass="validation-error" Display="Dynamic"></asp:RequiredFieldValidator>
                    </div>

                    <div>
                        <asp:Label For="txtConfirmPassword" runat="server" Text="Confirm Password" CssClass="block text-sm font-medium text-gray-700"></asp:Label>
                        <asp:TextBox ID="txtConfirmPassword" runat="server" TextMode="Password" CssClass="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-teal-500 focus:border-teal-500 sm:text-sm" placeholder="••••••••"></asp:TextBox>
                        <asp:CompareValidator ID="cvPassword" runat="server" ControlToValidate="txtConfirmPassword" ControlToCompare="txtPassword" Operator="Equal" ErrorMessage="Passwords do not match." CssClass="validation-error" Display="Dynamic"></asp:CompareValidator>
                    </div>
                </div>

                <div class="pt-4">
                    <asp:Button ID="btnRegister" runat="server" Text="Create Account" OnClick="btnRegister_Click" CssClass="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 cursor-pointer" />
                </div>
            </div>
        </div>
    </form>
</body>
</html>
