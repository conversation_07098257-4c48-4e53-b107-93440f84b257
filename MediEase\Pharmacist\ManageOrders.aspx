﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="ManageOrders.aspx.cs" Inherits="Pharmacist_ManageOrders" %>

<!DOCTYPE html>
<html lang="en">
<head runat="server">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Orders - MediEase Pharmacist</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <style>
        body { font-family: 'Inter', sans-serif; }
        .sidebar-link { display: flex; align-items: center; padding: 0.75rem 1rem; border-radius: 0.5rem; transition: background-color 0.2s, color 0.2s; }
        .sidebar-link:hover, .sidebar-link.active { background-color: #0d9488; color: white; }
        .sidebar-link i { width: 1.25rem; margin-right: 0.75rem; }
    </style>
</head>
<body class="bg-gray-100">
    <form id="form1" runat="server">
        <div class="flex h-screen bg-gray-100">
            <!-- Sidebar -->
            <aside class="w-64 bg-teal-600 text-white flex flex-col p-4">
                <div class="flex items-center space-x-2 pb-4 border-b border-teal-500">
                    <i class="fas fa-pills text-3xl"></i>
                    <span class="text-2xl font-bold">MediEase</span>
                </div>
                <nav class="mt-6 flex-grow">
                    <a href="Dashboard.aspx" class="sidebar-link"><i class="fas fa-tachometer-alt fa-fw"></i><span>Dashboard</span></a>
                    <a href="ManageOrders.aspx" class="sidebar-link active"><i class="fas fa-tasks fa-fw"></i><span>Manage Orders</span></a>
                    <a href="ManageStock.aspx" class="sidebar-link"><i class="fas fa-boxes fa-fw"></i><span>Manage Stock</span></a>
                    <a href="ManagePrescriptions.aspx" class="sidebar-link"><i class="fas fa-file-prescription fa-fw"></i><span>Validate Prescriptions</span></a>
                </nav>
                <div class="pt-4 border-t border-teal-500">
                    <asp:LinkButton ID="btnLogout" runat="server" OnClick="btnLogout_Click" CssClass="sidebar-link w-full"><i class="fas fa-sign-out-alt fa-fw"></i><span>Logout</span></asp:LinkButton>
                </div>
            </aside>

            <!-- Main Content -->
            <main class="flex-1 flex flex-col">
                <header class="bg-white shadow-sm p-4">
                    <h1 class="text-2xl font-bold text-gray-800">Manage Orders</h1>
                </header>

                <div class="flex-1 p-6 overflow-y-auto">
                    <!-- Search and Filter Controls -->
                    <div class="bg-white p-4 rounded-lg shadow mb-6">
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Search by Order # or Customer</label>
                                <asp:TextBox ID="txtSearch" runat="server" CssClass="mt-1 block w-full input-style"></asp:TextBox>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Filter by Status</label>
                                <asp:DropDownList ID="ddlStatusFilter" runat="server" CssClass="mt-1 block w-full input-style">
                                    <asp:ListItem Value="" Text="All Statuses"></asp:ListItem>
                                    <asp:ListItem Value="Pending" Text="Pending"></asp:ListItem>
                                    <asp:ListItem Value="Processing" Text="Processing"></asp:ListItem>
                                    <asp:ListItem Value="Shipped" Text="Shipped"></asp:ListItem>
                                    <asp:ListItem Value="Delivered" Text="Delivered"></asp:ListItem>
                                    <asp:ListItem Value="Cancelled" Text="Cancelled"></asp:ListItem>
                                </asp:DropDownList>
                            </div>
                            <div>
                                 <label class="block text-sm font-medium text-gray-700">Requires Prescription</label>
                                 <asp:DropDownList ID="ddlPrescriptionFilter" runat="server" CssClass="mt-1 block w-full input-style">
                                     <asp:ListItem Value="" Text="Any"></asp:ListItem>
                                     <asp:ListItem Value="1" Text="Yes"></asp:ListItem>
                                     <asp:ListItem Value="0" Text="No"></asp:ListItem>
                                 </asp:DropDownList>
                            </div>
                            <div class="flex space-x-2">
                                <asp:Button ID="btnSearch" runat="server" Text="Search" OnClick="btnSearch_Click" CssClass="w-full justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500" />
                                <asp:Button ID="btnClear" runat="server" Text="Clear" OnClick="btnClear_Click" CssClass="w-full justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500" />
                            </div>
                        </div>
                    </div>

                    <!-- Orders Grid -->
                    <div class="bg-white p-6 rounded-lg shadow overflow-x-auto">
                         <asp:GridView ID="gvOrders" runat="server" AutoGenerateColumns="False"
                             DataKeyNames="OrderId"
                             CssClass="min-w-full divide-y divide-gray-200"
                             HeaderStyle-CssClass="bg-gray-50" PagerStyle-CssClass="bg-white p-4"
                             AllowPaging="True" PageSize="15" OnPageIndexChanging="gvOrders_PageIndexChanging">
                             <Columns>
                                 <asp:BoundField DataField="OrderNumber" HeaderText="Order #" HeaderStyle-CssClass="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" ItemStyle-CssClass="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900" />
                                 <asp:BoundField DataField="CustomerName" HeaderText="Customer" HeaderStyle-CssClass="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" ItemStyle-CssClass="px-6 py-4 whitespace-nowrap text-sm text-gray-700" />
                                 <asp:BoundField DataField="OrderDate" HeaderText="Order Date" DataFormatString="{0:MMM dd, yyyy HH:mm}" HeaderStyle-CssClass="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" ItemStyle-CssClass="px-6 py-4 whitespace-nowrap text-sm text-gray-500" />
                                 <asp:BoundField DataField="TotalAmount" HeaderText="Total" DataFormatString="{0:C}" HeaderStyle-CssClass="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" ItemStyle-CssClass="px-6 py-4 whitespace-nowrap text-sm text-gray-500" />
                                 
                                 <asp:TemplateField HeaderText="Status" HeaderStyle-CssClass="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" ItemStyle-CssClass="px-6 py-4 whitespace-nowrap">
                                     <ItemTemplate>
                                         <span class='px-2 inline-flex text-xs leading-5 font-semibold rounded-full <%# GetStatusClass(Eval("Status").ToString()) %>'>
                                             <%# Eval("Status") %>
                                         </span>
                                     </ItemTemplate>
                                 </asp:TemplateField>

                                  <asp:TemplateField HeaderText="Prescription" HeaderStyle-CssClass="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider" ItemStyle-CssClass="px-6 py-4 whitespace-nowrap text-center">
                                     <ItemTemplate>
                                         <i class='<%# (bool)Eval("RequiresPrescription") ? "fas fa-check-circle text-green-500" : "fas fa-times-circle text-gray-400" %>'></i>
                                     </ItemTemplate>
                                 </asp:TemplateField>

                                 <asp:TemplateField HeaderText="Action" HeaderStyle-CssClass="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider" ItemStyle-CssClass="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                                     <ItemTemplate>
                                         <a href='OrderDetail.aspx?id=<%# Eval("OrderId") %>' class="text-teal-600 hover:text-teal-900">View Details</a>
                                     </ItemTemplate>
                                 </asp:TemplateField>
                             </Columns>
                             <EmptyDataTemplate>
                                 <div class="text-center py-8">
                                     <p class="mt-2 text-gray-500">No orders found matching your criteria.</p>
                                 </div>
                             </EmptyDataTemplate>
                         </asp:GridView>
                    </div>
                </div>
            </main>
        </div>
    </form>
</body>
</html>
