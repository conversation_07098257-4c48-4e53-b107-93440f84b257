using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Runtime.Remoting.Messaging;
using System.Web.UI;

public partial class Register : System.Web.UI.Page
{
    protected void btnRegister_Click(object sender, EventArgs e)
    {
        // Check if the page passes all validation checks (e.g., required fields, password match)
        if (!Page.IsValid)
        {
            return;
        }

        // Retrieve user input from the form
        string firstName = txtFirstName.Text.Trim();
        string lastName = txtLastName.Text.Trim();
        string email = txtEmail.Text.Trim();
        string phoneNumber = txtPhoneNumber.Text.Trim();
        string password = txtPassword.Text; // Password will be hashed, not trimmed

        // Use TryParse for date to avoid conversion errors
        DateTime? dateOfBirth = null;
        if (DateTime.TryParse(txtDateOfBirth.Text, out DateTime dob))
        {
            dateOfBirth = dob;
        }

        string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;

        using (SqlConnection con = new SqlConnection(connectionString))
        {
            try
            {
                con.Open();

                // 1. CHECK IF EMAIL ALREADY EXISTS
                string checkEmailQuery = "SELECT COUNT(*) FROM [Users] WHERE [Email] = @Email";
                using (SqlCommand checkCmd = new SqlCommand(checkEmailQuery, con))
                {
                    checkCmd.Parameters.AddWithValue("@Email", email);
                    int userCount = (int)checkCmd.ExecuteScalar();
                    if (userCount > 0)
                    {
                        ShowMessage("This email address is already registered. Please <a href='Login.aspx' class='font-bold text-teal-700'>log in</a>.", "error");
                        return;
                    }
                }

                // 2. HASH THE PASSWORD
                // This uses the BCrypt.Net-Next library. Make sure it's installed via NuGet.
                string passwordHash = BCrypt.Net.BCrypt.HashPassword(password);

                // 3. INSERT THE NEW USER
                string insertQuery = @"
                    INSERT INTO [Users] 
                    ([FirstName], [LastName], [Email], [PasswordHash], [PhoneNumber], [DateOfBirth], [Role], [IsActive], [CreatedDate])
                    VALUES 
                    (@FirstName, @LastName, @Email, @PasswordHash, @PhoneNumber, @DateOfBirth, 'Customer', 1, GETDATE())";

                using (SqlCommand insertCmd = new SqlCommand(insertQuery, con))
                {
                    // Use parameters to prevent SQL injection
                    insertCmd.Parameters.AddWithValue("@FirstName", firstName);
                    insertCmd.Parameters.AddWithValue("@LastName", lastName);
                    insertCmd.Parameters.AddWithValue("@Email", email);
                    insertCmd.Parameters.AddWithValue("@PasswordHash", passwordHash);
                    insertCmd.Parameters.AddWithValue("@PhoneNumber", string.IsNullOrEmpty(phoneNumber) ? (object)DBNull.Value : phoneNumber);
                    insertCmd.Parameters.AddWithValue("@DateOfBirth", dateOfBirth.HasValue ? (object)dateOfBirth.Value : (object)DBNull.Value);

                    int result = insertCmd.ExecuteNonQuery();

                    if (result > 0)
                    {
                        // Registration successful
                        ShowMessage("Registration successful! You can now <a href='Login.aspx' class='font-bold text-teal-700'>log in</a>.", "success");
                        // Optionally, disable the form to prevent re-submission
                        btnRegister.Enabled = false;
                    }
                    else
                    {
                        ShowMessage("An unexpected error occurred. Please try again.", "error");
                    }
                }
            }
            catch (Exception ex)
            {
                // Log the exception details for debugging
                System.Diagnostics.Debug.WriteLine(ex.ToString());
                ShowMessage("A database error occurred. Please contact support.", "error");
            }
        }
    }

    /// <summary>
    /// Displays a formatted message to the user.
    /// </summary>
    /// <param name="message">The message text to display.</param>
    /// <param name="type">The type of message ('success' or 'error').</param>
    private void ShowMessage(string message, string type)
    {
        string cssClass = (type == "success")
            ? "bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative"
            : "bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative";

        litMessage.Text = $"<div class='{cssClass}' role='alert'>{message}</div>";
    }
}
