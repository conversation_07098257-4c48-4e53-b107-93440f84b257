﻿using System;
using System.Web.UI;
using System.Data.SqlClient;
using System.Configuration;

namespace MediEase
{
    public partial class Register : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            LoginHyperLink.NavigateUrl = ResolveUrl("~/Login.aspx");
        }

        protected void CreateUser_Click(object sender, EventArgs e)
        {
            if (IsValid)
            {
                string firstName = FirstName.Text.Trim();
                string lastName = LastName.Text.Trim();
                string email = Email.Text.Trim();
                string password = Password.Text;
                int customerRoleID = 3;

                string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    con.Open();
                    SqlTransaction transaction = con.BeginTransaction();

                    try
                    {
                        string checkUserQuery = "SELECT COUNT(*) FROM Users WHERE Email = @Email";
                        using (SqlCommand checkUserCmd = new SqlCommand(checkUserQuery, con, transaction))
                        {
                            checkUserCmd.Parameters.AddWithValue("@Email", email);
                            if ((int)checkUserCmd.ExecuteScalar() > 0)
                            {
                                ErrorMessage.Text = "<div class='alert alert-danger'>Email address is already registered.</div>";
                                transaction.Rollback();
                                return;
                            }
                        }

                        string insertUserQuery = "INSERT INTO Users (Email, Password, RoleID) OUTPUT INSERTED.UserID VALUES (@Email, @Password, @RoleID);";
                        int newUserId;
                        using (SqlCommand insertUserCmd = new SqlCommand(insertUserQuery, con, transaction))
                        {
                            insertUserCmd.Parameters.AddWithValue("@Email", email);
                            insertUserCmd.Parameters.AddWithValue("@Password", password);
                            insertUserCmd.Parameters.AddWithValue("@RoleID", customerRoleID);
                            newUserId = (int)insertUserCmd.ExecuteScalar();
                        }

                        if (newUserId > 0)
                        {
                            string insertProfileQuery = "INSERT INTO UserProfiles (UserID, FirstName, LastName) VALUES (@UserID, @FirstName, @LastName)";
                            using (SqlCommand insertProfileCmd = new SqlCommand(insertProfileQuery, con, transaction))
                            {
                                insertProfileCmd.Parameters.AddWithValue("@UserID", newUserId);
                                insertProfileCmd.Parameters.AddWithValue("@FirstName", firstName);
                                insertProfileCmd.Parameters.AddWithValue("@LastName", lastName);
                                insertProfileCmd.ExecuteNonQuery();
                            }

                            transaction.Commit();
                            Response.Redirect("Login.aspx?Registration=Success");
                        }
                        else
                        {
                            throw new Exception("User account could not be created.");
                        }
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        ErrorMessage.Text = "<div class='alert alert-danger'>An error occurred. Please try again.</div>";
                        System.Diagnostics.Debug.WriteLine("Registration Error: " + ex.Message);
                    }
                }
            }
        }
    }
}