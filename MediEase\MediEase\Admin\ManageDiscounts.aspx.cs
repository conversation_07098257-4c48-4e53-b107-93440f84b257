﻿/* FILE: Admin/ManageDiscounts.aspx.cs */
using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace MediEase.Admin
{
    public partial class ManageDiscounts : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (Session["UserID"] == null || Session["RoleName"]?.ToString() != "Admin") { Response.Redirect("~/Login.aspx"); return; }
            if (!IsPostBack) { BindDiscountsGrid(); }
        }

        private void BindDiscountsGrid()
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "SELECT DiscountID, DiscountCode, Description, DiscountPercentage, StartDate, EndDate, IsActive FROM Discounts ORDER BY DiscountID DESC";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    try { con.Open(); SqlDataAdapter sda = new SqlDataAdapter(cmd); DataTable dt = new DataTable(); sda.Fill(dt); DiscountsGrid.DataSource = dt; DiscountsGrid.DataBind(); }
                    catch (Exception ex) { System.Diagnostics.Debug.WriteLine("Error binding discounts grid: " + ex.Message); }
                }
            }
        }

        protected void CreateDiscountButton_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(DiscountCodeBox.Text) || string.IsNullOrWhiteSpace(PercentageBox.Text)) { ShowMessage("Discount Code and Percentage are required.", false); return; }
            string code = DiscountCodeBox.Text.Trim().ToUpper();
            decimal percentage = decimal.Parse(PercentageBox.Text);
            string description = $"{percentage}% off discount";
            int adminId = Convert.ToInt32(Session["UserID"]);
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "INSERT INTO Discounts (DiscountCode, Description, DiscountPercentage, StartDate, EndDate, IsActive, CreatedByAdminID) VALUES (@Code, @Desc, @Percent, @Start, @End, 1, @AdminID)";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@Code", code);
                    cmd.Parameters.AddWithValue("@Desc", description);
                    cmd.Parameters.AddWithValue("@Percent", percentage);
                    cmd.Parameters.AddWithValue("@AdminID", adminId);
                    if (DateTime.TryParse(StartDateBox.Text, out DateTime startDate)) { cmd.Parameters.AddWithValue("@Start", startDate); } else { cmd.Parameters.AddWithValue("@Start", DBNull.Value); }
                    if (DateTime.TryParse(EndDateBox.Text, out DateTime endDate)) { cmd.Parameters.AddWithValue("@End", endDate); } else { cmd.Parameters.AddWithValue("@End", DBNull.Value); }
                    try { con.Open(); cmd.ExecuteNonQuery(); ShowMessage("Discount code created successfully!", true); ClearForm(); BindDiscountsGrid(); }
                    catch (SqlException sqlEx) when (sqlEx.Number == 2627) { ShowMessage("This discount code already exists. Please choose another.", false); }
                    catch (Exception ex) { ShowMessage("An error occurred: " + ex.Message, false); }
                }
            }
        }

        protected void DiscountsGrid_RowCommand(object sender, GridViewCommandEventArgs e)
        {
            if (e.CommandName == "ToggleStatus")
            {
                int discountId = Convert.ToInt32(e.CommandArgument);
                string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    const string query = "UPDATE Discounts SET IsActive = CASE WHEN IsActive = 1 THEN 0 ELSE 1 END WHERE DiscountID = @DiscountID";
                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        cmd.Parameters.AddWithValue("@DiscountID", discountId);
                        try { con.Open(); cmd.ExecuteNonQuery(); }
                        catch (Exception ex) { ShowMessage("Error updating status: " + ex.Message, false); }
                    }
                }
                BindDiscountsGrid();
            }
        }

        private void ClearForm() { DiscountCodeBox.Text = ""; PercentageBox.Text = ""; StartDateBox.Text = ""; EndDateBox.Text = ""; }
        private void ShowMessage(string message, bool isSuccess) { MessageText.Text = message; MessageAlert.Attributes["class"] = isSuccess ? "alert alert-success" : "alert alert-danger"; MessagePlaceholder.Visible = true; }
    }
}