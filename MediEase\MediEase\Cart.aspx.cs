﻿/* FILE: Cart.aspx.cs */
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace MediEase
{
    public partial class Cart : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (Session["UserID"] == null) { Response.Redirect("~/Login.aspx?returnUrl=" + Server.UrlEncode(Request.Url.AbsolutePath)); return; }
            if (!IsPostBack) { BindCart(); }
        }

        private void BindCart()
        {
            List<CartItem> cart = Session["Cart"] as List<CartItem>;
            if (cart != null && cart.Count > 0)
            {
                CartView.Visible = true;
                EmptyCartView.Visible = false;
                CartRepeater.DataSource = cart;
                CartRepeater.DataBind();
                decimal subtotal = cart.Sum(item => item.Total);
                SubtotalLabel.Text = subtotal.ToString("C");
                TotalLabel.Text = subtotal.ToString("C");
            }
            else
            {
                CartView.Visible = false;
                EmptyCartView.Visible = true;
            }
        }

        protected void CartRepeater_ItemCommand(object source, RepeaterCommandEventArgs e)
        {
            List<CartItem> cart = Session["Cart"] as List<CartItem>;
            if (cart == null) return;
            int productId = Convert.ToInt32(e.CommandArgument);
            CartItem itemToModify = cart.FirstOrDefault(item => item.ProductID == productId);
            if (itemToModify == null) return;
            if (e.CommandName == "Remove")
            {
                cart.Remove(itemToModify);
            }
            else if (e.CommandName == "Update")
            {
                TextBox quantityBox = e.Item.FindControl("QuantityBox") as TextBox;
                if (quantityBox != null && int.TryParse(quantityBox.Text, out int newQuantity))
                {
                    if (newQuantity > 0) { itemToModify.Quantity = newQuantity; }
                    else { cart.Remove(itemToModify); }
                }
            }
            Session["Cart"] = cart;
            BindCart();
        }

        protected void CheckoutButton_Click(object sender, EventArgs e)
        {
            Response.Redirect("~/Checkout.aspx");
        }
    }
}