﻿/*
 =======================================================================
 FILE: ManageQuestions.aspx.cs
 PURPOSE: Backend logic for the pharmacist's question management page.
          Handles filtering questions by answered/unanswered status.
 PLACE IN: The 'Pharmacist' folder in your project root.
 =======================================================================
*/
using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace MediEase.Pharmacist
{
    public partial class ManageQuestions : Page
    {
        // ViewState variable to keep track of the current filter (Unanswered/Answered)
        private string CurrentView
        {
            get { return ViewState["CurrentView"] as string ?? "Unanswered"; }
            set { ViewState["CurrentView"] = value; }
        }

        protected void Page_Load(object sender, EventArgs e)
        {
            if (Session["UserID"] == null || Session["RoleName"]?.ToString() != "Pharmacist")
            {
                Response.Redirect("~/Login.aspx");
                return;
            }

            if (!IsPostBack)
            {
                BindQuestionsGrid();
                UpdateActiveTab();
            }
        }

        private void BindQuestionsGrid()
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                var queryBuilder = new StringBuilder(@"
                    SELECT 
                        q.QuestionID, 
                        q.Subject, 
                        q.QuestionDate,
                        up.FirstName + ' ' + up.LastName AS CustomerName
                    FROM 
                        PharmacistQuestions q
                    INNER JOIN 
                        UserProfiles up ON q.CustomerID = up.UserID
                ");

                var cmd = new SqlCommand();

                // Dynamically change the WHERE clause based on the selected tab
                if (CurrentView == "Unanswered")
                {
                    queryBuilder.Append(" WHERE q.IsAnswered = 0");
                }
                else // Answered
                {
                    queryBuilder.Append(" WHERE q.IsAnswered = 1");
                }

                queryBuilder.Append(" ORDER BY q.QuestionDate DESC");

                cmd.CommandText = queryBuilder.ToString();
                cmd.Connection = con;

                try
                {
                    con.Open();
                    SqlDataAdapter sda = new SqlDataAdapter(cmd);
                    DataTable dt = new DataTable();
                    sda.Fill(dt);
                    QuestionsGrid.DataSource = dt;
                    QuestionsGrid.DataBind();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine("Error binding questions: " + ex.Message);
                }
            }
        }

        protected void Tab_Click(object sender, EventArgs e)
        {
            LinkButton clickedTab = (LinkButton)sender;
            CurrentView = clickedTab.CommandArgument;
            QuestionsGrid.PageIndex = 0; // Reset to first page on tab change
            BindQuestionsGrid();
            UpdateActiveTab();
        }

        private void UpdateActiveTab()
        {
            // Update the CSS classes to show which tab is active
            if (CurrentView == "Unanswered")
            {
                UnansweredTab.CssClass = "nav-link active";
                AnsweredTab.CssClass = "nav-link";
            }
            else
            {
                UnansweredTab.CssClass = "nav-link";
                AnsweredTab.CssClass = "nav-link active";
            }
        }

        protected void QuestionsGrid_PageIndexChanging(object sender, GridViewPageEventArgs e)
        {
            QuestionsGrid.PageIndex = e.NewPageIndex;
            BindQuestionsGrid();
        }
    }
}
