﻿/*
 =======================================================================
 FILE: ManageSettings.aspx.cs (Full & Unabridged)
 PURPOSE: Backend logic for the system settings page.
          **UPDATED** to include Auto-Refill configuration.
 PLACE IN: The 'Admin' folder in your project root.
 =======================================================================
*/
using System;
using System.Configuration;
using System.Data.SqlClient;
using System.Web.UI;

namespace MediEase.Admin
{
    public partial class ManageSettings : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (Session["UserID"] == null || Session["RoleName"]?.ToString() != "Admin")
            {
                Response.Redirect("~/Login.aspx");
                return;
            }

            if (!IsPostBack)
            {
                LoadSettings();
            }
        }

        private void LoadSettings()
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                // UPDATED: Query now fetches all three settings
                const string query = "SELECT SettingName, SettingValue FROM SystemSettings WHERE SettingName IN ('LoyaltyPointsPerDollar', 'PointsValueInDollars', 'AutoRefillFrequencies')";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    try
                    {
                        con.Open();
                        SqlDataReader reader = cmd.ExecuteReader();
                        while (reader.Read())
                        {
                            string settingName = reader["SettingName"].ToString();
                            string settingValue = reader["SettingValue"].ToString();

                            if (settingName == "LoyaltyPointsPerDollar")
                            {
                                PointsPerDollarBox.Text = settingValue;
                            }
                            else if (settingName == "PointsValueInDollars")
                            {
                                PointsValueBox.Text = settingValue;
                            }
                            else if (settingName == "AutoRefillFrequencies")
                            {
                                RefillFrequenciesBox.Text = settingValue;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine("Error loading system settings: " + ex.Message);
                    }
                }
            }
        }

        protected void SaveChangesButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Save all settings
                SaveSetting("LoyaltyPointsPerDollar", PointsPerDollarBox.Text.Trim());
                SaveSetting("PointsValueInDollars", PointsValueBox.Text.Trim());
                SaveSetting("AutoRefillFrequencies", RefillFrequenciesBox.Text.Trim());

                MessagePlaceholder.Visible = true;
            }
            catch (Exception ex)
            {
                // In a real app, you'd show a more user-friendly error message
                System.Diagnostics.Debug.WriteLine("Error saving settings: " + ex.Message);
            }
        }

        private void SaveSetting(string settingName, string settingValue)
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                con.Open();
                const string updateQuery = "UPDATE SystemSettings SET SettingValue = @Value WHERE SettingName = @Name";
                SqlCommand updateCmd = new SqlCommand(updateQuery, con);
                updateCmd.Parameters.AddWithValue("@Value", settingValue);
                updateCmd.Parameters.AddWithValue("@Name", settingName);

                int rowsAffected = updateCmd.ExecuteNonQuery();

                if (rowsAffected == 0)
                {
                    const string insertQuery = "INSERT INTO SystemSettings (SettingName, SettingValue) VALUES (@Name, @Value)";
                    SqlCommand insertCmd = new SqlCommand(insertQuery, con);
                    insertCmd.Parameters.AddWithValue("@Name", settingName);
                    insertCmd.Parameters.AddWithValue("@Value", settingValue);
                    insertCmd.ExecuteNonQuery();
                }
            }
        }
    }
}