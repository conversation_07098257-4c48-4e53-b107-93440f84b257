﻿/*
 =======================================================================
 FILE: FeedbackDetails.aspx.cs
 PURPOSE: Backend logic for viewing and responding to a single piece of
          customer feedback.
 PLACE IN: The 'Pharmacist' folder in your project root.
 =======================================================================
*/
using System;
using System.Configuration;
using System.Data.SqlClient;
using System.Web.UI;

namespace MediEase.Pharmacist
{
    public partial class FeedbackDetails : Page
    {
        private int feedbackId;

        protected void Page_Load(object sender, EventArgs e)
        {
            if (Session["UserID"] == null || Session["RoleName"]?.ToString() != "Pharmacist")
            {
                Response.Redirect("~/Login.aspx");
                return;
            }

            if (!int.TryParse(Request.QueryString["FeedbackID"], out feedbackId))
            {
                ShowErrorView();
                return;
            }

            if (!IsPostBack)
            {
                LoadFeedbackDetails();
            }
        }

        private void LoadFeedbackDetails()
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = @"
                    SELECT 
                        f.Subject, f.Message, f.SubmissionDate, f.Status, f.ResponseText,
                        up.FirstName + ' ' + up.LastName AS CustomerName
                    FROM Feedback f
                    INNER JOIN UserProfiles up ON f.CustomerID = up.UserID
                    WHERE f.FeedbackID = @FeedbackID;
                ";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@FeedbackID", feedbackId);
                    try
                    {
                        con.Open();
                        SqlDataReader reader = cmd.ExecuteReader();
                        if (reader.Read())
                        {
                            FeedbackIDLabel.Text = feedbackId.ToString();
                            CustomerNameLabel.Text = reader["CustomerName"].ToString();
                            SubmissionDateLabel.Text = Convert.ToDateTime(reader["SubmissionDate"]).ToString("MMMM dd, yyyy");
                            SubjectLabel.Text = Server.HtmlEncode(reader["Subject"].ToString());
                            MessageLiteral.Text = Server.HtmlEncode(reader["Message"].ToString());

                            string status = reader["Status"].ToString();
                            if (status == "Resolved")
                            {
                                RespondedView.Visible = true;
                                ResponseFormView.Visible = false;
                                ResponseTextLiteral.Text = Server.HtmlEncode(reader["ResponseText"].ToString());
                                // In a real app, you might also fetch and display the responder's name and date
                            }
                            else
                            {
                                RespondedView.Visible = false;
                                ResponseFormView.Visible = true;
                            }
                        }
                        else
                        {
                            ShowErrorView();
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine("Error loading feedback details: " + ex.Message);
                        ShowErrorView();
                    }
                }
            }
        }

        protected void SubmitResponseButton_Click(object sender, EventArgs e)
        {
            if (Page.IsValid)
            {
                string responseText = ResponseBox.Text.Trim();
                int pharmacistId = Convert.ToInt32(Session["UserID"]);

                string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    const string query = "UPDATE Feedback SET ResponseText = @ResponseText, Status = 'Resolved', RespondedByPharmacistID = @PharmacistID WHERE FeedbackID = @FeedbackID";
                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        cmd.Parameters.AddWithValue("@ResponseText", responseText);
                        cmd.Parameters.AddWithValue("@PharmacistID", pharmacistId);
                        cmd.Parameters.AddWithValue("@FeedbackID", feedbackId);
                        try
                        {
                            con.Open();
                            cmd.ExecuteNonQuery();
                            // After submitting, reload the page to show the response in the read-only view
                            LoadFeedbackDetails();
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine("Error submitting feedback response: " + ex.Message);
                        }
                    }
                }
            }
        }

        private void ShowErrorView()
        {
            FeedbackView.Visible = false;
            ErrorView.Visible = true;
        }
    }
}