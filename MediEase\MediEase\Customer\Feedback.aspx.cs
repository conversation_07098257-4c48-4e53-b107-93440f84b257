﻿/* FILE: Customer/Feedback.aspx.cs */
using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace MediEase.Customer
{
    public partial class Feedback : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (Session["UserID"] == null || Session["RoleName"]?.ToString() != "Customer") { Response.Redirect("~/Login.aspx?returnUrl=" + Server.UrlEncode(Request.Url.AbsolutePath)); return; }
            if (!IsPostBack) { BindFeedbackHistory(); }
        }

        private void BindFeedbackHistory()
        {
            int customerId = Convert.ToInt32(Session["UserID"]);
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "SELECT FeedbackID, Subject, SubmissionDate, Status FROM Feedback WHERE CustomerID = @CustomerID ORDER BY SubmissionDate DESC";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@CustomerID", customerId);
                    try
                    {
                        con.Open();
                        SqlDataAdapter sda = new SqlDataAdapter(cmd);
                        DataTable dt = new DataTable();
                        sda.Fill(dt);
                        FeedbackHistoryGrid.DataSource = dt;
                        FeedbackHistoryGrid.DataBind();
                    }
                    catch (Exception ex) { System.Diagnostics.Debug.WriteLine("Error binding feedback history: " + ex.Message); }
                }
            }
        }

        protected void SubmitButton_Click(object sender, EventArgs e)
        {
            if (Page.IsValid)
            {
                int customerId = Convert.ToInt32(Session["UserID"]);
                string subject = SubjectBox.Text.Trim();
                string message = MessageBox.Text.Trim();
                string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    const string query = "INSERT INTO Feedback (CustomerID, Subject, Message, Status) VALUES (@CustomerID, @Subject, @Message, 'Open')";
                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        cmd.Parameters.AddWithValue("@CustomerID", customerId);
                        cmd.Parameters.AddWithValue("@Subject", subject);
                        cmd.Parameters.AddWithValue("@Message", message);
                        try
                        {
                            con.Open();
                            cmd.ExecuteNonQuery();
                            FormView.Visible = false;
                            SuccessMessage.Visible = true;
                            BindFeedbackHistory();
                        }
                        catch (Exception ex) { System.Diagnostics.Debug.WriteLine("Error submitting feedback: " + ex.Message); }
                    }
                }
            }
        }
    }
}