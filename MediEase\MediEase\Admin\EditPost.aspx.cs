﻿/*
 =======================================================================
 FILE: EditPost.aspx.cs
 PURPOSE: Backend logic for creating and editing blog posts. Handles
          loading post data for editing and saving changes to the DB.
 PLACE IN: The 'Admin' folder in your project root.
 =======================================================================
*/
using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Web.UI;

namespace MediEase.Admin
{
    public partial class EditPost : Page
    {
        private int postId = 0; // Class-level variable to hold the PostID

        protected void Page_Load(object sender, EventArgs e)
        {
            if (Session["UserID"] == null || Session["RoleName"]?.ToString() != "Admin")
            {
                Response.Redirect("~/Login.aspx");
                return;
            }

            // Check for a PostID in the query string for editing
            if (!string.IsNullOrEmpty(Request.QueryString["PostID"]) && int.TryParse(Request.QueryString["PostID"], out postId))
            {
                PostIdField.Value = postId.ToString();
            }

            if (!IsPostBack)
            {
                if (postId > 0)
                {
                    LoadPostForEditing();
                }
            }
        }

        private void LoadPostForEditing()
        {
            PageTitle.Text = "Edit Post";
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "SELECT Title, Content, ImageUrl, IsPublished FROM BlogPosts WHERE PostID = @PostID";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@PostID", postId);
                    try
                    {
                        con.Open();
                        SqlDataReader reader = cmd.ExecuteReader();
                        if (reader.Read())
                        {
                            TitleBox.Text = reader["Title"].ToString();
                            ContentBox.Text = reader["Content"].ToString();
                            IsPublishedCheckBox.Checked = Convert.ToBoolean(reader["IsPublished"]);

                            string imageUrl = reader["ImageUrl"] as string;
                            if (!string.IsNullOrEmpty(imageUrl))
                            {
                                CurrentImage.ImageUrl = ResolveUrl(imageUrl);
                                CurrentImage.Visible = true;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine("Error loading post for editing: " + ex.Message);
                    }
                }
            }
        }

        protected void SavePostButton_Click(object sender, EventArgs e)
        {
            int currentPostId = Convert.ToInt32(PostIdField.Value);
            string title = TitleBox.Text.Trim();
            string content = ContentBox.Text.Trim();
            bool isPublished = IsPublishedCheckBox.Checked;
            int authorId = Convert.ToInt32(Session["UserID"]);
            string imageUrl = null;

            // Handle Image Upload
            if (ImageUpload.HasFile)
            {
                try
                {
                    string fileName = Guid.NewGuid().ToString() + Path.GetExtension(ImageUpload.FileName);
                    string folderPath = Server.MapPath("~/Images/Blog/");
                    if (!Directory.Exists(folderPath))
                    {
                        Directory.CreateDirectory(folderPath);
                    }
                    string savedFilePath = Path.Combine(folderPath, fileName);
                    ImageUpload.SaveAs(savedFilePath);
                    imageUrl = "~/Images/Blog/" + fileName;
                }
                catch (Exception ex)
                {
                    // Handle image upload error
                    System.Diagnostics.Debug.WriteLine("Blog image upload error: " + ex.Message);
                    return;
                }
            }
            else if (currentPostId > 0)
            {
                // Keep the existing image if not uploading a new one during an edit
                imageUrl = CurrentImage.ImageUrl;
            }


            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                string query;
                if (currentPostId == 0)
                {
                    // INSERT new post
                    query = "INSERT INTO BlogPosts (Title, Content, ImageUrl, AuthorID, IsPublished) OUTPUT INSERTED.PostID VALUES (@Title, @Content, @ImageUrl, @AuthorID, @IsPublished)";
                }
                else
                {
                    // UPDATE existing post
                    query = "UPDATE BlogPosts SET Title = @Title, Content = @Content, " +
                            (!string.IsNullOrEmpty(imageUrl) ? "ImageUrl = @ImageUrl, " : "") +
                            "IsPublished = @IsPublished, LastModifiedDate = GETDATE() WHERE PostID = @PostID";
                }

                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@Title", title);
                    cmd.Parameters.AddWithValue("@Content", content);
                    cmd.Parameters.AddWithValue("@IsPublished", isPublished);

                    if (currentPostId == 0)
                    {
                        cmd.Parameters.AddWithValue("@AuthorID", authorId);
                        cmd.Parameters.AddWithValue("@ImageUrl", (object)imageUrl ?? DBNull.Value);
                    }
                    else
                    {
                        cmd.Parameters.AddWithValue("@PostID", currentPostId);
                        if (!string.IsNullOrEmpty(imageUrl))
                        {
                            cmd.Parameters.AddWithValue("@ImageUrl", imageUrl);
                        }
                    }

                    try
                    {
                        con.Open();
                        if (currentPostId == 0)
                        {
                            int newPostId = (int)cmd.ExecuteScalar();
                            PostIdField.Value = newPostId.ToString(); // Update the hidden field with the new ID
                        }
                        else
                        {
                            cmd.ExecuteNonQuery();
                        }

                        MessagePlaceholder.Visible = true;
                        PageTitle.Text = "Edit Post"; // Change title to Edit mode after first save
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine("Error saving blog post: " + ex.Message);
                        // Optionally show an error message
                    }
                }
            }
        }
    }
}
