﻿<%--
 =======================================================================
 FILE: StockDetails.aspx (Final Version)
 =======================================================================
--%>
<%@ Page Title="Manage Stock Details" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="StockDetails.aspx.cs" Inherits="MediEase.Pharmacist.StockDetails" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-5">
        <asp:PlaceHolder ID="MainView" runat="server">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1>Stock Details</h1>
                    <h4 class="text-muted"><asp:Literal ID="ProductNameLabel" runat="server"></asp:Literal></h4>
                </div>
                <a href="ManageStock.aspx" class="btn btn-outline-secondary">&larr; Back to Stock Overview</a>
            </div>

            <!-- Add New Batch Section -->
            <div class="card shadow-sm mb-4">
                <div class="card-header"><h5 class="mb-0">Add New Batch</h5></div>
                <div class="card-body">
                    <div class="form-row align-items-end">
                        <div class="form-group col-md-4"><label>Batch Number</label><asp:TextBox ID="BatchNumberBox" runat="server" CssClass="form-control"></asp:TextBox></div>
                        <div class="form-group col-md-4"><label>Expiry Date</label><asp:TextBox ID="ExpiryDateBox" runat="server" CssClass="form-control" TextMode="Date"></asp:TextBox></div>
                        <div class="form-group col-md-2"><label>Quantity</label><asp:TextBox ID="QuantityBox" runat="server" CssClass="form-control" TextMode="Number"></asp:TextBox></div>
                        <div class="form-group col-md-2"><asp:Button ID="AddBatchButton" runat="server" Text="Add Batch" CssClass="btn btn-primary btn-block" OnClick="AddBatchButton_Click" /></div>
                    </div>
                </div>
            </div>

            <!-- Existing Batches Section -->
            <div class="card shadow-sm">
                <div class="card-header"><h5 class="mb-0">Existing Batches</h5></div>
                <div class="card-body">
                    <asp:GridView ID="BatchesGrid" runat="server"
                        CssClass="table table-hover table-striped"
                        AutoGenerateColumns="False"
                        DataKeyNames="InventoryID"
                        GridLines="None"
                        OnRowEditing="BatchesGrid_RowEditing"
                        OnRowUpdating="BatchesGrid_RowUpdating"
                        OnRowCancelingEdit="BatchesGrid_RowCancelingEdit"
                        OnRowDeleting="BatchesGrid_RowDeleting">
                        <Columns>
                            <asp:BoundField DataField="InventoryID" HeaderText="Inv. ID" ReadOnly="true" />
                            <asp:TemplateField HeaderText="Batch Number"><ItemTemplate><%# Eval("BatchNumber") %></ItemTemplate><EditItemTemplate><asp:TextBox ID="txtBatchNumber" runat="server" Text='<%# Bind("BatchNumber") %>' CssClass="form-control"></asp:TextBox></EditItemTemplate></asp:TemplateField>
                            <asp:TemplateField HeaderText="Expiry Date"><ItemTemplate><%# Eval("ExpiryDate", "{0:MMM dd, yyyy}") %></ItemTemplate><EditItemTemplate><asp:TextBox ID="txtExpiryDate" runat="server" Text='<%# Bind("ExpiryDate", "{0:yyyy-MM-dd}") %>' CssClass="form-control" TextMode="Date"></asp:TextBox></EditItemTemplate></asp:TemplateField>
                             <asp:TemplateField HeaderText="Quantity in Stock"><ItemTemplate><%# Eval("QuantityInStock") %></ItemTemplate><EditItemTemplate><asp:TextBox ID="txtQuantityInStock" runat="server" Text='<%# Bind("QuantityInStock") %>' CssClass="form-control" TextMode="Number"></asp:TextBox></EditItemTemplate></asp:TemplateField>
                            <asp:CommandField ShowEditButton="true" ControlStyle-CssClass="btn btn-sm btn-info" />
                            <asp:CommandField ShowDeleteButton="true" ControlStyle-CssClass="btn btn-sm btn-danger" />
                        </Columns>
                         <EmptyDataTemplate><div class="alert alert-light text-center">No inventory batches have been recorded for this product yet.</div></EmptyDataTemplate>
                    </asp:GridView>
                </div>
            </div>
        </asp:PlaceHolder>
        <asp:PlaceHolder ID="ErrorView" runat="server" Visible="false">
             <div class="alert alert-danger text-center"><h2>Product Not Found</h2><p>The product you are looking for does not exist.</p><a href="ManageStock.aspx" class="btn btn-primary">Return to Stock Overview</a></div>
        </asp:PlaceHolder>
    </div>
</asp:Content>