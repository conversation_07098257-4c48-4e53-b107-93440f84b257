//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated. 
// </auto-generated>
//------------------------------------------------------------------------------

namespace MediEase.Pharmacist
{
    public partial class Dashboard
    {
        /// <summary>
        /// btnRefresh control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Button btnRefresh;

        /// <summary>
        /// lblPendingPrescriptions control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblPendingPrescriptions;

        /// <summary>
        /// lblOrdersToProcess control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblOrdersToProcess;

        /// <summary>
        /// lblLowStockItems control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblLowStockItems;

        /// <summary>
        /// lblTodaysSales control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblTodaysSales;

        /// <summary>
        /// lnkVerifyPrescriptions control.
        /// </summary>
        protected global::System.Web.UI.WebControls.LinkButton lnkVerifyPrescriptions;

        /// <summary>
        /// lnkProcessOrders control.
        /// </summary>
        protected global::System.Web.UI.WebControls.LinkButton lnkProcessOrders;

        /// <summary>
        /// lnkManageInventory control.
        /// </summary>
        protected global::System.Web.UI.WebControls.LinkButton lnkManageInventory;

        /// <summary>
        /// lnkCustomerConsultation control.
        /// </summary>
        protected global::System.Web.UI.WebControls.LinkButton lnkCustomerConsultation;

        /// <summary>
        /// gvPendingPrescriptions control.
        /// </summary>
        protected global::System.Web.UI.WebControls.GridView gvPendingPrescriptions;

        /// <summary>
        /// gvOrdersToProcess control.
        /// </summary>
        protected global::System.Web.UI.WebControls.GridView gvOrdersToProcess;
    }
}
