﻿/* FILE: Admin/Dashboard.aspx.cs */
using System;
using System.Configuration;
using System.Data.SqlClient;
using System.Web.UI;
namespace MediEase.Admin
{
    public partial class Dashboard : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (Session["UserID"] == null || Session["RoleName"]?.ToString() != "Admin") { Response.Redirect("~/Login.aspx"); return; }
            if (!IsPostBack) { LoadDashboardStats(); }
        }
        private void LoadDashboardStats()
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "SELECT (SELECT COUNT(*) FROM Users) AS TotalUsers, (SELECT COUNT(*) FROM Orders) AS TotalOrders, (SELECT COUNT(*) FROM Products) AS TotalProducts, (SELECT COUNT(*) FROM Orders WHERE StatusID = 2) AS PendingOrders;";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    try
                    {
                        con.Open();
                        SqlDataReader reader = cmd.ExecuteReader();
                        if (reader.Read())
                        {
                            TotalUsersLabel.Text = reader["TotalUsers"].ToString();
                            TotalOrdersLabel.Text = reader["TotalOrders"].ToString();
                            TotalProductsLabel.Text = reader["TotalProducts"].ToString();
                            PendingOrdersLabel.Text = reader["PendingOrders"].ToString();
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine("Error loading admin dashboard stats: " + ex.Message);
                        TotalUsersLabel.Text = "N/A"; TotalOrdersLabel.Text = "N/A"; TotalProductsLabel.Text = "N/A"; PendingOrdersLabel.Text = "N/A";
                    }
                }
            }
        }
    }
}