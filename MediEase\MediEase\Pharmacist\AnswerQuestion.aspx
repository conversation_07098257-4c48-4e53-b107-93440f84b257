﻿<%--
 =======================================================================
 FILE: AnswerQuestion.aspx
 PURPOSE: Displays a single customer question and provides a form for
          the pharmacist to submit an answer.
 PLACE IN: The 'Pharmacist' folder in your project root.
 =======================================================================
--%>
<%@ Page Title="Answer Question" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="AnswerQuestion.aspx.cs" Inherits="MediEase.Pharmacist.AnswerQuestion" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-5">
        <asp:PlaceHolder ID="QuestionView" runat="server">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2>Answer Customer Question</h2>
                            <p class="mb-0">Question #<asp:Label ID="QuestionIDLabel" runat="server"></asp:Label></p>
                        </div>
                        <a href="ManageQuestions.aspx" class="btn btn-outline-secondary">&larr; Back to All Questions</a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <p class="mb-1"><strong>From:</strong> <asp:Label ID="CustomerNameLabel" runat="server"></asp:Label></p>
                        <p class="mb-1"><strong>Date Asked:</strong> <asp:Label ID="QuestionDateLabel" runat="server"></asp:Label></p>
                        <p class="mb-1"><strong>Subject:</strong> <asp:Label ID="SubjectLabel" runat="server"></asp:Label></p>
                    </div>
                    <div class="alert alert-secondary">
                        <strong>Customer's Question:</strong>
                        <p class="mt-2" style="white-space: pre-wrap;"><asp:Literal ID="QuestionTextLiteral" runat="server"></asp:Literal></p>
                    </div>

                    <hr />
                    
                    <asp:PlaceHolder ID="AnsweredView" runat="server" Visible="false">
                        <h4>Your Answer</h4>
                         <div class="alert alert-success">
                            <p style="white-space: pre-wrap;"><asp:Literal ID="AnswerTextLiteral" runat="server"></asp:Literal></p>
                        </div>
                         <p class="text-muted">Answered by <asp:Label ID="PharmacistNameLabel" runat="server"></asp:Label> on <asp:Label ID="AnswerDateLabel" runat="server"></asp:Label>.</p>
                    </asp:PlaceHolder>

                    <asp:PlaceHolder ID="AnswerFormView" runat="server">
                         <h4>Your Answer</h4>
                        <div class="form-group">
                            <asp:TextBox ID="AnswerBox" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="7"></asp:TextBox>
                            <asp:RequiredFieldValidator runat="server" ControlToValidate="AnswerBox" ErrorMessage="An answer is required." CssClass="text-danger" Display="Dynamic" />
                        </div>
                        <asp:Button ID="SubmitAnswerButton" runat="server" Text="Submit Answer" CssClass="btn btn-primary" OnClick="SubmitAnswerButton_Click" />
                    </asp:PlaceHolder>
                </div>
            </div>
        </asp:PlaceHolder>

        <asp:PlaceHolder ID="ErrorView" runat="server" Visible="false">
             <div class="alert alert-danger text-center">
                <h2>Question Not Found</h2>
                <p>The question you are looking for does not exist.</p>
                <a href="ManageQuestions.aspx" class="btn btn-primary">Return to Questions List</a>
            </div>
        </asp:PlaceHolder>
    </div>
</asp:Content>
