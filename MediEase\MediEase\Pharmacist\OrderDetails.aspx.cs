﻿/* FILE: Pharmacist/OrderDetails.aspx.cs */
using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace MediEase.Pharmacist
{
    public partial class OrderDetails : Page
    {
        private int orderId;
        private string customerEmail;
        private string customerName;

        protected void Page_Load(object sender, EventArgs e)
        {
            if (Session["UserID"] == null || Session["RoleName"]?.ToString() != "Pharmacist") { Response.Redirect("~/Login.aspx"); return; }
            if (!int.TryParse(Request.QueryString["OrderID"], out orderId)) { ShowErrorView(); return; }
            if (!IsPostBack) { LoadOrderDetails(); }
        }

        private void LoadOrderDetails()
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                try
                {
                    con.Open();
                    const string orderQuery = "SELECT o.OrderDate, o.TotalAmount, o.ShippingAddress, os.StatusName, up.FirstName, up.LastName, up.PhoneNumber, u.Email, pay.TransactionID as ScreenshotPath, pres.FilePath as PrescriptionPath FROM Orders o INNER JOIN OrderStatus os ON o.StatusID = os.StatusID INNER JOIN UserProfiles up ON o.CustomerID = up.UserID INNER JOIN Users u ON o.CustomerID = u.UserID LEFT JOIN Payments pay ON o.OrderID = pay.OrderID LEFT JOIN Prescriptions pres ON o.OrderID = pres.OrderID WHERE o.OrderID = @OrderID;";
                    bool orderFound = false;
                    using (SqlCommand cmd = new SqlCommand(orderQuery, con))
                    {
                        cmd.Parameters.AddWithValue("@OrderID", orderId);
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                orderFound = true;
                                customerName = reader["FirstName"].ToString();
                                customerEmail = reader["Email"].ToString();
                                OrderIDLabel.Text = orderId.ToString();
                                OrderDateLabel.Text = Convert.ToDateTime(reader["OrderDate"]).ToString("MMMM dd, dddd");
                                OrderTotalLabel.Text = Convert.ToDecimal(reader["TotalAmount"]).ToString("C");
                                ShippingAddressLabel.Text = Server.HtmlEncode(reader["ShippingAddress"].ToString()).Replace("\n", "<br/>");
                                CustomerNameLabel.Text = $"{customerName} {reader["LastName"]}";
                                CustomerPhoneLabel.Text = Server.HtmlEncode(reader["PhoneNumber"].ToString());
                                PaymentScreenshotImage.ImageUrl = ResolveUrl(reader["ScreenshotPath"]?.ToString() ?? "");
                                string prescriptionPath = reader["PrescriptionPath"] as string;
                                if (!string.IsNullOrEmpty(prescriptionPath)) { PrescriptionView.Visible = true; PrescriptionImage.ImageUrl = ResolveUrl(prescriptionPath); PrescriptionLink.HRef = ResolveUrl(prescriptionPath); }
                                string status = reader["StatusName"].ToString();
                                OrderStatusLabel.Text = status;
                                OrderStatusLabel.CssClass = "badge badge-pill " + GetStatusBadgeClass(status);
                                if (status.ToLower() != "under review") { ActionButtonsView.Visible = false; ActionTakenView.Visible = true; }
                                PrintInvoiceLink.NavigateUrl = $"~/Pharmacist/Invoice.aspx?OrderID={orderId}";
                            }
                        }
                    }
                    if (orderFound)
                    {
                        const string itemsQuery = "SELECT p.Name, oi.Quantity, oi.PricePerUnit FROM OrderItems oi INNER JOIN Products p ON oi.ProductID = p.ProductID WHERE oi.OrderID = @OrderID;";
                        using (SqlCommand cmd = new SqlCommand(itemsQuery, con))
                        {
                            cmd.Parameters.AddWithValue("@OrderID", orderId);
                            SqlDataAdapter sda = new SqlDataAdapter(cmd);
                            DataTable dt = new DataTable();
                            sda.Fill(dt);
                            OrderItemsGrid.DataSource = dt;
                            OrderItemsGrid.DataBind();
                        }
                        OrderDetailsView.Visible = true;
                        ErrorView.Visible = false;
                    }
                    else { ShowErrorView(); }
                }
                catch (Exception ex) { System.Diagnostics.Debug.WriteLine("Error loading pharmacist order details: " + ex.Message); ShowErrorView(); }
            }
        }

        protected void ApproveButton_Click(object sender, EventArgs e) { UpdateOrderStatus(3, "Processing"); }
        protected void RejectButton_Click(object sender, EventArgs e) { UpdateOrderStatus(7, "Rejected"); }

        private void UpdateOrderStatus(int newStatusId, string newStatusName)
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                con.Open();
                SqlTransaction transaction = con.BeginTransaction();
                try
                {
                    const string updateQuery = "UPDATE Orders SET StatusID = @StatusID WHERE OrderID = @OrderID";
                    using (SqlCommand cmd = new SqlCommand(updateQuery, con, transaction)) { cmd.Parameters.AddWithValue("@StatusID", newStatusId); cmd.Parameters.AddWithValue("@OrderID", orderId); cmd.ExecuteNonQuery(); }
                    if (newStatusId == 3)
                    {
                        const string orderInfoQuery = "SELECT CustomerID, TotalAmount FROM Orders WHERE OrderID = @OrderID";
                        int customerId = 0; decimal totalAmount = 0;
                        using (SqlCommand infoCmd = new SqlCommand(orderInfoQuery, con, transaction)) { infoCmd.Parameters.AddWithValue("@OrderID", orderId); using (SqlDataReader reader = infoCmd.ExecuteReader()) { if (reader.Read()) { customerId = Convert.ToInt32(reader["CustomerID"]); totalAmount = Convert.ToDecimal(reader["TotalAmount"]); } } }
                        decimal pointsPerDollar = SettingsService.GetDecimalSetting("LoyaltyPointsPerDollar", 1.0m);
                        int pointsToAward = (int)Math.Floor(totalAmount * pointsPerDollar);
                        if (pointsToAward > 0 && customerId > 0)
                        {
                            const string pointsQuery = "INSERT INTO LoyaltyPoints (CustomerID, Points, TransactionType, OrderID, TransactionDate) VALUES (@CustomerID, @Points, 'Earned', @OrderID, GETDATE())";
                            using (SqlCommand pointsCmd = new SqlCommand(pointsQuery, con, transaction)) { pointsCmd.Parameters.AddWithValue("@CustomerID", customerId); pointsCmd.Parameters.AddWithValue("@Points", pointsToAward); pointsCmd.Parameters.AddWithValue("@OrderID", orderId); pointsCmd.ExecuteNonQuery(); }
                        }
                    }
                    transaction.Commit();
                    int pharmacistId = Convert.ToInt32(Session["UserID"]);
                    AuditLogger.LogAction(pharmacistId, newStatusName, "Orders", orderId, newValues: $"StatusID = {newStatusId}");
                    LoadCustomerDetailsForEmail();
                    EmailService.SendOrderStatusUpdateEmail(customerEmail, customerName, orderId, newStatusName);
                    NotificationService.CreateNotification(GetCustomerIdForOrder(orderId), $"Your order #{orderId} has been updated to: {newStatusName}", $"~/Customer/OrderDetails.aspx?OrderID={orderId}");
                    LoadOrderDetails();
                }
                catch (Exception ex) { transaction.Rollback(); System.Diagnostics.Debug.WriteLine("Error updating order status: " + ex.Message); }
            }
        }

        private void LoadCustomerDetailsForEmail()
        {
            if (string.IsNullOrEmpty(customerEmail))
            {
                string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    const string query = "SELECT up.FirstName, u.Email FROM Orders o INNER JOIN Users u ON o.CustomerID = u.UserID INNER JOIN UserProfiles up ON u.UserID = up.UserID WHERE o.OrderID = @OrderID";
                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        cmd.Parameters.AddWithValue("@OrderID", orderId);
                        con.Open();
                        SqlDataReader reader = cmd.ExecuteReader();
                        if (reader.Read()) { customerName = reader["FirstName"].ToString(); customerEmail = reader["Email"].ToString(); }
                    }
                }
            }
        }

        private int GetCustomerIdForOrder(int orderId)
        {
            int customerId = 0;
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "SELECT CustomerID FROM Orders WHERE OrderID = @OrderID";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@OrderID", orderId);
                    con.Open();
                    object result = cmd.ExecuteScalar();
                    if (result != null) { customerId = Convert.ToInt32(result); }
                }
            }
            return customerId;
        }

        private void ShowErrorView() { OrderDetailsView.Visible = false; ErrorView.Visible = true; }
        public string GetStatusBadgeClass(string status) { switch (status.ToLower()) { case "pending payment": case "under review": return "badge-warning"; case "processing": return "badge-info"; case "shipped": case "delivered": return "badge-success"; case "cancelled": case "rejected": return "badge-danger"; default: return "badge-secondary"; } }
    }
}