﻿/* FILE: Admin/ManageOrders.aspx.cs */
using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace MediEase.Admin
{
    public partial class ManageOrders : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (Session["UserID"] == null || Session["RoleName"]?.ToString() != "Admin") { Response.Redirect("~/Login.aspx"); return; }
            if (!IsPostBack) { PopulateStatusFilter(); BindOrdersGrid(); }
        }

        private void PopulateStatusFilter()
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "SELECT StatusID, StatusName FROM OrderStatus ORDER BY StatusID";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    try
                    {
                        con.Open();
                        SqlDataAdapter sda = new SqlDataAdapter(cmd);
                        DataTable dt = new DataTable();
                        sda.Fill(dt);
                        StatusFilter.DataSource = dt;
                        StatusFilter.DataTextField = "StatusName";
                        StatusFilter.DataValueField = "StatusID";
                        StatusFilter.DataBind();
                        StatusFilter.Items.Insert(0, new ListItem("All Statuses", "0"));
                    }
                    catch (Exception ex) { System.Diagnostics.Debug.WriteLine("Error populating status filter: " + ex.Message); }
                }
            }
        }

        private void BindOrdersGrid()
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                var queryBuilder = new StringBuilder("SELECT o.OrderID, up.FirstName + ' ' + up.LastName AS CustomerName, o.OrderDate, o.TotalAmount, os.StatusName FROM Orders o INNER JOIN UserProfiles up ON o.CustomerID = up.UserID INNER JOIN OrderStatus os ON o.StatusID = os.StatusID");
                var cmd = new SqlCommand();
                if (StatusFilter.SelectedValue != "0")
                {
                    queryBuilder.Append(" WHERE o.StatusID = @StatusID");
                    cmd.Parameters.AddWithValue("@StatusID", StatusFilter.SelectedValue);
                }
                queryBuilder.Append(" ORDER BY o.OrderDate DESC");
                cmd.CommandText = queryBuilder.ToString();
                cmd.Connection = con;
                try
                {
                    con.Open();
                    SqlDataAdapter sda = new SqlDataAdapter(cmd);
                    DataTable dt = new DataTable();
                    sda.Fill(dt);
                    OrdersGrid.DataSource = dt;
                    OrdersGrid.DataBind();
                }
                catch (Exception ex) { System.Diagnostics.Debug.WriteLine("Error binding all orders for admin: " + ex.Message); }
            }
        }

        protected void StatusFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            OrdersGrid.PageIndex = 0;
            BindOrdersGrid();
        }

        protected void OrdersGrid_PageIndexChanging(object sender, GridViewPageEventArgs e)
        {
            OrdersGrid.PageIndex = e.NewPageIndex;
            BindOrdersGrid();
        }

        public string GetStatusBadgeClass(string status)
        {
            switch (status.ToLower())
            {
                case "pending payment": case "under review": return "badge-warning";
                case "processing": return "badge-info";
                case "shipped": case "delivered": return "badge-success";
                case "cancelled": case "rejected": return "badge-danger";
                default: return "badge-secondary";
            }
        }
    }
}