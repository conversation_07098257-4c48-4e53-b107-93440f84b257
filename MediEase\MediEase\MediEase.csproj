﻿<Project ToolsVersion="15.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props" Condition="Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{7CE4A3C2-750E-41D6-9580-E1313FD379D3}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>MediEase</RootNamespace>
    <AssemblyName>MediEase</AssemblyName>
    <TargetFrameworkVersion>v4.8.1</TargetFrameworkVersion>
    <UseIISExpress>true</UseIISExpress>
    <Use64BitIISExpress />
    <IISExpressSSLPort>44348</IISExpressSSLPort>
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.AspNet.FriendlyUrls, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.FriendlyUrls.Core.1.0.2\lib\net45\Microsoft.AspNet.FriendlyUrls.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="System.Web.Razor">
      <HintPath>..\packages\Microsoft.AspNet.Razor.3.2.9\lib\net45\System.Web.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Webpages">
      <HintPath>..\packages\Microsoft.AspNet.Webpages.3.2.9\lib\net45\System.Web.Webpages.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Webpages.Deployment">
      <HintPath>..\packages\Microsoft.AspNet.Webpages.3.2.9\lib\net45\System.Web.Webpages.Deployment.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Webpages.Razor">
      <HintPath>..\packages\Microsoft.AspNet.Webpages.3.2.9\lib\net45\System.Web.Webpages.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Helpers">
      <HintPath>..\packages\Microsoft.AspNet.Webpages.3.2.9\lib\net45\System.Web.Helpers.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Web.Infrastructure">
      <HintPath>..\packages\Microsoft.Web.Infrastructure.2.0.0\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Mvc">
      <HintPath>..\packages\Microsoft.AspNet.Mvc.5.2.9\lib\net45\System.Web.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.Formatting">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Client.5.2.9\lib\net45\System.Net.Http.Formatting.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Core.5.2.9\lib\net45\System.Web.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http.WebHost">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.WebHost.5.2.9\lib\net45\System.Web.Http.WebHost.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CodeDom.Providers.DotNetCompilerPlatform">
      <HintPath>..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\lib\net45\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Admin\AuditTrail.aspx" />
    <Content Include="Admin\BulkUpload.aspx" />
    <Content Include="Admin\Dashboard.aspx" />
    <Content Include="Admin\EditPost.aspx" />
    <Content Include="Admin\ManageBlog.aspx" />
    <Content Include="Admin\ManageChatLogs.aspx" />
    <Content Include="Admin\ManageContent.aspx" />
    <Content Include="Admin\ManageDiscounts.aspx" />
    <Content Include="Admin\ManageOrders.aspx" />
    <Content Include="Admin\ManageProducts.aspx" />
    <Content Include="Admin\ManageSettings.aspx" />
    <Content Include="Admin\ManageUsers.aspx" />
    <Content Include="Admin\OrderDetails.aspx" />
    <Content Include="Admin\Profile.aspx" />
    <Content Include="Admin\Reports.aspx" />
    <Content Include="App_Data\MediEase.mdf" />
    <Content Include="App_Data\MediEase_log.ldf">
      <DependentUpon>MediEase.mdf</DependentUpon>
    </Content>
    <Content Include="Blog.aspx" />
    <Content Include="Cart.aspx" />
    <Content Include="Chatbot.aspx" />
    <Content Include="Checkout.aspx" />
    <Content Include="Contact.aspx" />
    <Content Include="Content\Site.css" />
    <Content Include="Customer\Ask.aspx" />
    <Content Include="Customer\AutoRefills.aspx" />
    <Content Include="Customer\Dashboard.aspx" />
    <Content Include="Customer\Feedback.aspx" />
    <Content Include="Customer\Notifications.aspx" />
    <Content Include="Customer\OrderDetails.aspx" />
    <Content Include="Customer\Profile.aspx" />
    <Content Include="Customer\QuestionDetails.aspx" />
    <Content Include="Customer\Reminders.aspx" />
    <Content Include="Default.aspx" />
    <Content Include="FAQ.aspx" />
    <Content Include="ForgotPassword.aspx" />
    <Content Include="Global.asax" />
    <Content Include="Login.aspx" />
    <Content Include="OrderSuccess.aspx" />
    <Content Include="Pharmacist\AnswerQuestion.aspx" />
    <Content Include="Pharmacist\Dashboard.aspx" />
    <Content Include="Pharmacist\FeedbackDetails.aspx" />
    <Content Include="Pharmacist\Invoice.aspx" />
    <Content Include="Pharmacist\ManageFeedback.aspx" />
    <Content Include="Pharmacist\ManageOrders.aspx" />
    <Content Include="Pharmacist\ManagePricing.aspx" />
    <Content Include="Pharmacist\ManageProducts.aspx" />
    <Content Include="Pharmacist\ManageQuestions.aspx" />
    <Content Include="Pharmacist\ManageStock.aspx" />
    <Content Include="Pharmacist\OrderDetails.aspx" />
    <Content Include="Pharmacist\Profile.aspx" />
    <Content Include="Pharmacist\Reports.aspx" />
    <Content Include="Pharmacist\StockDetails.aspx" />
    <Content Include="Pharmacist\ViewDiscounts.aspx" />
    <Content Include="Post.aspx" />
    <Content Include="ProductDetails.aspx" />
    <Content Include="Register.aspx" />
    <Content Include="ResetPassword.aspx" />
    <Content Include="Shop.aspx" />
    <Content Include="ViewSwitcher.ascx" />
    <Content Include="Web.config" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Admin\AuditTrail.aspx.cs">
      <DependentUpon>AuditTrail.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Admin\AuditTrail.aspx.designer.cs">
      <DependentUpon>AuditTrail.aspx</DependentUpon>
    </Compile>
    <Compile Include="Admin\BulkUpload.aspx.cs">
      <DependentUpon>BulkUpload.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Admin\BulkUpload.aspx.designer.cs">
      <DependentUpon>BulkUpload.aspx</DependentUpon>
    </Compile>
    <Compile Include="Admin\Dashboard.aspx.cs">
      <DependentUpon>Dashboard.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Admin\Dashboard.aspx.designer.cs">
      <DependentUpon>Dashboard.aspx</DependentUpon>
    </Compile>
    <Compile Include="Admin\EditPost.aspx.cs">
      <DependentUpon>EditPost.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Admin\EditPost.aspx.designer.cs">
      <DependentUpon>EditPost.aspx</DependentUpon>
    </Compile>
    <Compile Include="Admin\ManageBlog.aspx.cs">
      <DependentUpon>ManageBlog.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Admin\ManageBlog.aspx.designer.cs">
      <DependentUpon>ManageBlog.aspx</DependentUpon>
    </Compile>
    <Compile Include="Admin\ManageChatLogs.aspx.cs">
      <DependentUpon>ManageChatLogs.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Admin\ManageChatLogs.aspx.designer.cs">
      <DependentUpon>ManageChatLogs.aspx</DependentUpon>
    </Compile>
    <Compile Include="Admin\ManageContent.aspx.cs">
      <DependentUpon>ManageContent.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Admin\ManageContent.aspx.designer.cs">
      <DependentUpon>ManageContent.aspx</DependentUpon>
    </Compile>
    <Compile Include="Admin\ManageDiscounts.aspx.cs">
      <DependentUpon>ManageDiscounts.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Admin\ManageDiscounts.aspx.designer.cs">
      <DependentUpon>ManageDiscounts.aspx</DependentUpon>
    </Compile>
    <Compile Include="Admin\ManageOrders.aspx.cs">
      <DependentUpon>ManageOrders.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Admin\ManageOrders.aspx.designer.cs">
      <DependentUpon>ManageOrders.aspx</DependentUpon>
    </Compile>
    <Compile Include="Admin\ManageProducts.aspx.cs">
      <DependentUpon>ManageProducts.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Admin\ManageProducts.aspx.designer.cs">
      <DependentUpon>ManageProducts.aspx</DependentUpon>
    </Compile>
    <Compile Include="Admin\ManageSettings.aspx.cs">
      <DependentUpon>ManageSettings.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Admin\ManageSettings.aspx.designer.cs">
      <DependentUpon>ManageSettings.aspx</DependentUpon>
    </Compile>
    <Compile Include="Admin\ManageUsers.aspx.cs">
      <DependentUpon>ManageUsers.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Admin\ManageUsers.aspx.designer.cs">
      <DependentUpon>ManageUsers.aspx</DependentUpon>
    </Compile>
    <Compile Include="Admin\OrderDetails.aspx.cs">
      <DependentUpon>OrderDetails.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Admin\OrderDetails.aspx.designer.cs">
      <DependentUpon>OrderDetails.aspx</DependentUpon>
    </Compile>
    <Compile Include="Admin\Profile.aspx.cs">
      <DependentUpon>Profile.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Admin\Profile.aspx.designer.cs">
      <DependentUpon>Profile.aspx</DependentUpon>
    </Compile>
    <Compile Include="Admin\Reports.aspx.cs">
      <DependentUpon>Reports.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Admin\Reports.aspx.designer.cs">
      <DependentUpon>Reports.aspx</DependentUpon>
    </Compile>
    <Compile Include="App_Start\RouteConfig.cs" />
    <Compile Include="App_Start\WebApiConfig.cs" />
    <Compile Include="AuditLogger.cs" />
    <Compile Include="Blog.aspx.cs">
      <DependentUpon>Blog.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Blog.aspx.designer.cs">
      <DependentUpon>Blog.aspx</DependentUpon>
    </Compile>
    <Compile Include="Cart.aspx.cs">
      <DependentUpon>Cart.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Cart.aspx.designer.cs">
      <DependentUpon>Cart.aspx</DependentUpon>
    </Compile>
    <Compile Include="CartItem.cs" />
    <Compile Include="Chatbot.aspx.cs">
      <DependentUpon>Chatbot.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Chatbot.aspx.designer.cs">
      <DependentUpon>Chatbot.aspx</DependentUpon>
    </Compile>
    <Compile Include="Checkout.aspx.cs">
      <DependentUpon>Checkout.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Checkout.aspx.designer.cs">
      <DependentUpon>Checkout.aspx</DependentUpon>
    </Compile>
    <Compile Include="Contact.aspx.cs">
      <DependentUpon>Contact.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Contact.aspx.designer.cs">
      <DependentUpon>Contact.aspx</DependentUpon>
    </Compile>
    <Compile Include="Customer\Ask.aspx.cs">
      <DependentUpon>Ask.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Customer\Ask.aspx.designer.cs">
      <DependentUpon>Ask.aspx</DependentUpon>
    </Compile>
    <Compile Include="Customer\AutoRefills.aspx.cs">
      <DependentUpon>AutoRefills.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Customer\AutoRefills.aspx.designer.cs">
      <DependentUpon>AutoRefills.aspx</DependentUpon>
    </Compile>
    <Compile Include="Customer\Dashboard.aspx.cs">
      <DependentUpon>Dashboard.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Customer\Dashboard.aspx.designer.cs">
      <DependentUpon>Dashboard.aspx</DependentUpon>
    </Compile>
    <Compile Include="Customer\Feedback.aspx.cs">
      <DependentUpon>Feedback.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Customer\Feedback.aspx.designer.cs">
      <DependentUpon>Feedback.aspx</DependentUpon>
    </Compile>
    <Compile Include="Customer\Notifications.aspx.cs">
      <DependentUpon>Notifications.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Customer\Notifications.aspx.designer.cs">
      <DependentUpon>Notifications.aspx</DependentUpon>
    </Compile>
    <Compile Include="Customer\OrderDetails.aspx.cs">
      <DependentUpon>OrderDetails.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Customer\OrderDetails.aspx.designer.cs">
      <DependentUpon>OrderDetails.aspx</DependentUpon>
    </Compile>
    <Compile Include="Customer\Profile.aspx.cs">
      <DependentUpon>Profile.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Customer\Profile.aspx.designer.cs">
      <DependentUpon>Profile.aspx</DependentUpon>
    </Compile>
    <Compile Include="Customer\QuestionDetails.aspx.cs">
      <DependentUpon>QuestionDetails.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Customer\QuestionDetails.aspx.designer.cs">
      <DependentUpon>QuestionDetails.aspx</DependentUpon>
    </Compile>
    <Compile Include="Customer\Reminders.aspx.cs">
      <DependentUpon>Reminders.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Customer\Reminders.aspx.designer.cs">
      <DependentUpon>Reminders.aspx</DependentUpon>
    </Compile>
    <Compile Include="Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Default.aspx.designer.cs">
      <DependentUpon>Default.aspx</DependentUpon>
    </Compile>
    <Compile Include="EmailService.cs" />
    <Compile Include="FAQ.aspx.cs">
      <DependentUpon>FAQ.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="FAQ.aspx.designer.cs">
      <DependentUpon>FAQ.aspx</DependentUpon>
    </Compile>
    <Compile Include="ForgotPassword.aspx.cs">
      <DependentUpon>ForgotPassword.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ForgotPassword.aspx.designer.cs">
      <DependentUpon>ForgotPassword.aspx</DependentUpon>
    </Compile>
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="Login.aspx.cs">
      <DependentUpon>Login.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Login.aspx.designer.cs">
      <DependentUpon>Login.aspx</DependentUpon>
    </Compile>
    <Compile Include="NotificationService.cs" />
    <Compile Include="OrderSuccess.aspx.cs">
      <DependentUpon>OrderSuccess.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="OrderSuccess.aspx.designer.cs">
      <DependentUpon>OrderSuccess.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pharmacist\AnswerQuestion.aspx.cs">
      <DependentUpon>AnswerQuestion.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pharmacist\AnswerQuestion.aspx.designer.cs">
      <DependentUpon>AnswerQuestion.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pharmacist\Dashboard.aspx.cs">
      <DependentUpon>Dashboard.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pharmacist\Dashboard.aspx.designer.cs">
      <DependentUpon>Dashboard.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pharmacist\FeedbackDetails.aspx.cs">
      <DependentUpon>FeedbackDetails.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pharmacist\FeedbackDetails.aspx.designer.cs">
      <DependentUpon>FeedbackDetails.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pharmacist\Invoice.aspx.cs">
      <DependentUpon>Invoice.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pharmacist\Invoice.aspx.designer.cs">
      <DependentUpon>Invoice.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pharmacist\ManageFeedback.aspx.cs">
      <DependentUpon>ManageFeedback.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pharmacist\ManageFeedback.aspx.designer.cs">
      <DependentUpon>ManageFeedback.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pharmacist\ManageOrders.aspx.cs">
      <DependentUpon>ManageOrders.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pharmacist\ManageOrders.aspx.designer.cs">
      <DependentUpon>ManageOrders.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pharmacist\ManagePricing.aspx.cs">
      <DependentUpon>ManagePricing.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pharmacist\ManagePricing.aspx.designer.cs">
      <DependentUpon>ManagePricing.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pharmacist\ManageProducts.aspx.cs">
      <DependentUpon>ManageProducts.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pharmacist\ManageProducts.aspx.designer.cs">
      <DependentUpon>ManageProducts.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pharmacist\ManageQuestions.aspx.cs">
      <DependentUpon>ManageQuestions.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pharmacist\ManageQuestions.aspx.designer.cs">
      <DependentUpon>ManageQuestions.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pharmacist\ManageStock.aspx.cs">
      <DependentUpon>ManageStock.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pharmacist\ManageStock.aspx.designer.cs">
      <DependentUpon>ManageStock.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pharmacist\OrderDetails.aspx.cs">
      <DependentUpon>OrderDetails.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pharmacist\OrderDetails.aspx.designer.cs">
      <DependentUpon>OrderDetails.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pharmacist\Profile.aspx.cs">
      <DependentUpon>Profile.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pharmacist\Profile.aspx.designer.cs">
      <DependentUpon>Profile.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pharmacist\Reports.aspx.cs">
      <DependentUpon>Reports.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pharmacist\Reports.aspx.designer.cs">
      <DependentUpon>Reports.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pharmacist\StockDetails.aspx.cs">
      <DependentUpon>StockDetails.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pharmacist\StockDetails.aspx.designer.cs">
      <DependentUpon>StockDetails.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pharmacist\ViewDiscounts.aspx.cs">
      <DependentUpon>ViewDiscounts.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pharmacist\ViewDiscounts.aspx.designer.cs">
      <DependentUpon>ViewDiscounts.aspx</DependentUpon>
    </Compile>
    <Compile Include="Post.aspx.cs">
      <DependentUpon>Post.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Post.aspx.designer.cs">
      <DependentUpon>Post.aspx</DependentUpon>
    </Compile>
    <Compile Include="ProductDetails.aspx.cs">
      <DependentUpon>ProductDetails.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ProductDetails.aspx.designer.cs">
      <DependentUpon>ProductDetails.aspx</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Register.aspx.cs">
      <DependentUpon>Register.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Register.aspx.designer.cs">
      <DependentUpon>Register.aspx</DependentUpon>
    </Compile>
    <Compile Include="ResetPassword.aspx.cs">
      <DependentUpon>ResetPassword.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ResetPassword.aspx.designer.cs">
      <DependentUpon>ResetPassword.aspx</DependentUpon>
    </Compile>
    <Compile Include="SettingsService.cs" />
    <Compile Include="Shop.aspx.cs">
      <DependentUpon>Shop.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Shop.aspx.designer.cs">
      <DependentUpon>Shop.aspx</DependentUpon>
    </Compile>
    <Compile Include="Site.Master.cs">
      <DependentUpon>Site.Master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Site.Master.designer.cs">
      <DependentUpon>Site.Master</DependentUpon>
    </Compile>
    <Compile Include="ViewSwitcher.ascx.cs">
      <DependentUpon>ViewSwitcher.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ViewSwitcher.ascx.designer.cs">
      <DependentUpon>ViewSwitcher.ascx</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\web.config" />
    <None Include="packages.config" />
    <Content Include="Site.Master" />
    <None Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
    <None Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Controllers\" />
    <Folder Include="Models\" />
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>51807</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>https://localhost:44348/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props'))" />
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>