﻿using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class Customer_Reminders : System.Web.UI.Page
{
    protected void Page_Load(object sender, EventArgs e)
    {
        // --- SECURITY CHECK ---
        if (Session["UserId"] == null || Session["UserRole"]?.ToString() != "Customer")
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        if (!IsPostBack)
        {
            BindReminders();
        }
    }

    private void BindReminders()
    {
        int userId = (int)Session["UserId"];
        string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
        using (SqlConnection con = new SqlConnection(connectionString))
        {
            string query = "SELECT ReminderId, Title, Description, NextReminderDate, Frequency, IsActive FROM HealthReminders WHERE UserId = @UserId ORDER BY IsActive DESC, NextReminderDate ASC";
            using (SqlDataAdapter sda = new SqlDataAdapter(query, con))
            {
                sda.SelectCommand.Parameters.AddWithValue("@UserId", userId);
                DataTable dt = new DataTable();
                sda.Fill(dt);
                rptReminders.DataSource = dt;
                rptReminders.DataBind();
            }
        }
    }

    protected void btnSaveReminder_Click(object sender, EventArgs e)
    {
        if (!Page.IsValid) return;

        int userId = (int)Session["UserId"];
        int reminderId = Convert.ToInt32(hdnReminderId.Value); // Will be 0 for new reminders

        DateTime reminderTime;
        if (!DateTime.TryParse(txtReminderTime.Text, out reminderTime))
        {
            ShowMessage("Invalid time format.", "error");
            return;
        }

        // Combine today's date with the selected time
        DateTime nextReminderDateTime = DateTime.Today.Add(reminderTime.TimeOfDay);
        // If the time has already passed for today, set the first reminder for tomorrow
        if (nextReminderDateTime < DateTime.Now)
        {
            nextReminderDateTime = nextReminderDateTime.AddDays(1);
        }

        string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
        using (SqlConnection con = new SqlConnection(connectionString))
        {
            string query = (reminderId == 0)
                ? @"INSERT INTO HealthReminders (UserId, Title, NextReminderDate, Frequency, IsActive, CreatedDate) 
                    VALUES (@UserId, @Title, @NextReminderDate, @Frequency, 1, GETDATE())"
                : @"UPDATE HealthReminders SET Title = @Title, NextReminderDate = @NextReminderDate, Frequency = @Frequency 
                    WHERE ReminderId = @ReminderId AND UserId = @UserId";

            using (SqlCommand cmd = new SqlCommand(query, con))
            {
                cmd.Parameters.AddWithValue("@UserId", userId);
                cmd.Parameters.AddWithValue("@Title", txtTitle.Text.Trim());
                cmd.Parameters.AddWithValue("@NextReminderDate", nextReminderDateTime);
                cmd.Parameters.AddWithValue("@Frequency", ddlFrequency.SelectedValue);
                if (reminderId != 0)
                {
                    cmd.Parameters.AddWithValue("@ReminderId", reminderId);
                }

                try
                {
                    con.Open();
                    cmd.ExecuteNonQuery();
                    ShowMessage("Reminder saved successfully!", "success");
                    ClearForm();
                    BindReminders();
                }
                catch (Exception ex)
                {
                    ShowMessage("Error saving reminder: " + ex.Message, "error");
                }
            }
        }
    }

    protected void rptReminders_ItemCommand(object source, RepeaterCommandEventArgs e)
    {
        int reminderId = Convert.ToInt32(e.CommandArgument);

        if (e.CommandName == "EditReminder")
        {
            LoadReminderForEdit(reminderId);
        }
        else if (e.CommandName == "DeleteReminder")
        {
            DeleteReminder(reminderId);
        }
    }

    private void LoadReminderForEdit(int reminderId)
    {
        litFormTitle.Text = "Edit Reminder";
        btnCancelEdit.Visible = true;
        hdnReminderId.Value = reminderId.ToString();

        int userId = (int)Session["UserId"];
        string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
        using (SqlConnection con = new SqlConnection(connectionString))
        {
            string query = "SELECT Title, NextReminderDate, Frequency FROM HealthReminders WHERE ReminderId = @ReminderId AND UserId = @UserId";
            using (SqlCommand cmd = new SqlCommand(query, con))
            {
                cmd.Parameters.AddWithValue("@ReminderId", reminderId);
                cmd.Parameters.AddWithValue("@UserId", userId);
                con.Open();
                SqlDataReader reader = cmd.ExecuteReader();
                if (reader.Read())
                {
                    txtTitle.Text = reader["Title"].ToString();
                    txtReminderTime.Text = Convert.ToDateTime(reader["NextReminderDate"]).ToString("HH:mm");
                    ddlFrequency.SelectedValue = reader["Frequency"].ToString();
                }
            }
        }
    }

    private void DeleteReminder(int reminderId)
    {
        int userId = (int)Session["UserId"];
        string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
        using (SqlConnection con = new SqlConnection(connectionString))
        {
            string query = "DELETE FROM HealthReminders WHERE ReminderId = @ReminderId AND UserId = @UserId";
            using (SqlCommand cmd = new SqlCommand(query, con))
            {
                cmd.Parameters.AddWithValue("@ReminderId", reminderId);
                cmd.Parameters.AddWithValue("@UserId", userId);
                con.Open();
                int rowsAffected = cmd.ExecuteNonQuery();
                if (rowsAffected > 0)
                {
                    ShowMessage("Reminder deleted successfully.", "success");
                }
                BindReminders();
            }
        }
    }

    protected void btnCancelEdit_Click(object sender, EventArgs e)
    {
        ClearForm();
    }

    private void ClearForm()
    {
        hdnReminderId.Value = "0";
        txtTitle.Text = "";
        txtReminderTime.Text = "";
        ddlFrequency.SelectedIndex = 0;
        litFormTitle.Text = "Add New Reminder";
        btnCancelEdit.Visible = false;
    }

    protected void btnLogout_Click(object sender, EventArgs e)
    {
        Session.Clear();
        Session.Abandon();
        Response.Redirect("~/Login.aspx");
    }

    private void ShowMessage(string message, string type)
    {
        string cssClass = (type == "success")
            ? "bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg relative mb-4"
            : "bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg relative mb-4";

        litMessage.Text = $"<div class='{cssClass}' role='alert'>{message}</div>";
    }
}
