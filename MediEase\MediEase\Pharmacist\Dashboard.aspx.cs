﻿/* FILE: Pharmacist/Dashboard.aspx.cs */
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace MediEase.Pharmacist
{
    public partial class Dashboard : Page
    {
        private static List<ListItem> orderStatusList;
        protected void Page_Load(object sender, EventArgs e)
        {
            if (Session["UserID"] == null || Session["RoleName"]?.ToString() != "Pharmacist") { Response.Redirect("~/Login.aspx"); return; }
            if (!IsPostBack) { LoadDashboardStats(); LoadOrderStatusList(); PopulateStatusFilter(); BindOrdersGrid(); }
        }
        private void LoadDashboardStats()
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "SELECT (SELECT COUNT(*) FROM Orders WHERE StatusID = 2) AS PendingOrders, (SELECT COUNT(*) FROM PharmacistQuestions WHERE IsAnswered = 0) AS PendingQuestions, (SELECT COUNT(*) FROM Feedback WHERE Status = 'Open') AS OpenFeedback;";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    con.Open(); SqlDataReader reader = cmd.ExecuteReader();
                    if (reader.Read()) { PendingOrdersLabel.Text = reader["PendingOrders"].ToString(); PendingQuestionsLabel.Text = reader["PendingQuestions"].ToString(); OpenFeedbackLabel.Text = reader["OpenFeedback"].ToString(); }
                }
            }
        }
        private void LoadOrderStatusList()
        {
            if (orderStatusList == null)
            {
                orderStatusList = new List<ListItem>();
                string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    const string query = "SELECT StatusID, StatusName FROM OrderStatus ORDER BY StatusID";
                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        con.Open(); SqlDataReader reader = cmd.ExecuteReader();
                        while (reader.Read()) { orderStatusList.Add(new ListItem(reader["StatusName"].ToString(), reader["StatusID"].ToString())); }
                    }
                }
            }
        }
        private void PopulateStatusFilter()
        {
            StatusFilter.DataSource = orderStatusList; StatusFilter.DataTextField = "Text"; StatusFilter.DataValueField = "Value"; StatusFilter.DataBind();
            StatusFilter.Items.Insert(0, new ListItem("All Statuses", "0"));
            ListItem defaultItem = StatusFilter.Items.FindByText("Under Review");
            if (defaultItem != null) { StatusFilter.SelectedValue = defaultItem.Value; }
        }
        private void BindOrdersGrid()
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                var queryBuilder = new StringBuilder("SELECT o.OrderID, up.FirstName + ' ' + up.LastName AS CustomerName, o.OrderDate, o.TotalAmount, os.StatusName FROM Orders o INNER JOIN UserProfiles up ON o.CustomerID = up.UserID INNER JOIN OrderStatus os ON o.StatusID = os.StatusID");
                var cmd = new SqlCommand();
                if (StatusFilter.SelectedValue != "0")
                {
                    queryBuilder.Append(" WHERE o.StatusID = @StatusID");
                    cmd.Parameters.AddWithValue("@StatusID", StatusFilter.SelectedValue);
                }
                queryBuilder.Append(" ORDER BY o.OrderDate DESC");
                cmd.CommandText = queryBuilder.ToString(); cmd.Connection = con;
                con.Open(); SqlDataAdapter sda = new SqlDataAdapter(cmd); DataTable dt = new DataTable(); sda.Fill(dt);
                OrdersGrid.DataSource = dt; OrdersGrid.DataBind();
            }
        }
        protected void StatusFilter_SelectedIndexChanged(object sender, EventArgs e) { OrdersGrid.PageIndex = 0; BindOrdersGrid(); }
        protected void OrdersGrid_PageIndexChanging(object sender, GridViewPageEventArgs e) { OrdersGrid.PageIndex = e.NewPageIndex; BindOrdersGrid(); }
        public string GetStatusBadgeClass(string status) { switch (status.ToLower()) { case "pending payment": case "under review": return "badge-warning"; case "processing": return "badge-info"; case "shipped": case "delivered": return "badge-success"; case "cancelled": case "rejected": return "badge-danger"; default: return "badge-secondary"; } }
    }
}