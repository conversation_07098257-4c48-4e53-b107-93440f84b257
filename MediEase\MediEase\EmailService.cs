﻿/*
 =======================================================================
 FILE: EmailService.cs (Final Version with Reminder Logic)
 =======================================================================
*/
using System;
using System.Net;
using System.Net.Mail;

namespace MediEase
{
    public static class EmailService
    {
        // IMPORTANT: Replace with your own Gmail address and a Google "App Password"
        private static readonly string FromEmail = "<EMAIL>";
        private static readonly string FromPassword = "ojtl edhu fqxy tevc";

        /// <summary>
        /// Sends an email to a customer when their order status is updated.
        /// </summary>
        public static void SendOrderStatusUpdateEmail(string toEmail, string customerName, int orderId, string newStatus)
        {
            string subject = $"Update on your MediEase Order #{orderId}";
            string body = $"<p>Hi {customerName},</p><p>The status of your order #{orderId} has been updated to: <strong>{newStatus}</strong>.</p><p>Thank you for shopping with MediEase!</p>";
            if (newStatus == "Rejected")
            {
                body = $"<p>Hi {customerName},</p><p>We're writing to inform you about an issue with your order #{orderId}. The order has been marked as 'Rejected'. Please contact support for more information.</p>";
            }
            SendEmail(toEmail, subject, body);
        }

        /// <summary>
        /// Sends an email to a customer to confirm a new medication reminder has been set.
        /// </summary>
        public static void SendNewReminderConfirmationEmail(string toEmail, string customerName, string productName, string time, string frequency)
        {
            string subject = "MediEase - Your Medication Reminder has been set!";
            string body = $@"
                <p>Hi {customerName},</p>
                <p>This is a confirmation that you have successfully set a new medication reminder.</p>
                <hr>
                <p><strong>Medication:</strong> {productName}</p>
                <p><strong>Time:</strong> {time}</p>
                <p><strong>Frequency:</strong> {frequency}</p>
                <hr>
                <p>Please remember, this is a confirmation email. The system will not send daily emails at the reminder time.</p>
            ";
            SendEmail(toEmail, subject, body);
        }

        private static void SendEmail(string toEmail, string subject, string htmlBody)
        {
            try
            {
                MailMessage mailMessage = new MailMessage { From = new MailAddress(FromEmail, "MediEase Pharmacy"), Subject = subject, IsBodyHtml = true, Body = htmlBody };
                mailMessage.To.Add(toEmail);
                SmtpClient smtpClient = new SmtpClient("smtp.gmail.com", 587) { EnableSsl = true, Credentials = new NetworkCredential(FromEmail, FromPassword) };
                smtpClient.Send(mailMessage);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("Email Send Error: " + ex.ToString());
            }
        }
    }
}
