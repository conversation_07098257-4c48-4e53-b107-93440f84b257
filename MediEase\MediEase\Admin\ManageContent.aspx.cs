﻿/* FILE: Admin/ManageContent.aspx.cs */
using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace MediEase.Admin
{
    public partial class ManageContent : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (Session["UserID"] == null || Session["RoleName"]?.ToString() != "Admin")
            {
                Response.Redirect("~/Login.aspx");
                return;
            }

            if (!IsPostBack)
            {
                PopulatePageSelector();
                LoadPageContent();
            }
        }

        private void PopulatePageSelector()
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "SELECT PageName FROM CMSContent ORDER BY PageName";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    try
                    {
                        con.Open();
                        SqlDataAdapter sda = new SqlDataAdapter(cmd);
                        DataTable dt = new DataTable();
                        sda.Fill(dt);
                        PageSelector.DataSource = dt;
                        PageSelector.DataTextField = "PageName";
                        PageSelector.DataValueField = "PageName";
                        PageSelector.DataBind();
                    }
                    catch (Exception ex) { System.Diagnostics.Debug.WriteLine("Error populating page selector: " + ex.Message); }
                }
            }
        }

        private void LoadPageContent()
        {
            if (PageSelector.SelectedItem == null) return;
            string selectedPage = PageSelector.SelectedItem.Value;
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "SELECT ContentBody FROM CMSContent WHERE PageName = @PageName";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@PageName", selectedPage);
                    try
                    {
                        con.Open();
                        object result = cmd.ExecuteScalar();
                        if (result != null) { ContentEditor.Text = result.ToString(); }
                    }
                    catch (Exception ex) { System.Diagnostics.Debug.WriteLine("Error loading page content: " + ex.Message); }
                }
            }
        }

        protected void PageSelector_SelectedIndexChanged(object sender, EventArgs e)
        {
            LoadPageContent();
            MessagePlaceholder.Visible = false;
        }

        protected void SaveChangesButton_Click(object sender, EventArgs e)
        {
            if (PageSelector.SelectedItem == null) return;
            string selectedPage = PageSelector.SelectedItem.Value;
            string newContent = ContentEditor.Text;
            int adminUserId = Convert.ToInt32(Session["UserID"]);
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "UPDATE CMSContent SET ContentBody = @ContentBody, LastUpdatedByAdminID = @AdminID, LastUpdatedDate = GETDATE() WHERE PageName = @PageName";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@ContentBody", newContent);
                    cmd.Parameters.AddWithValue("@AdminID", adminUserId);
                    cmd.Parameters.AddWithValue("@PageName", selectedPage);
                    try
                    {
                        con.Open();
                        int rowsAffected = cmd.ExecuteNonQuery();
                        if (rowsAffected > 0) { MessagePlaceholder.Visible = true; }
                    }
                    catch (Exception ex) { System.Diagnostics.Debug.WriteLine("Error saving page content: " + ex.Message); }
                }
            }
        }
    }
}