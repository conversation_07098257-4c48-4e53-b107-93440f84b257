﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="Reminders.aspx.cs" Inherits="Customer_Reminders" %>

<!DOCTYPE html>
<html lang="en">
<head runat="server">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Health Reminders - MediEase</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <style>
        body { font-family: 'Inter', sans-serif; }
        .sidebar-link { display: flex; align-items: center; padding: 0.75rem 1rem; border-radius: 0.5rem; transition: background-color 0.2s, color 0.2s; }
        .sidebar-link:hover, .sidebar-link.active { background-color: #0d9488; color: white; }
        .sidebar-link i { width: 1.25rem; margin-right: 0.75rem; }
        .validation-error { color: #D32F2F; font-size: 0.875rem; margin-top: 0.25rem; }
    </style>
</head>
<body class="bg-gray-100">
    <form id="form1" runat="server">
        <div class="flex h-screen bg-gray-100">
            <!-- Sidebar -->
            <aside class="w-64 bg-teal-600 text-white flex-col p-4 hidden md:flex">
                <div class="flex items-center space-x-2 pb-4 border-b border-teal-500">
                    <i class="fas fa-pills text-3xl"></i><span class="text-2xl font-bold">MediEase</span>
                </div>
                <nav class="mt-6 flex-grow">
                    <a href="Dashboard.aspx" class="sidebar-link"><i class="fas fa-tachometer-alt fa-fw"></i><span>Dashboard</span></a>
                    <a href="Profile.aspx" class="sidebar-link"><i class="fas fa-user-edit fa-fw"></i><span>My Profile</span></a>
                    <a href="OrderHistory.aspx" class="sidebar-link"><i class="fas fa-history fa-fw"></i><span>Order History</span></a>
                    <a href="Prescriptions.aspx" class="sidebar-link"><i class="fas fa-file-prescription fa-fw"></i><span>Prescriptions</span></a>
                    <a href="Reminders.aspx" class="sidebar-link active"><i class="fas fa-bell fa-fw"></i><span>Reminders</span></a>
                </nav>
                <div class="pt-4 border-t border-teal-500">
                    <asp:LinkButton ID="btnLogout" runat="server" OnClick="btnLogout_Click" CssClass="sidebar-link w-full"><i class="fas fa-sign-out-alt fa-fw"></i><span>Logout</span></asp:LinkButton>
                </div>
            </aside>

            <!-- Main Content -->
            <main class="flex-1 flex flex-col">
                <header class="bg-white shadow-sm p-4">
                    <h1 class="text-2xl font-bold text-gray-800">Health Reminders</h1>
                </header>

                <div class="flex-1 p-6 overflow-y-auto">
                    <!-- Server Message -->
                    <asp:Literal ID="litMessage" runat="server"></asp:Literal>

                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <!-- Left Column: Add/Edit Form -->
                        <div class="lg:col-span-1 bg-white p-8 rounded-lg shadow h-fit">
                            <h2 class="text-xl font-bold text-gray-800 mb-6">
                                <asp:Literal ID="litFormTitle" runat="server" Text="Add New Reminder"></asp:Literal>
                            </h2>
                             <asp:HiddenField ID="hdnReminderId" runat="server" Value="0" />
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Title *</label>
                                    <asp:TextBox ID="txtTitle" runat="server" CssClass="mt-1 block w-full input-style" placeholder="e.g., Take Vitamin D"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvTitle" runat="server" ControlToValidate="txtTitle" ErrorMessage="Title is required." CssClass="validation-error" Display="Dynamic"></asp:RequiredFieldValidator>
                                </div>
                                 <div>
                                    <label class="block text-sm font-medium text-gray-700">Reminder Time *</label>
                                    <asp:TextBox ID="txtReminderTime" runat="server" TextMode="Time" CssClass="mt-1 block w-full input-style"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvReminderTime" runat="server" ControlToValidate="txtReminderTime" ErrorMessage="Time is required." CssClass="validation-error" Display="Dynamic"></asp:RequiredFieldValidator>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Frequency</label>
                                    <asp:DropDownList ID="ddlFrequency" runat="server" CssClass="mt-1 block w-full input-style">
                                        <asp:ListItem Text="Daily" Value="Daily" Selected="True"></asp:ListItem>
                                        <asp:ListItem Text="Weekly" Value="Weekly"></asp:ListItem>
                                        <asp:ListItem Text="Monthly" Value="Monthly"></asp:ListItem>
                                        <asp:ListItem Text="As Needed" Value="As Needed"></asp:ListItem>
                                    </asp:DropDownList>
                                </div>
                                <div class="pt-2">
                                    <asp:Button ID="btnSaveReminder" runat="server" Text="Save Reminder" OnClick="btnSaveReminder_Click" CssClass="w-full justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-teal-600 hover:bg-teal-700"/>
                                    <asp:Button ID="btnCancelEdit" runat="server" Text="Cancel Edit" OnClick="btnCancelEdit_Click" CausesValidation="false" Visible="false" CssClass="w-full justify-center mt-2 py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"/>
                                </div>
                            </div>
                        </div>

                        <!-- Right Column: Reminders List -->
                        <div class="lg:col-span-2">
                            <asp:Repeater ID="rptReminders" runat="server" OnItemCommand="rptReminders_ItemCommand">
                                <HeaderTemplate>
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                </HeaderTemplate>
                                <ItemTemplate>
                                     <div class="bg-white p-6 rounded-lg shadow flex">
                                         <div class="flex-grow">
                                             <div class="flex justify-between items-start">
                                                 <h3 class="text-lg font-bold text-gray-900"><%# Eval("Title") %></h3>
                                                 <span class='px-2 inline-flex text-xs leading-5 font-semibold rounded-full <%# (bool)Eval("IsActive") ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800" %>'>
                                                     <%# (bool)Eval("IsActive") ? "Active" : "Inactive" %>
                                                 </span>
                                             </div>
                                             <p class="text-sm text-gray-500 mt-1">Next on: <span class="font-semibold"><%# Convert.ToDateTime(Eval("NextReminderDate")).ToString("MMM dd, yyyy 'at' hh:mm tt") %></span></p>
                                             <p class="text-sm text-gray-500">Frequency: <span class="font-semibold"><%# Eval("Frequency") %></span></p>
                                         </div>
                                         <div class="flex flex-col space-y-2 ml-4">
                                             <asp:LinkButton ID="btnEdit" runat="server" CommandName="EditReminder" CommandArgument='<%# Eval("ReminderId") %>' CssClass="text-gray-400 hover:text-teal-600" ToolTip="Edit"><i class="fas fa-pencil-alt"></i></asp:LinkButton>
                                             <asp:LinkButton ID="btnDelete" runat="server" CommandName="DeleteReminder" CommandArgument='<%# Eval("ReminderId") %>' OnClientClick='return confirm("Are you sure you want to delete this reminder?");' CssClass="text-gray-400 hover:text-red-600" ToolTip="Delete"><i class="fas fa-trash-alt"></i></asp:LinkButton>
                                         </div>
                                     </div>
                                </ItemTemplate>
                                <FooterTemplate>
                                    </div>
                                    <%# (rptReminders.Items.Count == 0) ? "<div class='text-center py-12 bg-white rounded-lg shadow'><p class='text-gray-500'>You have no active reminders.</p></div>" : "" %>
                                </FooterTemplate>
                            </asp:Repeater>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </form>
</body>
</html>
