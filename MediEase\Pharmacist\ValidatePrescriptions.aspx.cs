﻿using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web.UI;
using System.Text;
using System.Web.UI.WebControls;

public partial class Pharmacist_ValidatePrescriptions : System.Web.UI.Page
{
    protected void Page_Load(object sender, EventArgs e)
    {
        // --- SECURITY CHECK ---
        if (Session["UserId"] == null || Session["UserRole"]?.ToString() != "Pharmacist")
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        if (!IsPostBack)
        {
            BindPrescriptionsGrid();
        }
    }

    private void BindPrescriptionsGrid()
    {
        string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
        using (SqlConnection con = new SqlConnection(connectionString))
        {
            StringBuilder queryBuilder = new StringBuilder(@"
                SELECT 
                    p.PrescriptionId, p.PrescriptionNumber, p.PrescriptionDate, p.Doctor<PERSON>ame, p.Status,
                    u.FirstName + ' ' + u.LastName AS CustomerName
                FROM Prescriptions p
                INNER JOIN Users u ON p.UserId = u.UserId
            ");

            SqlCommand cmd = new SqlCommand();

            // --- Build Where Clause for Filtering ---
            queryBuilder.Append(" WHERE 1=1 ");

            if (!string.IsNullOrWhiteSpace(txtSearch.Text))
            {
                queryBuilder.Append(" AND (p.PrescriptionNumber LIKE @Search OR u.FirstName LIKE @Search OR u.LastName LIKE @Search)");
                cmd.Parameters.AddWithValue("@Search", "%" + txtSearch.Text.Trim() + "%");
            }

            if (!string.IsNullOrEmpty(ddlStatusFilter.SelectedValue))
            {
                queryBuilder.Append(" AND p.Status = @Status");
                cmd.Parameters.AddWithValue("@Status", ddlStatusFilter.SelectedValue);
            }

            // Order by status first to see 'Pending' items on top
            queryBuilder.Append(" ORDER BY CASE p.Status WHEN 'Pending' THEN 1 WHEN 'Verified' THEN 2 ELSE 3 END, p.PrescriptionDate DESC");

            cmd.CommandText = queryBuilder.ToString();
            cmd.Connection = con;

            using (SqlDataAdapter sda = new SqlDataAdapter(cmd))
            {
                DataTable dt = new DataTable();
                sda.Fill(dt);
                gvPrescriptions.DataSource = dt;
                gvPrescriptions.DataBind();
            }
        }
    }

    protected void btnSearch_Click(object sender, EventArgs e)
    {
        gvPrescriptions.PageIndex = 0;
        BindPrescriptionsGrid();
    }

    protected void btnClear_Click(object sender, EventArgs e)
    {
        txtSearch.Text = string.Empty;
        ddlStatusFilter.SelectedValue = "Pending"; // Default back to pending
        gvPrescriptions.PageIndex = 0;
        BindPrescriptionsGrid();
    }

    protected void gvPrescriptions_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        gvPrescriptions.PageIndex = e.NewPageIndex;
        BindPrescriptionsGrid();
    }

    protected void btnLogout_Click(object sender, EventArgs e)
    {
        Session.Clear();
        Session.Abandon();
        Response.Redirect("~/Login.aspx");
    }

    public string GetStatusClass(string status)
    {
        switch (status.ToLower())
        {
            case "pending": return "bg-yellow-100 text-yellow-800";
            case "verified": return "bg-green-100 text-green-800";
            case "rejected": return "bg-red-100 text-red-800";
            default: return "bg-gray-100 text-gray-800";
        }
    }
}
