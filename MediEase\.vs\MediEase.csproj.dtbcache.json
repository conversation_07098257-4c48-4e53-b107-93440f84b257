{"RootPath": "C:\\Users\\<USER>\\Desktop\\Project\\MediEase", "ProjectFileName": "MediEase.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "Admin\\Dashboard.aspx.cs"}, {"SourceFile": "Admin\\Dashboard.aspx.designer.cs"}, {"SourceFile": "Controls\\AIChatbot.ascx.cs"}, {"SourceFile": "Controls\\AIChatbot.ascx.designer.cs"}, {"SourceFile": "Customer\\AIRecommendations.aspx.cs"}, {"SourceFile": "Customer\\AIRecommendations.aspx.designer.cs"}, {"SourceFile": "Cart.aspx.cs"}, {"SourceFile": "Cart.aspx.designer.cs"}, {"SourceFile": "Customer\\Profile.aspx.cs"}, {"SourceFile": "Customer\\Profile.aspx.designer.cs"}, {"SourceFile": "Customer\\UploadPrescription.aspx.cs"}, {"SourceFile": "Customer\\UploadPrescription.aspx.designer.cs"}, {"SourceFile": "Customer\\PriceComparison.aspx.cs"}, {"SourceFile": "Customer\\PriceComparison.aspx.designer.cs"}, {"SourceFile": "Customer\\LoyaltyProgram.aspx.cs"}, {"SourceFile": "Customer\\LoyaltyProgram.aspx.designer.cs"}, {"SourceFile": "Customer\\OrderTracking.aspx.cs"}, {"SourceFile": "Customer\\OrderTracking.aspx.designer.cs"}, {"SourceFile": "Checkout.aspx.cs"}, {"SourceFile": "Checkout.aspx.designer.cs"}, {"SourceFile": "Pharmacist\\Dashboard.aspx.cs"}, {"SourceFile": "Pharmacist\\Dashboard.aspx.designer.cs"}, {"SourceFile": "Pharmacist\\PrescriptionValidation.aspx.cs"}, {"SourceFile": "Pharmacist\\PrescriptionValidation.aspx.designer.cs"}, {"SourceFile": "Pharmacist\\OrderManagement.aspx.cs"}, {"SourceFile": "Pharmacist\\OrderManagement.aspx.designer.cs"}, {"SourceFile": "Pharmacist\\InventoryManagement.aspx.cs"}, {"SourceFile": "Pharmacist\\InventoryManagement.aspx.designer.cs"}, {"SourceFile": "Admin\\UserManagement.aspx.cs"}, {"SourceFile": "Admin\\UserManagement.aspx.designer.cs"}, {"SourceFile": "Customer\\FamilyProfiles.aspx.cs"}, {"SourceFile": "Customer\\FamilyProfiles.aspx.designer.cs"}, {"SourceFile": "Default.aspx.cs"}, {"SourceFile": "Default.aspx.designer.cs"}, {"SourceFile": "GuestSearch.aspx.cs"}, {"SourceFile": "GuestSearch.aspx.designer.cs"}, {"SourceFile": "Login.aspx.cs"}, {"SourceFile": "Login.aspx.designer.cs"}, {"SourceFile": "OrderConfirmation.aspx.cs"}, {"SourceFile": "OrderConfirmation.aspx.designer.cs"}, {"SourceFile": "Register.aspx.cs"}, {"SourceFile": "Prescription.aspx.cs"}, {"SourceFile": "Prescription.aspx.designer.cs"}, {"SourceFile": "Register.aspx.designer.cs"}, {"SourceFile": "Medicines.aspx.cs"}, {"SourceFile": "Medicines.aspx.designer.cs"}, {"SourceFile": "About.aspx.cs"}, {"SourceFile": "About.aspx.designer.cs"}, {"SourceFile": "Contact.aspx.cs"}, {"SourceFile": "Contact.aspx.designer.cs"}, {"SourceFile": "FAQ.aspx.cs"}, {"SourceFile": "FAQ.aspx.designer.cs"}, {"SourceFile": "ForgotPassword.aspx.cs"}, {"SourceFile": "ForgotPassword.aspx.designer.cs"}, {"SourceFile": "ResetPassword.aspx.cs"}, {"SourceFile": "ResetPassword.aspx.designer.cs"}, {"SourceFile": "Customer\\Dashboard.aspx.cs"}, {"SourceFile": "Customer\\Dashboard.aspx.designer.cs"}, {"SourceFile": "Site.Master.cs"}, {"SourceFile": "Site.Master.designer.cs"}, {"SourceFile": "Global.asax.cs"}, {"SourceFile": "Handlers\\ChatHandler.ashx.cs"}, {"SourceFile": "Models\\User.cs"}, {"SourceFile": "Models\\Medicine.cs"}, {"SourceFile": "Models\\Order.cs"}, {"SourceFile": "Models\\Prescription.cs"}, {"SourceFile": "Models\\AdditionalModels.cs"}, {"SourceFile": "DAL\\MediEaseContext.cs"}, {"SourceFile": "DAL\\DirectDatabaseAccess.cs"}, {"SourceFile": "Utilities\\AIHelper.cs"}, {"SourceFile": "Utilities\\DatabaseInitializer.cs"}, {"SourceFile": "Utilities\\SecurityHelper.cs"}, {"SourceFile": "Utilities\\ErrorHandlingModule.cs"}, {"SourceFile": "Utilities\\PaymentService.cs"}, {"SourceFile": "Admin\\SystemConfiguration.aspx.cs"}, {"SourceFile": "Admin\\BulkUpload.aspx.cs"}, {"SourceFile": "Customer\\HealthReminders.aspx.cs"}, {"SourceFile": "Pharmacist\\InvoiceGeneration.aspx.cs"}, {"SourceFile": "Pharmacist\\InvoiceGeneration.aspx.designer.cs"}, {"SourceFile": "Pharmacist\\PriceManagement.aspx.cs"}, {"SourceFile": "Pharmacist\\PriceManagement.aspx.designer.cs"}, {"SourceFile": "Pharmacist\\Reports.aspx.cs"}, {"SourceFile": "Pharmacist\\Reports.aspx.designer.cs"}, {"SourceFile": "Admin\\BackupRestore.aspx.cs"}, {"SourceFile": "Admin\\BackupRestore.aspx.designer.cs"}, {"SourceFile": "Admin\\ChatbotManagement.aspx.designer.cs"}, {"SourceFile": "Admin\\MedicineManagement.aspx.designer.cs"}, {"SourceFile": "Customer\\AccessibilitySettings.aspx.designer.cs"}, {"SourceFile": "Customer\\AutoRefill.aspx.cs"}, {"SourceFile": "Customer\\AutoRefill.aspx.designer.cs"}, {"SourceFile": "Customer\\FeedbackReviews.aspx.cs"}, {"SourceFile": "Customer\\FeedbackReviews.aspx.designer.cs"}, {"SourceFile": "Customer\\HealthReminders.aspx.designer.cs"}, {"SourceFile": "Shared\\InternalMessaging.aspx.cs"}, {"SourceFile": "Shared\\InternalMessaging.aspx.designer.cs"}, {"SourceFile": "Properties\\AssemblyInfo.cs"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.8.AssemblyAttributes.cs"}], "References": [{"Reference": "C:\\Users\\<USER>\\Desktop\\Project\\packages\\BCrypt.Net-Next.4.0.3\\lib\\net48\\BCrypt.Net-Next.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Project\\packages\\EntityFramework.6.0.0\\lib\\net45\\EntityFramework.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Project\\packages\\EntityFramework.6.0.0\\lib\\net45\\EntityFramework.SqlServer.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\Microsoft.CSharp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Project\\packages\\Microsoft.AspNet.ScriptManager.MSAjax.5.0.0\\lib\\net45\\Microsoft.ScriptManager.MSAjax.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Project\\packages\\Microsoft.Web.Infrastructure.*******\\lib\\net40\\Microsoft.Web.Infrastructure.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Project\\packages\\Newtonsoft.Json.13.0.3\\lib\\net45\\Newtonsoft.Json.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.ComponentModel.DataAnnotations.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Configuration.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Data.DataSetExtensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Drawing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.EnterpriseServices.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Web.ApplicationServices.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Web.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Web.DynamicData.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Web.Entity.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Web.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Web.Services.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Xml.Linq.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "C:\\Users\\<USER>\\Desktop\\Project\\MediEase\\bin\\MediEase.dll", "OutputItemRelativePath": "MediEase.dll"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}