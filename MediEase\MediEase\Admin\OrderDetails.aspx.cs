﻿/* FILE: Admin/OrderDetails.aspx.cs */
using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace MediEase.Admin
{
    public partial class OrderDetails : Page
    {
        private int orderId;

        protected void Page_Load(object sender, EventArgs e)
        {
            if (Session["UserID"] == null || Session["RoleName"]?.ToString() != "Admin") { Response.Redirect("~/Login.aspx"); return; }
            if (!int.TryParse(Request.QueryString["OrderID"], out orderId)) { ShowErrorView(); return; }
            if (!IsPostBack) { LoadOrderDetails(); }
        }

        private void LoadOrderDetails()
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                try
                {
                    con.Open();
                    const string orderQuery = "SELECT o.OrderDate, o.TotalAmount, o.ShippingAddress, os.StatusName, up.FirstName, up.LastName, up.PhoneNumber, pay.TransactionID as ScreenshotPath, pres.FilePath as PrescriptionPath FROM Orders o INNER JOIN OrderStatus os ON o.StatusID = os.StatusID INNER JOIN UserProfiles up ON o.CustomerID = up.UserID LEFT JOIN Payments pay ON o.OrderID = pay.OrderID LEFT JOIN Prescriptions pres ON o.OrderID = pres.OrderID WHERE o.OrderID = @OrderID;";
                    bool orderFound = false;
                    using (SqlCommand cmd = new SqlCommand(orderQuery, con))
                    {
                        cmd.Parameters.AddWithValue("@OrderID", orderId);
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                orderFound = true;
                                OrderIDLabel.Text = orderId.ToString();
                                OrderDateLabel.Text = Convert.ToDateTime(reader["OrderDate"]).ToString("MMMM dd, yyyy");
                                OrderTotalLabel.Text = Convert.ToDecimal(reader["TotalAmount"]).ToString("C");
                                ShippingAddressLabel.Text = Server.HtmlEncode(reader["ShippingAddress"].ToString()).Replace("\n", "<br/>");
                                CustomerNameLabel.Text = $"{reader["FirstName"]} {reader["LastName"]}";
                                CustomerPhoneLabel.Text = Server.HtmlEncode(reader["PhoneNumber"].ToString());
                                PaymentScreenshotImage.ImageUrl = ResolveUrl(reader["ScreenshotPath"].ToString());
                                string prescriptionPath = reader["PrescriptionPath"] as string;
                                if (!string.IsNullOrEmpty(prescriptionPath)) { PrescriptionView.Visible = true; PrescriptionImage.ImageUrl = ResolveUrl(prescriptionPath); PrescriptionLink.HRef = ResolveUrl(prescriptionPath); }
                                string status = reader["StatusName"].ToString();
                                OrderStatusLabel.Text = status;
                                OrderStatusLabel.CssClass = "badge badge-pill " + GetStatusBadgeClass(status);
                            }
                        }
                    }
                    if (orderFound)
                    {
                        const string itemsQuery = "SELECT p.Name, oi.Quantity, oi.PricePerUnit FROM OrderItems oi INNER JOIN Products p ON oi.ProductID = p.ProductID WHERE oi.OrderID = @OrderID;";
                        using (SqlCommand cmd = new SqlCommand(itemsQuery, con))
                        {
                            cmd.Parameters.AddWithValue("@OrderID", orderId);
                            SqlDataAdapter sda = new SqlDataAdapter(cmd);
                            DataTable dt = new DataTable();
                            sda.Fill(dt);
                            OrderItemsGrid.DataSource = dt;
                            OrderItemsGrid.DataBind();
                        }
                        OrderDetailsView.Visible = true;
                        ErrorView.Visible = false;
                    }
                    else { ShowErrorView(); }
                }
                catch (Exception ex) { System.Diagnostics.Debug.WriteLine("Error loading admin order details: " + ex.Message); ShowErrorView(); }
            }
        }

        private void ShowErrorView() { OrderDetailsView.Visible = false; ErrorView.Visible = true; }

        public string GetStatusBadgeClass(string status)
        {
            switch (status.ToLower())
            {
                case "pending payment": case "under review": return "badge-warning";
                case "processing": return "badge-info";
                case "shipped": case "delivered": return "badge-success";
                case "cancelled": case "rejected": return "badge-danger";
                default: return "badge-secondary";
            }
        }
    }
}