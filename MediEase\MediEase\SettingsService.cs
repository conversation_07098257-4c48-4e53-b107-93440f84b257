﻿/*
 =======================================================================
 FILE: SettingsService.cs (Final Version)
 PURPOSE: A reusable helper class to fetch configuration values from
          the SystemSettings table in the database.
 =======================================================================
*/
using System;
using System.Configuration;
using System.Data.SqlClient;

namespace MediEase
{
    public static class SettingsService
    {
        /// <summary>
        /// Gets a setting value from the database as a string.
        /// </summary>
        /// <param name="settingName">The name of the setting to retrieve.</param>
        /// <param name="defaultValue">The value to return if the setting is not found.</param>
        /// <returns>The setting value as a string.</returns>
        public static string GetStringSetting(string settingName, string defaultValue)
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "SELECT SettingValue FROM SystemSettings WHERE SettingName = @SettingName";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@SettingName", settingName);
                    try
                    {
                        con.Open();
                        object result = cmd.ExecuteScalar();
                        if (result != null && result != DBNull.Value)
                        {
                            return result.ToString();
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Error getting setting '{settingName}': {ex.Message}");
                    }
                }
            }
            return defaultValue;
        }

        /// <summary>
        /// Gets a setting value from the database as a decimal.
        /// </summary>
        public static decimal GetDecimalSetting(string settingName, decimal defaultValue)
        {
            string settingValue = GetStringSetting(settingName, defaultValue.ToString());
            if (decimal.TryParse(settingValue, out decimal value))
            {
                return value;
            }
            return defaultValue;
        }
    }
}