﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="PrescriptionDetail.aspx.cs" Inherits="Pharmacist_PrescriptionDetail" %>

<!DOCTYPE html>
<html lang="en">
<head runat="server">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prescription Detail - MediEase Pharmacist</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <style>
        body { font-family: 'Inter', sans-serif; }
        .sidebar-link { display: flex; align-items: center; padding: 0.75rem 1rem; border-radius: 0.5rem; transition: background-color 0.2s, color 0.2s; }
        .sidebar-link:hover, .sidebar-link.active { background-color: #0d9488; color: white; }
        .sidebar-link i { width: 1.25rem; margin-right: 0.75rem; }
    </style>
</head>
<body class="bg-gray-100">
    <form id="form1" runat="server">
        <div class="flex h-screen bg-gray-100">
            <!-- Sidebar -->
            <aside class="w-64 bg-teal-600 text-white flex-col p-4 hidden md:flex">
                <div class="flex items-center space-x-2 pb-4 border-b border-teal-500">
                    <i class="fas fa-pills text-3xl"></i>
                    <span class="text-2xl font-bold">MediEase</span>
                </div>
                <nav class="mt-6 flex-grow">
                    <a href="Dashboard.aspx" class="sidebar-link"><i class="fas fa-tachometer-alt fa-fw"></i><span>Dashboard</span></a>
                    <a href="ManageOrders.aspx" class="sidebar-link"><i class="fas fa-tasks fa-fw"></i><span>Manage Orders</span></a>
                    <a href="ManageStock.aspx" class="sidebar-link"><i class="fas fa-boxes fa-fw"></i><span>Manage Stock</span></a>
                    <a href="ValidatePrescriptions.aspx" class="sidebar-link active"><i class="fas fa-file-prescription fa-fw"></i><span>Validate Prescriptions</span></a>
                </nav>
                <div class="pt-4 border-t border-teal-500">
                    <asp:LinkButton ID="btnLogout" runat="server" OnClick="btnLogout_Click" CssClass="sidebar-link w-full"><i class="fas fa-sign-out-alt fa-fw"></i><span>Logout</span></asp:LinkButton>
                </div>
            </aside>

            <!-- Main Content -->
            <main class="flex-1 flex flex-col">
                <header class="bg-white shadow-sm p-4">
                     <div class="flex items-center">
                        <a href="ValidatePrescriptions.aspx" class="text-gray-500 hover:text-gray-700 mr-4"><i class="fas fa-arrow-left"></i></a>
                        <h1 class="text-2xl font-bold text-gray-800">
                            Validate Prescription: <asp:Literal ID="litPrescriptionNumber" runat="server"></asp:Literal>
                        </h1>
                    </div>
                </header>

                <div class="flex-1 p-6 overflow-y-auto">
                     <asp:HiddenField ID="hdnPrescriptionId" runat="server" />
                     <!-- Server Message -->
                     <asp:Literal ID="litMessage" runat="server"></asp:Literal>

                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <!-- Center Column: Prescription Image -->
                        <div class="lg:col-span-2 bg-white p-6 rounded-lg shadow">
                            <h2 class="text-xl font-bold text-gray-800 mb-4">Prescription Image</h2>
                            <asp:Image ID="imgPrescription" runat="server" CssClass="w-full rounded-lg border" ImageUrl="https://placehold.co/800x1000/CCCCCC/FFFFFF?text=No+Image+Found" />
                        </div>

                        <!-- Right Column: Details & Actions -->
                        <div class="lg:col-span-1 space-y-6">
                            <div class="bg-white p-6 rounded-lg shadow">
                                <h2 class="text-xl font-bold text-gray-800 mb-4">Details</h2>
                                <div class="space-y-2 text-sm">
                                    <p><span class="font-semibold">Customer:</span> <asp:Literal ID="litCustomerName" runat="server"></asp:Literal></p>
                                    <p><span class="font-semibold">Doctor:</span> <asp:Literal ID="litDoctorName" runat="server"></asp:Literal></p>
                                    <p><span class="font-semibold">Date:</span> <asp:Literal ID="litPrescriptionDate" runat="server"></asp:Literal></p>
                                    <p><span class="font-semibold">Current Status:</span> <asp:Literal ID="litCurrentStatus" runat="server"></asp:Literal></p>
                                </div>
                            </div>
                            
                            <asp:Panel ID="pnlValidationActions" runat="server" CssClass="bg-white p-6 rounded-lg shadow">
                                <h2 class="text-xl font-bold text-gray-800 mb-4">Validation Action</h2>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Verification Notes</label>
                                    <asp:TextBox ID="txtVerificationNotes" runat="server" TextMode="MultiLine" Rows="3" CssClass="mt-1 block w-full input-style" placeholder="e.g., Verified with Dr. Smith's office."></asp:TextBox>
                                </div>
                                 <div class="mt-4">
                                    <label class="block text-sm font-medium text-gray-700">Rejection Reason (if rejecting)</label>
                                    <asp:TextBox ID="txtRejectionReason" runat="server" CssClass="mt-1 block w-full input-style" placeholder="e.g., Illegible, expired, etc."></asp:TextBox>
                                </div>
                                <div class="mt-6 flex space-x-3">
                                    <asp:Button ID="btnApprove" runat="server" Text="Approve" OnClick="btnApprove_Click" CssClass="flex-1 justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700" />
                                    <asp:Button ID="btnReject" runat="server" Text="Reject" OnClick="btnReject_Click" CausesValidation="false" OnClientClick="return confirm('Are you sure you want to reject this prescription?');" CssClass="flex-1 justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700" />
                                </div>
                            </asp:Panel>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </form>
</body>
</html>
