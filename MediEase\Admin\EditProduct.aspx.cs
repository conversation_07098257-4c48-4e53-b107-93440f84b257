﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Runtime.Remoting.Messaging;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Xml.Linq;

public partial class Admin_EditProduct : System.Web.UI.Page
{
    private bool IsEditMode = false;
    private int MedicineId = 0;

    protected void Page_Load(object sender, EventArgs e)
    {
        // --- SECURITY CHECK ---
        if (Session["UserId"] == null || Session["UserRole"]?.ToString() != "Admin")
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        // Determine if we are in Edit Mode by checking the query string
        if (!string.IsNullOrEmpty(Request.QueryString["id"]) && int.TryParse(Request.QueryString["id"], out MedicineId))
        {
            IsEditMode = true;
            hdnMedicineId.Value = MedicineId.ToString();
        }

        if (!IsPostBack)
        {
            PopulateDropDowns();
            if (IsEditMode)
            {
                litPageTitle.Text = "Edit Product";
                LoadProductData();
            }
            else
            {
                litPageTitle.Text = "Add New Product";
            }
        }
    }

    private void PopulateDropDowns()
    {
        string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
        using (SqlConnection con = new SqlConnection(connectionString))
        {
            try
            {
                con.Open();
                // Populate Categories
                SqlDataAdapter catSda = new SqlDataAdapter("SELECT CategoryId, Name FROM Categories WHERE IsActive = 1 ORDER BY Name", con);
                DataTable catDt = new DataTable();
                catSda.Fill(catDt);
                ddlCategory.DataSource = catDt;
                ddlCategory.DataTextField = "Name";
                ddlCategory.DataValueField = "CategoryId";
                ddlCategory.DataBind();
                ddlCategory.Items.Insert(0, new ListItem("-- Select Category --", ""));

                // Populate Brands
                SqlDataAdapter brandSda = new SqlDataAdapter("SELECT BrandId, Name FROM Brands WHERE IsActive = 1 ORDER BY Name", con);
                DataTable brandDt = new DataTable();
                brandSda.Fill(brandDt);
                ddlBrand.DataSource = brandDt;
                ddlBrand.DataTextField = "Name";
                ddlBrand.DataValueField = "BrandId";
                ddlBrand.DataBind();
                ddlBrand.Items.Insert(0, new ListItem("-- Select Brand --", ""));
            }
            catch (Exception ex)
            {
                ShowMessage($"Error loading dropdowns: {ex.Message}", "error");
            }
        }
    }

    private void LoadProductData()
    {
        string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
        using (SqlConnection con = new SqlConnection(connectionString))
        {
            string query = "SELECT * FROM Medicines WHERE MedicineId = @MedicineId";
            SqlCommand cmd = new SqlCommand(query, con);
            cmd.Parameters.AddWithValue("@MedicineId", MedicineId);
            try
            {
                con.Open();
                SqlDataReader reader = cmd.ExecuteReader();
                if (reader.Read())
                {
                    txtName.Text = reader["Name"].ToString();
                    txtGenericName.Text = reader["GenericName"]?.ToString();
                    txtDescription.Text = reader["Description"]?.ToString();
                    ddlCategory.SelectedValue = reader["CategoryId"]?.ToString();
                    ddlBrand.SelectedValue = reader["BrandId"]?.ToString();
                    txtPrice.Text = Convert.ToDecimal(reader["Price"]).ToString("F2");
                    txtStockQuantity.Text = reader["StockQuantity"].ToString();
                    txtStrength.Text = reader["Strength"]?.ToString();
                    chkIsActive.Checked = (bool)reader["IsActive"];
                    chkIsFeatured.Checked = (bool)reader["IsFeatured"];
                    chkPrescriptionRequired.Checked = (bool)reader["PrescriptionRequired"];

                    if (reader["ImageUrl"] != DBNull.Value && !string.IsNullOrEmpty(reader["ImageUrl"].ToString()))
                    {
                        imgCurrentImage.ImageUrl = $"~/{reader["ImageUrl"]}";
                        imgCurrentImage.Visible = true;
                    }
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"Error loading product data: {ex.Message}", "error");
            }
        }
    }

    protected void btnSave_Click(object sender, EventArgs e)
    {
        if (!Page.IsValid) return;

        string imageUrl = HandleImageUpload();

        string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
        using (SqlConnection con = new SqlConnection(connectionString))
        {
            string query = "";
            if (IsEditMode)
            {
                // UPDATE query
                query = @"UPDATE Medicines SET 
                            Name=@Name, GenericName=@GenericName, Description=@Description, CategoryId=@CategoryId, BrandId=@BrandId, Price=@Price, 
                            StockQuantity=@StockQuantity, Strength=@Strength, IsActive=@IsActive, IsFeatured=@IsFeatured, PrescriptionRequired=@PrescriptionRequired, 
                            ImageUrl=ISNULL(@ImageUrl, ImageUrl), ModifiedDate=@ModifiedDate, ModifiedBy=@ModifiedBy 
                          WHERE MedicineId=@MedicineId";
            }
            else
            {
                // INSERT query
                query = @"INSERT INTO Medicines (Name, GenericName, Description, CategoryId, BrandId, Price, StockQuantity, Strength, IsActive, IsFeatured, PrescriptionRequired, ImageUrl, CreatedBy, ModifiedBy) 
                          VALUES (@Name, @GenericName, @Description, @CategoryId, @BrandId, @Price, @StockQuantity, @Strength, @IsActive, @IsFeatured, @PrescriptionRequired, @ImageUrl, @CreatedBy, NULL)";
            }

            using (SqlCommand cmd = new SqlCommand(query, con))
            {
                // Add parameters
                cmd.Parameters.AddWithValue("@Name", txtName.Text);
                cmd.Parameters.AddWithValue("@GenericName", string.IsNullOrWhiteSpace(txtGenericName.Text) ? (object)DBNull.Value : txtGenericName.Text);
                cmd.Parameters.AddWithValue("@Description", string.IsNullOrWhiteSpace(txtDescription.Text) ? (object)DBNull.Value : txtDescription.Text);
                cmd.Parameters.AddWithValue("@CategoryId", ddlCategory.SelectedValue);
                cmd.Parameters.AddWithValue("@BrandId", string.IsNullOrEmpty(ddlBrand.SelectedValue) ? (object)DBNull.Value : Convert.ToInt32(ddlBrand.SelectedValue));
                cmd.Parameters.AddWithValue("@Price", Convert.ToDecimal(txtPrice.Text));
                cmd.Parameters.AddWithValue("@StockQuantity", Convert.ToInt32(txtStockQuantity.Text));
                cmd.Parameters.AddWithValue("@Strength", string.IsNullOrWhiteSpace(txtStrength.Text) ? (object)DBNull.Value : txtStrength.Text);
                cmd.Parameters.AddWithValue("@IsActive", chkIsActive.Checked);
                cmd.Parameters.AddWithValue("@IsFeatured", chkIsFeatured.Checked);
                cmd.Parameters.AddWithValue("@PrescriptionRequired", chkPrescriptionRequired.Checked);
                cmd.Parameters.AddWithValue("@ImageUrl", string.IsNullOrEmpty(imageUrl) ? (object)DBNull.Value : imageUrl);

                int currentUserId = (int)Session["UserId"];
                if (IsEditMode)
                {
                    cmd.Parameters.AddWithValue("@MedicineId", MedicineId);
                    cmd.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);
                    cmd.Parameters.AddWithValue("@ModifiedBy", currentUserId);
                }
                else
                {
                    cmd.Parameters.AddWithValue("@CreatedBy", currentUserId);
                }

                try
                {
                    con.Open();
                    cmd.ExecuteNonQuery();
                    Session["ToastMessage"] = "Product saved successfully!"; // Using Session to pass message across redirect
                    Response.Redirect("ProductManagement.aspx");
                }
                catch (Exception ex)
                {
                    ShowMessage($"Error saving product: {ex.Message}", "error");
                }
            }
        }
    }

    private string HandleImageUpload()
    {
        if (fileUploadImage.HasFile)
        {
            try
            {
                string filename = Path.GetFileName(fileUploadImage.FileName);
                string fileExtension = Path.GetExtension(filename).ToLower();
                string[] allowedExtensions = { ".jpg", ".jpeg", ".png", ".gif" };
                if (Array.IndexOf(allowedExtensions, fileExtension) != -1)
                {
                    string uniqueFileName = Guid.NewGuid().ToString() + fileExtension;
                    string folderPath = Server.MapPath("~/Uploads/Products/");
                    if (!Directory.Exists(folderPath))
                    {
                        Directory.CreateDirectory(folderPath);
                    }
                    fileUploadImage.SaveAs(folderPath + uniqueFileName);
                    return "Uploads/Products/" + uniqueFileName;
                }
                else
                {
                    ShowMessage("Invalid image file type. Please upload JPG, PNG, or GIF.", "error");
                    return null;
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"File upload error: {ex.Message}", "error");
                return null;
            }
        }
        return null; // Return null if no new file is uploaded
    }

    protected void btnCancel_Click(object sender, EventArgs e)
    {
        Response.Redirect("ProductManagement.aspx");
    }

    protected void btnLogout_Click(object sender, EventArgs e)
    {
        Session.Clear();
        Session.Abandon();
        Response.Redirect("~/Login.aspx");
    }

    private void ShowMessage(string message, string type)
    {
        string cssClass = (type == "success")
            ? "bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg relative mb-4"
            : "bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg relative mb-4";

        litMessage.Text = $"<div class='{cssClass}' role='alert'>{message}</div>";
    }
}
