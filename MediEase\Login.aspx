<%@ Page Language="C#" AutoEventWireup="true" CodeFile="Login.aspx.cs" Inherits="Login" %>

<!DOCTYPE html>
<html lang="en">
<head runat="server">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - MediEase</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" xintegrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <style>
        body { font-family: 'Inter', sans-serif; }
        .validation-error { color: #D32F2F; font-size: 0.875rem; margin-top: 0.25rem; }
    </style>
</head>
<body class="bg-gray-50">
    <form id="form1" runat="server">
        <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
            <div class="max-w-md w-full space-y-8 p-10 bg-white shadow-lg rounded-xl">
                <div>
                    <a href="default.aspx" class="flex justify-center items-center space-x-2">
                        <i class="fas fa-pills text-4xl text-teal-600"></i>
                        <span class="text-3xl font-bold text-gray-800">MediEase</span>
                    </a>
                    <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                        Sign in to your account
                    </h2>
                    <p class="mt-2 text-center text-sm text-gray-600">
                        Or <a href="Register.aspx" class="font-medium text-teal-600 hover:text-teal-500">
                            start for free by creating a new account
                        </a>
                    </p>
                </div>

                <!-- Server-side message placeholder -->
                <asp:Literal ID="litMessage" runat="server"></asp:Literal>

                <div class="rounded-md shadow-sm -space-y-px">
                    <div>
                        <asp:Label For="txtEmail" runat="server" Text="Email Address" CssClass="sr-only"></asp:Label>
                        <asp:TextBox ID="txtEmail" runat="server" TextMode="Email" CssClass="appearance-none rounded-none relative block w-full px-3 py-3 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-teal-500 focus:border-teal-500 focus:z-10 sm:text-sm" placeholder="Email address"></asp:TextBox>
                        <asp:RequiredFieldValidator ID="rfvEmail" runat="server" ControlToValidate="txtEmail" ErrorMessage="Email is required." CssClass="validation-error" Display="Dynamic"></asp:RequiredFieldValidator>
                    </div>
                    <div>
                        <asp:Label For="txtPassword" runat="server" Text="Password" CssClass="sr-only"></asp:Label>
                        <asp:TextBox ID="txtPassword" runat="server" TextMode="Password" CssClass="appearance-none rounded-none relative block w-full px-3 py-3 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-teal-500 focus:border-teal-500 focus:z-10 sm:text-sm" placeholder="Password"></asp:TextBox>
                         <asp:RequiredFieldValidator ID="rfvPassword" runat="server" ControlToValidate="txtPassword" ErrorMessage="Password is required." CssClass="validation-error" Display="Dynamic"></asp:RequiredFieldValidator>
                    </div>
                </div>

                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <asp:CheckBox ID="chkRememberMe" runat="server" CssClass="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded" />
                        <asp:Label For="chkRememberMe" runat="server" Text="Remember me" CssClass="ml-2 block text-sm text-gray-900"></asp:Label>
                    </div>

                    <div class="text-sm">
                        <a href="ForgotPassword.aspx" class="font-medium text-teal-600 hover:text-teal-500">
                            Forgot your password?
                        </a>
                    </div>
                </div>

                <div>
                    <asp:Button ID="btnLogin" runat="server" OnClick="btnLogin_Click" Text="Sign In" CssClass="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 cursor-pointer" />
                </div>
            </div>
        </div>
    </form>
</body>
</html>
