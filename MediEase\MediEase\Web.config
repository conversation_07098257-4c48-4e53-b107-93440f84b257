﻿<?xml version="1.0" encoding="utf-8"?>
<!-- ======================================================================= -->
<!-- FILE: Web.config (Simplified for Client-Side Chatbot)                   -->
<!-- ======================================================================= -->
<configuration>
	<appSettings>
		<add key="ValidationSettings:UnobtrusiveValidationMode" value="None" />
	</appSettings>
	<connectionStrings>
		<add name="MediEaseDB"
			 connectionString="Data Source=(LocalDB)\MSSQLLocalDB;AttachDbFilename=|DataDirectory|\MediEase.mdf;Integrated Security=True"
			 providerName="System.Data.SqlClient" />
	</connectionStrings>
	<system.web>
		<compilation debug="true" targetFramework="4.7.2" />
		<httpRuntime targetFramework="4.7.2" />
		<pages>
			<namespaces />
			<controls />
		</pages>
		<sessionState mode="InProc" timeout="30" />
	</system.web>
	<runtime>
		<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Web.Infrastructure" culture="neutral" publicKeyToken="31bf3856ad364e35" />
				<bindingRedirect oldVersion="0.0.0.0-2.0.0.0" newVersion="2.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Newtonsoft.Json" culture="neutral" publicKeyToken="30ad4fe6b2a6aeed" />
				<bindingRedirect oldVersion="0.0.0.0-13.0.0.0" newVersion="13.0.0.0" />
			</dependentAssembly>
		</assemblyBinding>
	</runtime>
</configuration>
