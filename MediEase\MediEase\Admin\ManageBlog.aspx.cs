﻿/*
 =======================================================================
 FILE: ManageBlog.aspx.cs
 PURPOSE: Backend logic for the blog management page. Handles fetching
          and deleting blog posts.
 PLACE IN: The 'Admin' folder in your project root.
 =======================================================================
*/
using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace MediEase.Admin
{
    public partial class ManageBlog : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            // Security Check: Ensure user is logged in and is an Admin
            if (Session["UserID"] == null || Session["RoleName"]?.ToString() != "Admin")
            {
                Response.Redirect("~/Login.aspx");
                return;
            }

            if (!IsPostBack)
            {
                BindBlogPostsGrid();
            }
        }

        private void BindBlogPostsGrid()
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                // This query joins BlogPosts with UserProfiles to get the author's name
                const string query = @"
                    SELECT 
                        bp.PostID,
                        bp.Title,
                        up.FirstName + ' ' + up.LastName AS AuthorName,
                        bp.PublishDate,
                        bp.IsPublished
                    FROM 
                        BlogPosts bp
                    INNER JOIN 
                        UserProfiles up ON bp.AuthorID = up.UserID
                    ORDER BY 
                        bp.PublishDate DESC;
                ";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    try
                    {
                        con.Open();
                        SqlDataAdapter sda = new SqlDataAdapter(cmd);
                        DataTable dt = new DataTable();
                        sda.Fill(dt);
                        BlogPostsGrid.DataSource = dt;
                        BlogPostsGrid.DataBind();
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine("Error binding blog posts grid: " + ex.Message);
                    }
                }
            }
        }

        protected void BlogPostsGrid_PageIndexChanging(object sender, GridViewPageEventArgs e)
        {
            BlogPostsGrid.PageIndex = e.NewPageIndex;
            BindBlogPostsGrid();
        }

        protected void BlogPostsGrid_RowCommand(object sender, GridViewCommandEventArgs e)
        {
            if (e.CommandName == "DeletePost")
            {
                int postId = Convert.ToInt32(e.CommandArgument);
                DeleteBlogPost(postId);
                BindBlogPostsGrid(); // Re-bind the grid to reflect the deletion
            }
        }

        private void DeleteBlogPost(int postId)
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                // This command permanently deletes the blog post.
                const string query = "DELETE FROM BlogPosts WHERE PostID = @PostID";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@PostID", postId);
                    try
                    {
                        con.Open();
                        cmd.ExecuteNonQuery();
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine("Error deleting blog post: " + ex.Message);
                    }
                }
            }
        }
    }
}
