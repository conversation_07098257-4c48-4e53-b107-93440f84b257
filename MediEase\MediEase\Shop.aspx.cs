﻿/* FILE: Shop.aspx.cs (Full & Unabridged Final Version) */
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace MediEase
{
    public partial class Shop : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                PopulateFilters();
                BindProducts();
                SetFilterControlsFromQueryString();
            }
        }

        private void PopulateFilters()
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                try
                {
                    con.Open();
                    SqlDataAdapter sdaBrand = new SqlDataAdapter("SELECT BrandID, BrandName FROM Brands ORDER BY BrandName", con);
                    DataTable dtBrand = new DataTable();
                    sdaBrand.Fill(dtBrand);
                    BrandFilter.DataSource = dtBrand;
                    BrandFilter.DataTextField = "BrandName";
                    BrandFilter.DataValueField = "BrandID";
                    BrandFilter.DataBind();
                    BrandFilter.Items.Insert(0, new ListItem("All Brands", ""));

                    SqlDataAdapter sdaForm = new SqlDataAdapter("SELECT DISTINCT Form FROM Products WHERE Form IS NOT NULL AND Form <> '' ORDER BY Form", con);
                    DataTable dtForm = new DataTable();
                    sdaForm.Fill(dtForm);
                    FormFilter.DataSource = dtForm;
                    FormFilter.DataTextField = "Form";
                    FormFilter.DataValueField = "Form";
                    FormFilter.DataBind();
                    FormFilter.Items.Insert(0, new ListItem("All Forms", ""));

                    BindCategories();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine("Error populating filters: " + ex.Message);
                }
            }
        }

        private void BindProducts()
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                var queryBuilder = new StringBuilder(@"SELECT p.ProductID, p.Name, p.ImageUrl, b.BrandName, pr.Price FROM Products p LEFT JOIN Brands b ON p.BrandID = b.BrandID INNER JOIN (SELECT ProductID, Price, ROW_NUMBER() OVER(PARTITION BY ProductID ORDER BY EffectiveDate DESC) as rn FROM Pricing) pr ON p.ProductID = pr.ProductID AND pr.rn = 1 WHERE p.IsActive = 1 AND p.IsApproved = 1");
                var parameters = new List<SqlParameter>();
                string categoryId = Request.QueryString["category"];
                string searchTerm = Request.QueryString["search"];
                string brandId = Request.QueryString["brand"];
                string minPrice = Request.QueryString["minPrice"];
                string maxPrice = Request.QueryString["maxPrice"];
                string form = Request.QueryString["form"];

                if (!string.IsNullOrEmpty(categoryId)) { queryBuilder.Append(" AND p.CategoryID = @CategoryID"); parameters.Add(new SqlParameter("@CategoryID", categoryId)); }
                if (!string.IsNullOrEmpty(searchTerm)) { queryBuilder.Append(" AND p.Name LIKE @SearchTerm"); parameters.Add(new SqlParameter("@SearchTerm", "%" + searchTerm + "%")); }
                if (!string.IsNullOrEmpty(brandId)) { queryBuilder.Append(" AND p.BrandID = @BrandID"); parameters.Add(new SqlParameter("@BrandID", brandId)); }
                if (!string.IsNullOrEmpty(minPrice) && decimal.TryParse(minPrice, out _)) { queryBuilder.Append(" AND pr.Price >= @MinPrice"); parameters.Add(new SqlParameter("@MinPrice", minPrice)); }
                if (!string.IsNullOrEmpty(maxPrice) && decimal.TryParse(maxPrice, out _)) { queryBuilder.Append(" AND pr.Price <= @MaxPrice"); parameters.Add(new SqlParameter("@MaxPrice", maxPrice)); }
                if (!string.IsNullOrEmpty(form)) { queryBuilder.Append(" AND p.Form = @Form"); parameters.Add(new SqlParameter("@Form", form)); }

                SqlCommand cmd = new SqlCommand(queryBuilder.ToString(), con);
                cmd.Parameters.AddRange(parameters.ToArray());
                try
                {
                    con.Open();
                    SqlDataAdapter sda = new SqlDataAdapter(cmd);
                    DataTable dt = new DataTable();
                    sda.Fill(dt);
                    if (dt.Rows.Count > 0) { ProductRepeater.DataSource = dt; ProductRepeater.DataBind(); NoProductsMessage.Visible = false; }
                    else { ProductRepeater.DataSource = null; ProductRepeater.DataBind(); NoProductsMessage.Visible = true; }
                    UpdatePageTitle(categoryId, searchTerm);
                }
                catch (Exception ex)
                {
                    NoProductsMessage.Visible = true;
                    System.Diagnostics.Debug.WriteLine("Error binding products: " + ex.Message);
                }
            }
        }

        protected void ApplyFiltersButton_Click(object sender, EventArgs e)
        {
            var query = HttpUtility.ParseQueryString(string.Empty);
            if (!string.IsNullOrEmpty(SearchBox.Text)) query["search"] = SearchBox.Text.Trim();
            if (!string.IsNullOrEmpty(BrandFilter.SelectedValue)) query["brand"] = BrandFilter.SelectedValue;
            if (!string.IsNullOrEmpty(MinPriceBox.Text)) query["minPrice"] = MinPriceBox.Text.Trim();
            if (!string.IsNullOrEmpty(MaxPriceBox.Text)) query["maxPrice"] = MaxPriceBox.Text.Trim();
            if (!string.IsNullOrEmpty(FormFilter.SelectedValue)) query["form"] = FormFilter.SelectedValue;
            if (!string.IsNullOrEmpty(Request.QueryString["category"])) query["category"] = Request.QueryString["category"];
            Response.Redirect("Shop.aspx?" + query.ToString());
        }

        private void SetFilterControlsFromQueryString()
        {
            if (!string.IsNullOrEmpty(Request.QueryString["search"])) SearchBox.Text = Request.QueryString["search"];
            if (!string.IsNullOrEmpty(Request.QueryString["brand"])) BrandFilter.SelectedValue = Request.QueryString["brand"];
            if (!string.IsNullOrEmpty(Request.QueryString["minPrice"])) MinPriceBox.Text = Request.QueryString["minPrice"];
            if (!string.IsNullOrEmpty(Request.QueryString["maxPrice"])) MaxPriceBox.Text = Request.QueryString["maxPrice"];
            if (!string.IsNullOrEmpty(Request.QueryString["form"])) FormFilter.SelectedValue = Request.QueryString["form"];
        }

        private void BindCategories()
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "SELECT CategoryID, CategoryName FROM Categories ORDER BY CategoryName";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    try
                    {
                        if (con.State != ConnectionState.Open) { con.Open(); }
                        SqlDataAdapter sda = new SqlDataAdapter(cmd);
                        DataTable dt = new DataTable();
                        sda.Fill(dt);
                        CategoryRepeater.DataSource = dt;
                        CategoryRepeater.DataBind();
                    }
                    catch (Exception ex) { System.Diagnostics.Debug.WriteLine("Error binding categories: " + ex.Message); }
                }
            }
        }

        private void UpdatePageTitle(string categoryId, string searchTerm)
        {
            if (!string.IsNullOrEmpty(categoryId))
            {
                string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    string query = "SELECT CategoryName FROM Categories WHERE CategoryID = @CategoryID";
                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        cmd.Parameters.AddWithValue("@CategoryID", categoryId);
                        con.Open();
                        object result = cmd.ExecuteScalar();
                        if (result != null) { PageTitle.InnerText = "Shop - " + result.ToString(); AllCategoriesLink.CssClass = "list-group-item list-group-item-action"; }
                    }
                }
            }
            else if (!string.IsNullOrEmpty(searchTerm)) { PageTitle.InnerText = "Search Results for '" + searchTerm + "'"; }
            else { PageTitle.InnerText = "All Products"; AllCategoriesLink.CssClass = "list-group-item list-group-item-action active"; }
        }

        protected void ProductRepeater_ItemDataBound(object sender, RepeaterItemEventArgs e)
        {
            if (e.Item.ItemType == ListItemType.Item || e.Item.ItemType == ListItemType.AlternatingItem)
            {
                Image productImage = (Image)e.Item.FindControl("ProductImage");
                if (productImage != null)
                {
                    string imageUrl = DataBinder.Eval(e.Item.DataItem, "ImageUrl") as string;
                    productImage.ImageUrl = string.IsNullOrEmpty(imageUrl) ? "~/Images/Products/default_product.png" : ResolveUrl(imageUrl);
                }
            }
        }
    }
}