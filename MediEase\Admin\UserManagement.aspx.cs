using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web.UI;
using System.Text;
using System.Collections.Generic;
using System.Web.UI.WebControls;

public partial class Admin_UserManagement : System.Web.UI.Page
{
    protected void Page_Load(object sender, EventArgs e)
    {
        // --- SECURITY CHECK ---
        if (Session["UserId"] == null || Session["UserRole"]?.ToString() != "Admin")
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        if (!IsPostBack)
        {
            BindUsersGrid();
        }
    }

    /// <summary>
    /// Fetches users from the database based on filter criteria and binds them to the GridView.
    /// </summary>
    private void BindUsersGrid()
    {
        string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
        using (SqlConnection con = new SqlConnection(connectionString))
        {
            StringBuilder queryBuilder = new StringBuilder("SELECT UserId, FirstName, LastName, Email, Role, IsActive, LastLoginDate FROM Users WHERE Role != 'Admin'");
            SqlCommand cmd = new SqlCommand();

            // --- Build Where Clause for Filtering ---
            List<string> conditions = new List<string>();

            // Search Term Filter
            if (!string.IsNullOrWhiteSpace(txtSearch.Text))
            {
                conditions.Add("(FirstName LIKE @Search OR LastName LIKE @Search OR Email LIKE @Search)");
                cmd.Parameters.AddWithValue("@Search", "%" + txtSearch.Text.Trim() + "%");
            }

            // Role Filter
            if (!string.IsNullOrEmpty(ddlRoleFilter.SelectedValue))
            {
                conditions.Add("Role = @Role");
                cmd.Parameters.AddWithValue("@Role", ddlRoleFilter.SelectedValue);
            }

            // Status Filter
            if (!string.IsNullOrEmpty(ddlStatusFilter.SelectedValue))
            {
                conditions.Add("IsActive = @IsActive");
                cmd.Parameters.AddWithValue("@IsActive", Convert.ToInt32(ddlStatusFilter.SelectedValue));
            }

            if (conditions.Count > 0)
            {
                queryBuilder.Append(" AND " + string.Join(" AND ", conditions));
            }

            queryBuilder.Append(" ORDER BY CreatedDate DESC");

            cmd.CommandText = queryBuilder.ToString();
            cmd.Connection = con;

            try
            {
                con.Open();
                using (SqlDataAdapter sda = new SqlDataAdapter(cmd))
                {
                    DataTable dt = new DataTable();
                    sda.Fill(dt);
                    gvUsers.DataSource = dt;
                    gvUsers.DataBind();
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"An error occurred: {ex.Message}", "error");
            }
        }
    }

    protected void btnSearch_Click(object sender, EventArgs e)
    {
        gvUsers.PageIndex = 0; // Reset to first page on new search
        BindUsersGrid();
    }

    protected void btnClear_Click(object sender, EventArgs e)
    {
        txtSearch.Text = string.Empty;
        ddlRoleFilter.SelectedIndex = 0;
        ddlStatusFilter.SelectedIndex = 0;
        gvUsers.PageIndex = 0;
        BindUsersGrid();
    }

    protected void gvUsers_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        gvUsers.PageIndex = e.NewPageIndex;
        BindUsersGrid();
    }

    protected void gvUsers_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "ToggleStatus")
        {
            int userIdToToggle = Convert.ToInt32(e.CommandArgument);

            // For extra security, ensure an admin cannot deactivate themselves (though filtered out in query)
            if (userIdToToggle == (int)Session["UserId"])
            {
                ShowMessage("You cannot change your own status.", "error");
                return;
            }

            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                // The query flips the boolean value of IsActive
                string query = "UPDATE Users SET IsActive = CASE WHEN IsActive = 1 THEN 0 ELSE 1 END WHERE UserId = @UserId";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@UserId", userIdToToggle);
                    try
                    {
                        con.Open();
                        int rowsAffected = cmd.ExecuteNonQuery();
                        if (rowsAffected > 0)
                        {
                            ShowMessage("User status updated successfully.", "success");
                        }
                        // Re-bind the grid to show the change
                        BindUsersGrid();
                    }
                    catch (Exception ex)
                    {
                        ShowMessage($"An error occurred: {ex.Message}", "error");
                    }
                }
            }
        }
    }

    protected void gvUsers_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.DataRow)
        {
            // Convert null LastLoginDate to a more friendly display
            if (e.Row.DataItem != null && DataBinder.Eval(e.Row.DataItem, "LastLoginDate") == DBNull.Value)
            {
                e.Row.Cells[5].Text = "Never"; // Index of LastLoginDate column
            }
        }
    }

    protected void btnLogout_Click(object sender, EventArgs e)
    {
        Session.Clear();
        Session.Abandon();
        Response.Redirect("~/Login.aspx");
    }

    private void ShowMessage(string message, string type)
    {
        string cssClass = (type == "success")
            ? "bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg relative mb-4"
            : "bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg relative mb-4";

        litMessage.Text = $"<div class='{cssClass}' role='alert'>{message}</div>";
    }
}
