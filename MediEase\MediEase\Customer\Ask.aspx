﻿<%--
 =======================================================================
 FILE: Ask.aspx (Final Version)
 =======================================================================
--%>
<%@ Page Title="Ask a Pharmacist" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="Ask.aspx.cs" Inherits="MediEase.Customer.Ask" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>Ask a Pharmacist</h1>
            <a href="Dashboard.aspx" class="btn btn-outline-secondary">&larr; Back to Dashboard</a>
        </div>

        <div class="row">
            <!-- New Question Form -->
            <div class="col-lg-6">
                <div class="card shadow-sm">
                    <div class="card-header">
                        <h4 class="mb-0">Submit a New Question</h4>
                    </div>
                    <div class="card-body">
                        <p>Have a non-urgent question? Fill out the form below, and one of our pharmacists will get back to you.</p>
                        <asp:PlaceHolder ID="FormView" runat="server">
                             <div class="form-group">
                                <label>Subject</label>
                                <asp:TextBox ID="SubjectBox" runat="server" CssClass="form-control" placeholder="e.g., Question about medication side effects"></asp:TextBox>
                                <asp:RequiredFieldValidator runat="server" ControlToValidate="SubjectBox" ErrorMessage="A subject is required." CssClass="text-danger" Display="Dynamic" />
                            </div>
                            <div class="form-group">
                                <label>Your Question</label>
                                <asp:TextBox ID="QuestionBox" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="6"></asp:TextBox>
                                <asp:RequiredFieldValidator runat="server" ControlToValidate="QuestionBox" ErrorMessage="A question is required." CssClass="text-danger" Display="Dynamic" />
                            </div>
                            <asp:Button ID="SubmitQuestionButton" runat="server" Text="Submit Question" CssClass="btn btn-primary" OnClick="SubmitQuestionButton_Click" />
                        </asp:PlaceHolder>
                        <asp:PlaceHolder ID="SuccessMessage" runat="server" Visible="false">
                            <div class="alert alert-success">
                                <h5>Question Submitted!</h5>
                                <p>Your question has been sent successfully. You will find the answer below in your question history once a pharmacist responds.</p>
                            </div>
                        </asp:PlaceHolder>
                    </div>
                </div>
            </div>
            
            <!-- Question History -->
            <div class="col-lg-6">
                 <h4>Your Question History</h4>
                 <hr />
                 <asp:GridView ID="QuestionHistoryGrid" runat="server"
                    CssClass="table table-bordered table-striped"
                    AutoGenerateColumns="False"
                    DataKeyNames="QuestionID"
                    GridLines="None"
                    AllowPaging="True"
                    PageSize="10">
                     <Columns>
                         <asp:BoundField DataField="Subject" HeaderText="Subject" />
                         <asp:BoundField DataField="QuestionDate" HeaderText="Date Asked" DataFormatString="{0:MMM dd,ꗤ}" />
                         <asp:TemplateField HeaderText="Status">
                             <ItemTemplate>
                                 <span class='badge <%# Convert.ToBoolean(Eval("IsAnswered")) ? "badge-success" : "badge-info" %>'>
                                     <%# Convert.ToBoolean(Eval("IsAnswered")) ? "Answered" : "Pending" %>
                                 </span>
                             </ItemTemplate>
                         </asp:TemplateField>
                         <asp:HyperLinkField DataNavigateUrlFields="QuestionID"
                             DataNavigateUrlFormatString="~/Customer/QuestionDetails.aspx?QuestionID={0}"
                             Text="View Details"
                             ControlStyle-CssClass="btn btn-sm btn-outline-primary" />
                     </Columns>
                     <EmptyDataTemplate>
                         <div class="alert alert-light text-center">
                             You have not asked any questions yet.
                         </div>
                     </EmptyDataTemplate>
                 </asp:GridView>
            </div>
        </div>
    </div>
</asp:Content>