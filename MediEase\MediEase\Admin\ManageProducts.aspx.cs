﻿/* FILE: Admin/ManageProducts.aspx.cs */
using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace MediEase.Admin
{
    public partial class ManageProducts : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (Session["UserID"] == null || Session["RoleName"]?.ToString() != "Admin") { Response.Redirect("~/Login.aspx"); return; }
            if (!IsPostBack) { BindProductsGrid(); PopulateModalDropDowns(); }
        }

        private void BindProductsGrid()
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "SELECT p.ProductID, p.Name, p.ImageUrl, p.IsApproved, p.<PERSON>, c.CategoryName, b.Brand<PERSON>, (SELECT TOP 1 Price FROM Pricing WHERE ProductID = p.ProductID ORDER BY EffectiveDate DESC) AS Price, ISNULL((SELECT SUM(QuantityInStock) FROM Inventory WHERE ProductID = p.ProductID), 0) AS QuantityInStock FROM Products p LEFT JOIN Categories c ON p.CategoryID = c.CategoryID LEFT JOIN Brands b ON p.BrandID = b.BrandID ORDER BY p.ProductID DESC;";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    try { con.Open(); SqlDataAdapter sda = new SqlDataAdapter(cmd); DataTable dt = new DataTable(); sda.Fill(dt); ProductsGrid.DataSource = dt; ProductsGrid.DataBind(); }
                    catch (Exception ex) { System.Diagnostics.Debug.WriteLine("Error binding products grid: " + ex.Message); }
                }
            }
        }

        private void PopulateModalDropDowns()
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                try
                {
                    con.Open();
                    SqlDataAdapter sdaCat = new SqlDataAdapter("SELECT CategoryID, CategoryName FROM Categories ORDER BY CategoryName", con);
                    DataTable dtCat = new DataTable(); sdaCat.Fill(dtCat);
                    NewProductCategory.DataSource = dtCat; NewProductCategory.DataTextField = "CategoryName"; NewProductCategory.DataValueField = "CategoryID"; NewProductCategory.DataBind();
                    SqlDataAdapter sdaBrand = new SqlDataAdapter("SELECT BrandID, BrandName FROM Brands ORDER BY BrandName", con);
                    DataTable dtBrand = new DataTable(); sdaBrand.Fill(dtBrand);
                    NewProductBrand.DataSource = dtBrand; NewProductBrand.DataTextField = "BrandName"; NewProductBrand.DataValueField = "BrandID"; NewProductBrand.DataBind();
                }
                catch (Exception ex) { System.Diagnostics.Debug.WriteLine("Error populating modal dropdowns: " + ex.Message); }
            }
        }

        protected void AddProductButton_Click(object sender, EventArgs e)
        {
            string imageUrl = "~/Images/Products/default_product.png";
            if (NewProductImage.HasFile) { try { string fileName = Guid.NewGuid().ToString() + Path.GetExtension(NewProductImage.FileName); string folderPath = Server.MapPath("~/Images/Products/"); if (!Directory.Exists(folderPath)) Directory.CreateDirectory(folderPath); string imagePath = Path.Combine(folderPath, fileName); NewProductImage.SaveAs(imagePath); imageUrl = "~/Images/Products/" + fileName; } catch (Exception ex) { System.Diagnostics.Debug.WriteLine("Image Upload Error: " + ex.Message); return; } }
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                con.Open(); SqlTransaction trans = con.BeginTransaction(); int newProductId = 0;
                try
                {
                    string queryProduct = "INSERT INTO Products (Name, Description, ImageUrl, CategoryID, BrandID, Form, Strength, IsPrescriptionRequired, IsApproved, ApprovedByAdminID) OUTPUT INSERTED.ProductID VALUES (@Name, @Desc, @ImageUrl, @CatID, @BrandID, @Form, @Strength, @IsRx, 0, NULL);";
                    SqlCommand cmdProduct = new SqlCommand(queryProduct, con, trans);
                    cmdProduct.Parameters.AddWithValue("@Name", NewProductName.Text); cmdProduct.Parameters.AddWithValue("@Desc", NewProductDesc.Text); cmdProduct.Parameters.AddWithValue("@ImageUrl", imageUrl); cmdProduct.Parameters.AddWithValue("@CatID", NewProductCategory.SelectedValue); cmdProduct.Parameters.AddWithValue("@BrandID", NewProductBrand.SelectedValue); cmdProduct.Parameters.AddWithValue("@Form", NewProductForm.Text); cmdProduct.Parameters.AddWithValue("@Strength", NewProductStrength.Text); cmdProduct.Parameters.AddWithValue("@IsRx", NewProductIsPrescription.Checked);
                    newProductId = (int)cmdProduct.ExecuteScalar();
                    SqlCommand cmdPrice = new SqlCommand("INSERT INTO Pricing (ProductID, Price, SetByUserID) VALUES (@ProductID, @Price, @UserID)", con, trans);
                    cmdPrice.Parameters.AddWithValue("@ProductID", newProductId); cmdPrice.Parameters.AddWithValue("@Price", Convert.ToDecimal(NewProductPrice.Text)); cmdPrice.Parameters.AddWithValue("@UserID", Session["UserID"]); cmdPrice.ExecuteNonQuery();
                    SqlCommand cmdStock = new SqlCommand("INSERT INTO Inventory (ProductID, BatchNumber, ExpiryDate, QuantityInStock, ReorderLevel, LastUpdatedByUserID) VALUES (@ProductID, @Batch, @Expiry, @Stock, @Reorder, @UserID)", con, trans);
                    cmdStock.Parameters.AddWithValue("@ProductID", newProductId); cmdStock.Parameters.AddWithValue("@Batch", "INITIAL"); cmdStock.Parameters.AddWithValue("@Expiry", DateTime.Now.AddYears(2)); cmdStock.Parameters.AddWithValue("@Stock", Convert.ToInt32(NewProductStock.Text)); cmdStock.Parameters.AddWithValue("@Reorder", 10); cmdStock.Parameters.AddWithValue("@UserID", Session["UserID"]); cmdStock.ExecuteNonQuery();
                    trans.Commit();
                    AuditLogger.LogAction(Convert.ToInt32(Session["UserID"]), "Create Product", "Products", newProductId, newValues: $"Name = {NewProductName.Text}");
                }
                catch (Exception ex) { trans.Rollback(); System.Diagnostics.Debug.WriteLine("Error adding new product: " + ex.Message); }
            }
            BindProductsGrid();
        }

        protected void ProductsGrid_PageIndexChanging(object sender, GridViewPageEventArgs e) { ProductsGrid.PageIndex = e.NewPageIndex; BindProductsGrid(); }
        protected void ProductsGrid_RowEditing(object sender, GridViewEditEventArgs e) { ProductsGrid.EditIndex = e.NewEditIndex; BindProductsGrid(); }
        protected void ProductsGrid_RowCancelingEdit(object sender, GridViewCancelEditEventArgs e) { ProductsGrid.EditIndex = -1; BindProductsGrid(); }
        protected void ProductsGrid_RowUpdating(object sender, GridViewUpdateEventArgs e)
        {
            int productId = Convert.ToInt32(ProductsGrid.DataKeys[e.RowIndex].Value);
            TextBox txtName = (TextBox)ProductsGrid.Rows[e.RowIndex].FindControl("txtName"); TextBox txtPrice = (TextBox)ProductsGrid.Rows[e.RowIndex].FindControl("txtPrice"); TextBox txtStock = (TextBox)ProductsGrid.Rows[e.RowIndex].FindControl("txtStock");
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                con.Open(); SqlTransaction trans = con.BeginTransaction();
                try
                {
                    SqlCommand cmdProduct = new SqlCommand("UPDATE Products SET Name = @Name WHERE ProductID = @ProductID", con, trans); cmdProduct.Parameters.AddWithValue("@Name", txtName.Text); cmdProduct.Parameters.AddWithValue("@ProductID", productId); cmdProduct.ExecuteNonQuery();
                    SqlCommand cmdPrice = new SqlCommand("INSERT INTO Pricing (ProductID, Price, SetByUserID) VALUES (@ProductID, @Price, @UserID)", con, trans); cmdPrice.Parameters.AddWithValue("@ProductID", productId); cmdPrice.Parameters.AddWithValue("@Price", Convert.ToDecimal(txtPrice.Text)); cmdPrice.Parameters.AddWithValue("@UserID", Session["UserID"]); cmdPrice.ExecuteNonQuery();
                    SqlCommand cmdStock = new SqlCommand("UPDATE Inventory SET QuantityInStock = @Stock WHERE InventoryID = (SELECT TOP 1 InventoryID FROM Inventory WHERE ProductID = @ProductID)", con, trans); cmdStock.Parameters.AddWithValue("@Stock", Convert.ToInt32(txtStock.Text)); cmdStock.Parameters.AddWithValue("@ProductID", productId); cmdStock.ExecuteNonQuery();
                    trans.Commit();
                    AuditLogger.LogAction(Convert.ToInt32(Session["UserID"]), "Update Product", "Products", productId, newValues: $"Name={txtName.Text}, Price={txtPrice.Text}, Stock={txtStock.Text}");
                }
                catch (Exception ex) { trans.Rollback(); System.Diagnostics.Debug.WriteLine("Error updating product: " + ex.Message); }
            }
            ProductsGrid.EditIndex = -1; BindProductsGrid();
        }
        protected void ProductsGrid_RowDeleting(object sender, GridViewDeleteEventArgs e)
        {
            int productId = Convert.ToInt32(ProductsGrid.DataKeys[e.RowIndex].Value);
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "UPDATE Products SET IsActive = 0 WHERE ProductID = @ProductID";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@ProductID", productId); con.Open(); cmd.ExecuteNonQuery();
                    AuditLogger.LogAction(Convert.ToInt32(Session["UserID"]), "Delete Product (Soft)", "Products", productId, newValues: "IsActive = 0");
                }
            }
            BindProductsGrid();
        }
        protected void ProductsGrid_RowDataBound(object sender, GridViewRowEventArgs e) { if (e.Row.RowType == DataControlRowType.DataRow) { Image productImage = (Image)e.Row.FindControl("ProductImage"); if (productImage != null) { string imageUrl = DataBinder.Eval(e.Row.DataItem, "ImageUrl") as string; productImage.ImageUrl = string.IsNullOrEmpty(imageUrl) ? "~/Images/Products/default_product.png" : ResolveUrl(imageUrl); } } }
        protected void ProductsGrid_RowCommand(object sender, GridViewCommandEventArgs e)
        {
            if (e.CommandName == "ApproveProduct")
            {
                int productId = Convert.ToInt32(e.CommandArgument); int adminId = Convert.ToInt32(Session["UserID"]);
                string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    const string query = "UPDATE Products SET IsApproved = 1, ApprovedByAdminID = @AdminID WHERE ProductID = @ProductID";
                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        cmd.Parameters.AddWithValue("@ProductID", productId); cmd.Parameters.AddWithValue("@AdminID", adminId);
                        try { con.Open(); cmd.ExecuteNonQuery(); AuditLogger.LogAction(adminId, "Approve Product", "Products", productId, newValues: "IsApproved = 1"); }
                        catch (Exception ex) { System.Diagnostics.Debug.WriteLine("Error approving product: " + ex.Message); }
                    }
                }
                BindProductsGrid();
            }
        }
    }
}