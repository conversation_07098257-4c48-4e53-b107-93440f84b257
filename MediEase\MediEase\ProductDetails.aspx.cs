﻿/* FILE: ProductDetails.aspx.cs (Definitive Final Version) */
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Net.Http;
using System.Threading.Tasks;
using Newtonsoft.Json;
using System.Net;

namespace MediEase
{
    public partial class ProductDetails : Page
    {
        private int productId;
        private int currentCategoryId;
        private string currentProductName;
        private string currentProductDesc;
        private static readonly HttpClient client = new HttpClient();

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!int.TryParse(Request.QueryString["ProductID"], out productId)) { ShowErrorView(); return; }
            if (!IsPostBack)
            {
                LoadProductDetails();
                if (ProductView.Visible)
                {
                    LoadProductReviews();
                    CheckReviewEligibility();
                    GetAIRecommendations();
                    LoadComparisonData();
                }
            }
        }

        private void LoadProductDetails()
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "SELECT p.Name, p.Description, p.Form, p.Strength, p.IsPrescriptionRequired, p.ImageUrl, p.CategoryID, b.BrandName, pr.Price, ISNULL((SELECT AVG(CAST(Rating AS DECIMAL(5,2))) FROM ProductReviews WHERE ProductID = p.ProductID), 0) AS AvgRating, ISNULL((SELECT COUNT(*) FROM ProductReviews WHERE ProductID = p.ProductID), 0) AS TotalReviews FROM Products p LEFT JOIN Brands b ON p.BrandID = b.BrandID INNER JOIN (SELECT ProductID, Price, ROW_NUMBER() OVER(PARTITION BY ProductID ORDER BY EffectiveDate DESC) as rn FROM Pricing) pr ON p.ProductID = pr.ProductID AND pr.rn = 1 WHERE p.ProductID = @ProductID AND p.IsActive = 1 AND p.IsApproved = 1;";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@ProductID", productId);
                    try
                    {
                        con.Open();
                        SqlDataReader reader = cmd.ExecuteReader();
                        if (reader.Read())
                        {
                            currentProductName = reader["Name"].ToString();
                            currentProductDesc = reader["Description"].ToString();
                            currentCategoryId = reader["CategoryID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["CategoryID"]);
                            ProductName.InnerText = currentProductName;
                            Page.Title = currentProductName;
                            ProductBrand.InnerText = reader["BrandName"].ToString();
                            ProductDescription.InnerText = currentProductDesc;
                            ProductStrength.Text = reader["Strength"].ToString();
                            ProductForm.Text = reader["Form"].ToString();
                            ProductPrice.InnerText = string.Format("${0:N2}", reader["Price"]);
                            string imageUrl = reader["ImageUrl"] as string;
                            ProductImage.ImageUrl = string.IsNullOrEmpty(imageUrl) ? "~/Images/Products/default_product.png" : ResolveUrl(imageUrl);
                            StarRatingSummary.Text = GetStarRating((int)Math.Round(Convert.ToDecimal(reader["AvgRating"])));
                            ReviewCount.Text = reader["TotalReviews"].ToString();
                            if ((bool)reader["IsPrescriptionRequired"]) { PrescriptionPanel.Visible = true; }
                            ProductView.Visible = true; ErrorView.Visible = false;
                        }
                        else { ShowErrorView(); }
                    }
                    catch (Exception ex) { System.Diagnostics.Debug.WriteLine("Error loading product details: " + ex.Message); ShowErrorView(); }
                }
            }
        }

        private void GetAIRecommendations()
        {
            if (string.IsNullOrEmpty(currentProductName)) return;
            try
            {
                string prompt = $"For the medicine '{currentProductName}', which is for '{currentProductDesc}', suggest three alternative or complementary products. Provide only the names in a comma-separated list.";
                var task = Task.Run(() => CallOpenRouterApi(prompt)); task.Wait();
                string recommendations = task.Result;
                if (!string.IsNullOrEmpty(recommendations))
                {
                    if (recommendations.StartsWith("API_ERROR:"))
                    {
                        AIRecommendationsLiteral.Text = $"<div class='alert alert-warning'>Could not fetch recommendations at this time. Reason: {Server.HtmlEncode(recommendations.Replace("API_ERROR:", "").Trim())}</div>";
                    }
                    else
                    {
                        var recList = recommendations.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
                        StringBuilder htmlList = new StringBuilder("<ul>");
                        foreach (var rec in recList) { htmlList.Append($"<li>{Server.HtmlEncode(rec.Trim())}</li>"); }
                        htmlList.Append("</ul>");
                        AIRecommendationsLiteral.Text = htmlList.ToString();
                    }
                    AIRecommendationsView.Visible = true;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("AI Recommendation Error: " + ex.Message);
                AIRecommendationsLiteral.Text = "<div class='alert alert-danger'>An unexpected error occurred while fetching AI recommendations.</div>";
                AIRecommendationsView.Visible = true;
            }
        }

        private async Task<string> CallOpenRouterApi(string userPrompt)
        {
            try
            {
                ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;
                string apiKey = "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";
                string modelName = "meta-llama/llama-3.3-8b-instruct:free";
                var payload = new { model = modelName, messages = new[] { new { role = "user", content = userPrompt } } };
                var request = new HttpRequestMessage
                {
                    Method = HttpMethod.Post,
                    RequestUri = new Uri("https://openrouter.ai/api/v1/chat/completions"),
                    Headers = { { "Authorization", "Bearer " + apiKey }, { "HTTP-Referer", Request.Url.GetLeftPart(UriPartial.Authority) } },
                    Content = new StringContent(JsonConvert.SerializeObject(payload), Encoding.UTF8, "application/json")
                };
                var response = await client.SendAsync(request);
                string responseBody = await response.Content.ReadAsStringAsync();
                if (!response.IsSuccessStatusCode)
                {
                    try { dynamic errorData = JsonConvert.DeserializeObject(responseBody); string errorMessage = errorData?.error?.message ?? response.ReasonPhrase; return "API_ERROR: " + errorMessage; }
                    catch { return "API_ERROR: " + response.ReasonPhrase + " - " + responseBody; }
                }
                dynamic responseData = JsonConvert.DeserializeObject(responseBody);
                return responseData.choices[0].message.content;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("--- AI API Call Failed ---");
                System.Diagnostics.Debug.WriteLine(ex.ToString());
                return "API_ERROR: " + ex.Message;
            }
        }

        private void LoadComparisonData()
        {
            if (currentCategoryId == 0) return;
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "SELECT TOP 4 p.ProductID, p.Name, p.ImageUrl, b.BrandName, p.Form, p.Strength, pr.Price FROM Products p LEFT JOIN Brands b ON p.BrandID = b.BrandID INNER JOIN (SELECT ProductID, Price, ROW_NUMBER() OVER(PARTITION BY ProductID ORDER BY EffectiveDate DESC) as rn FROM Pricing) pr ON p.ProductID = pr.ProductID AND pr.rn = 1 WHERE p.IsActive = 1 AND p.IsApproved = 1 AND p.CategoryID = @CategoryID AND p.ProductID <> @CurrentProductID ORDER BY pr.Price ASC;";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@CategoryID", currentCategoryId);
                    cmd.Parameters.AddWithValue("@CurrentProductID", productId);
                    try { con.Open(); SqlDataAdapter sda = new SqlDataAdapter(cmd); DataTable dt = new DataTable(); sda.Fill(dt); if (dt.Rows.Count > 0) { ComparisonGrid.DataSource = dt; ComparisonGrid.DataBind(); ComparisonView.Visible = true; } }
                    catch (Exception ex) { System.Diagnostics.Debug.WriteLine("Error loading comparison products: " + ex.Message); }
                }
            }
        }

        protected void ComparisonGrid_RowDataBound(object sender, GridViewRowEventArgs e) { if (e.Row.RowType == DataControlRowType.DataRow) { Image productImage = (Image)e.Row.FindControl("CompareProductImage"); if (productImage != null) { string imageUrl = DataBinder.Eval(e.Row.DataItem, "ImageUrl") as string; productImage.ImageUrl = string.IsNullOrEmpty(imageUrl) ? "~/Images/Products/default_product.png" : ResolveUrl(imageUrl); } } }

        private void LoadProductReviews()
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "SELECT pr.Rating, pr.Comment, pr.ReviewDate, up.FirstName + ' ' + up.LastName AS CustomerName FROM ProductReviews pr INNER JOIN UserProfiles up ON pr.CustomerID = up.UserID WHERE pr.ProductID = @ProductID ORDER BY pr.ReviewDate DESC;";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@ProductID", productId);
                    try { con.Open(); SqlDataAdapter sda = new SqlDataAdapter(cmd); DataTable dt = new DataTable(); sda.Fill(dt); if (dt.Rows.Count > 0) { ReviewsRepeater.DataSource = dt; ReviewsRepeater.DataBind(); NoReviewsMessage.Visible = false; } else { NoReviewsMessage.Visible = true; } }
                    catch (Exception ex) { System.Diagnostics.Debug.WriteLine("Error loading reviews: " + ex.Message); }
                }
            }
        }

        private void CheckReviewEligibility()
        {
            if (Session["UserID"] == null) return;
            int customerId = Convert.ToInt32(Session["UserID"]);
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                try
                {
                    con.Open();
                    const string checkReviewQuery = "SELECT COUNT(*) FROM ProductReviews WHERE CustomerID = @CustomerID AND ProductID = @ProductID";
                    SqlCommand checkCmd = new SqlCommand(checkReviewQuery, con); checkCmd.Parameters.AddWithValue("@CustomerID", customerId); checkCmd.Parameters.AddWithValue("@ProductID", productId);
                    if ((int)checkCmd.ExecuteScalar() > 0) { AlreadyReviewedMessage.Visible = true; return; }
                    const string checkPurchaseQuery = "SELECT COUNT(*) FROM Orders o INNER JOIN OrderItems oi ON o.OrderID = oi.OrderID WHERE o.CustomerID = @CustomerID AND oi.ProductID = @ProductID AND o.StatusID = 5";
                    SqlCommand purchaseCmd = new SqlCommand(checkPurchaseQuery, con); purchaseCmd.Parameters.AddWithValue("@CustomerID", customerId); purchaseCmd.Parameters.AddWithValue("@ProductID", productId);
                    if ((int)purchaseCmd.ExecuteScalar() > 0) { ReviewFormView.Visible = true; }
                }
                catch (Exception ex) { System.Diagnostics.Debug.WriteLine("Error checking review eligibility: " + ex.Message); }
            }
        }

        protected void SubmitReviewButton_Click(object sender, EventArgs e)
        {
            if (Page.IsValid)
            {
                int customerId = Convert.ToInt32(Session["UserID"]); int rating = Convert.ToInt32(RatingDropDown.SelectedValue); string comment = ReviewCommentBox.Text.Trim();
                string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    const string query = "INSERT INTO ProductReviews (ProductID, CustomerID, Rating, Comment, ReviewDate) VALUES (@ProductID, @CustomerID, @Rating, @Comment, GETDATE())";
                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        cmd.Parameters.AddWithValue("@ProductID", productId); cmd.Parameters.AddWithValue("@CustomerID", customerId); cmd.Parameters.AddWithValue("@Rating", rating); cmd.Parameters.AddWithValue("@Comment", comment);
                        try { con.Open(); cmd.ExecuteNonQuery(); ReviewFormView.Visible = false; AlreadyReviewedMessage.Visible = true; NoReviewsMessage.Visible = false; LoadProductReviews(); LoadProductDetails(); }
                        catch (Exception ex) { System.Diagnostics.Debug.WriteLine("Error submitting review: " + ex.Message); }
                    }
                }
            }
        }

        protected void AddToCartButton_Click(object sender, EventArgs e)
        {
            if (Session["UserID"] == null) { Response.Redirect("~/Login.aspx"); return; }
            int.TryParse(QuantityBox.Text, out int quantity);
            if (productId > 0 && quantity > 0)
            {
                List<CartItem> cart = Session["Cart"] as List<CartItem> ?? new List<CartItem>();
                CartItem existingItem = cart.FirstOrDefault(item => item.ProductID == productId);
                if (existingItem != null) { existingItem.Quantity += quantity; }
                else
                {
                    string productName = ProductName.InnerText; decimal price = decimal.Parse(ProductPrice.InnerText, System.Globalization.NumberStyles.Currency);
                    CartItem newItem = new CartItem(productId, productName, price, quantity) { ImageUrl = ProductImage.ImageUrl };
                    cart.Add(newItem);
                }
                Session["Cart"] = cart; Response.Redirect("~/Cart.aspx");
            }
        }

        private void ShowErrorView() { ProductView.Visible = false; ErrorView.Visible = true; }
        public string GetStarRating(int rating) { StringBuilder stars = new StringBuilder(); for (int i = 1; i <= 5; i++) { stars.Append(i <= rating ? "★" : "☆"); } return stars.ToString(); }
    }
}