﻿<%--
 =======================================================================
 FILE: ManageStock.aspx (Final Version)
 =======================================================================
--%>
<%@ Page Title="Manage Stock" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="ManageStock.aspx.cs" Inherits="MediEase.Pharmacist.ManageStock" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>Product Stock Levels</h1>
            <a href="Dashboard.aspx" class="btn btn-outline-secondary">&larr; Back to Dashboard</a>
        </div>

        <div class="card shadow-sm">
            <div class="card-header">
                <h4 class="mb-0">Inventory Overview</h4>
            </div>
            <div class="card-body">
                <asp:GridView ID="StockGrid" runat="server"
                    CssClass="table table-hover table-striped"
                    AutoGenerateColumns="False"
                    DataKeyNames="ProductID"
                    GridLines="None"
                    AllowPaging="True"
                    OnPageIndexChanging="StockGrid_PageIndexChanging"
                    OnRowDataBound="StockGrid_RowDataBound"
                    PageSize="15">
                    <Columns>
                        <asp:BoundField DataField="ProductID" HeaderText="ID" />
                        <asp:BoundField DataField="Name" HeaderText="Product Name" />
                        <asp:BoundField DataField="TotalStock" HeaderText="Total Stock" />
                        <asp:BoundField DataField="ReorderLevel" HeaderText="Reorder Level" />
                        <asp:TemplateField HeaderText="Status">
                            <ItemTemplate>
                                <asp:Label ID="StatusLabel" runat="server" CssClass="badge badge-pill"></asp:Label>
                            </ItemTemplate>
                        </asp:TemplateField>
                        <asp:HyperLinkField DataNavigateUrlFields="ProductID"
                            DataNavigateUrlFormatString="~/Pharmacist/StockDetails.aspx?ProductID={0}"
                            Text="Manage Batches"
                            ControlStyle-CssClass="btn btn-sm btn-info" />
                    </Columns>
                    <EmptyDataTemplate>
                        <div class="alert alert-info text-center">
                            There are no products in the system to manage stock for.
                        </div>
                    </EmptyDataTemplate>
                </asp:GridView>
            </div>
        </div>
    </div>
</asp:Content>