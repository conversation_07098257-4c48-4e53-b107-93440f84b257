﻿/* FILE: Pharmacist/ManagePricing.aspx.cs */
using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace MediEase.Pharmacist
{
    public partial class ManagePricing : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (Session["UserID"] == null || Session["RoleName"]?.ToString() != "Pharmacist") { Response.Redirect("~/Login.aspx"); return; }
            if (!IsPostBack) { BindPricingGrid(); }
        }

        private void BindPricingGrid()
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "SELECT p.ProductID, p.Name, c.CategoryName, b.BrandName, pr.Price FROM Products p LEFT JOIN Categories c ON p.CategoryID = c.CategoryID LEFT JOIN Brands b ON p.BrandID = b.BrandID LEFT JOIN ( SELECT ProductID, Price, ROW_NUMBER() OVER(PARTITION BY ProductID ORDER BY EffectiveDate DESC) as rn FROM Pricing ) pr ON p.ProductID = pr.ProductID AND pr.rn = 1 WHERE p.IsActive = 1 ORDER BY p.Name;";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    try
                    {
                        con.Open();
                        SqlDataAdapter sda = new SqlDataAdapter(cmd);
                        DataTable dt = new DataTable();
                        sda.Fill(dt);
                        PricingGrid.DataSource = dt;
                        PricingGrid.DataBind();
                    }
                    catch (Exception ex) { System.Diagnostics.Debug.WriteLine("Error binding pricing grid: " + ex.Message); }
                }
            }
        }

        protected void PricingGrid_PageIndexChanging(object sender, GridViewPageEventArgs e)
        {
            PricingGrid.PageIndex = e.NewPageIndex;
            BindPricingGrid();
        }

        protected void PricingGrid_RowEditing(object sender, GridViewEditEventArgs e)
        {
            PricingGrid.EditIndex = e.NewEditIndex;
            BindPricingGrid();
        }

        protected void PricingGrid_RowCancelingEdit(object sender, GridViewCancelEditEventArgs e)
        {
            PricingGrid.EditIndex = -1;
            BindPricingGrid();
        }

        protected void PricingGrid_RowUpdating(object sender, GridViewUpdateEventArgs e)
        {
            int productId = Convert.ToInt32(PricingGrid.DataKeys[e.RowIndex].Value);
            TextBox txtPrice = (TextBox)PricingGrid.Rows[e.RowIndex].FindControl("txtPrice");
            int pharmacistId = Convert.ToInt32(Session["UserID"]);

            if (decimal.TryParse(txtPrice.Text, out decimal newPrice))
            {
                string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    const string query = "INSERT INTO Pricing (ProductID, Price, EffectiveDate, SetByUserID) VALUES (@ProductID, @Price, GETDATE(), @UserID)";
                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        cmd.Parameters.AddWithValue("@ProductID", productId);
                        cmd.Parameters.AddWithValue("@Price", newPrice);
                        cmd.Parameters.AddWithValue("@UserID", pharmacistId);
                        try
                        {
                            con.Open();
                            cmd.ExecuteNonQuery();
                            MessagePlaceholder.Visible = true;
                        }
                        catch (Exception ex) { System.Diagnostics.Debug.WriteLine("Error updating price: " + ex.Message); }
                    }
                }
            }
            PricingGrid.EditIndex = -1;
            BindPricingGrid();
        }
    }
}