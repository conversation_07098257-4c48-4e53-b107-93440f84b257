﻿<%--
 =======================================================================
 FILE: Blog.aspx (Final Corrected Version)
 =======================================================================
--%>
<%@ Page Title="Health & Wellness Blog" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="Blog.aspx.cs" Inherits="MediEase.Blog" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-5">
        <div class="text-center mb-5">
            <h1>Health &amp; Wellness Blog</h1>
            <p class="lead">Articles and advice from our team to help you stay informed and healthy.</p>
        </div>
        <div class="row">
            <asp:Repeater ID="BlogPostsRepeater" runat="server" OnItemDataBound="BlogPostsRepeater_ItemDataBound">
                <ItemTemplate>
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card shadow-sm h-100">
                            <asp:HyperLink ID="PostLinkTop" runat="server" NavigateUrl='<%# "Post.aspx?PostID=" + Eval("PostID") %>'>
                                <asp:Image ID="PostImage" runat="server" ImageUrl='<%# Eval("ImageUrl") %>' CssClass="card-img-top" style="height: 200px; object-fit: cover;" />
                            </asp:HyperLink>
                            <div class="card-body d-flex flex-column">
                                <h5 class="card-title">
                                     <asp:HyperLink ID="PostLinkBottom" runat="server" NavigateUrl='<%# "Post.aspx?PostID=" + Eval("PostID") %>' Text='<%# Eval("Title") %>' />
                                </h5>
                                <small class="text-muted mb-2">
                                    By <%# Eval("AuthorName") %> on <%# Convert.ToDateTime(Eval("PublishDate")).ToString("MMMM dd,DamageTook") %>
                                </small>
                                <div class="mt-auto">
                                    <asp:HyperLink ID="ReadMoreLink" runat="server" NavigateUrl='<%# "Post.aspx?PostID=" + Eval("PostID") %>' CssClass="btn btn-outline-primary">Read More &rarr;</asp:HyperLink>
                                </div>
                            </div>
                        </div>
                    </div>
                </ItemTemplate>
            </asp:Repeater>
        </div>
        <asp:PlaceHolder ID="NoPostsView" runat="server" Visible="false">
             <div class="alert alert-info text-center"><p>There are no blog posts yet. Please check back soon!</p></div>
        </asp:PlaceHolder>
    </div>
</asp:Content>