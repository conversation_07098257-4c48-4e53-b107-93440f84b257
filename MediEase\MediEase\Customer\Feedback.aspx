﻿<%--
 =======================================================================
 FILE: Feedback.aspx (Final Version)
 =======================================================================
--%>
<%@ Page Title="Submit Feedback" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="Feedback.aspx.cs" Inherits="MediEase.Customer.Feedback" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>Feedback &amp; Complaints</h1>
            <a href="Dashboard.aspx" class="btn btn-outline-secondary">&larr; Back to Dashboard</a>
        </div>

        <div class="row">
            <!-- New Feedback Form -->
            <div class="col-lg-6">
                <div class="card shadow-sm">
                    <div class="card-header">
                        <h4 class="mb-0">Submit New Feedback</h4>
                    </div>
                    <div class="card-body">
                        <asp:PlaceHolder ID="FormView" runat="server">
                             <div class="form-group">
                                <label>Subject</label>
                                <asp:TextBox ID="SubjectBox" runat="server" CssClass="form-control" placeholder="e.g., Issue with my last order"></asp:TextBox>
                                <asp:RequiredFieldValidator runat="server" ControlToValidate="SubjectBox" ErrorMessage="A subject is required." CssClass="text-danger" Display="Dynamic" />
                            </div>
                            <div class="form-group">
                                <label>Your Message</label>
                                <asp:TextBox ID="MessageBox" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="6"></asp:TextBox>
                                <asp:RequiredFieldValidator runat="server" ControlToValidate="MessageBox" ErrorMessage="A message is required." CssClass="text-danger" Display="Dynamic" />
                            </div>
                            <asp:Button ID="SubmitButton" runat="server" Text="Submit Feedback" CssClass="btn btn-primary" OnClick="SubmitButton_Click" />
                        </asp:PlaceHolder>
                        <asp:PlaceHolder ID="SuccessMessage" runat="server" Visible="false">
                            <div class="alert alert-success">
                                <h5>Thank You!</h5>
                                <p>Your feedback has been submitted successfully. Our team will review it shortly.</p>
                            </div>
                        </asp:PlaceHolder>
                    </div>
                </div>
            </div>
            
            <!-- Feedback History -->
            <div class="col-lg-6">
                 <h4>Your Feedback History</h4>
                 <hr />
                 <asp:GridView ID="FeedbackHistoryGrid" runat="server"
                    CssClass="table table-bordered table-striped"
                    AutoGenerateColumns="False"
                    DataKeyNames="FeedbackID"
                    GridLines="None"
                    AllowPaging="True"
                    PageSize="10">
                     <Columns>
                         <asp:BoundField DataField="Subject" HeaderText="Subject" />
                         <asp:BoundField DataField="SubmissionDate" HeaderText="Date Sent" DataFormatString="{0:MMM dd,ꗤ}" />
                         <asp:TemplateField HeaderText="Status">
                             <ItemTemplate>
                                 <span class='badge <%# Eval("Status").ToString() == "Resolved" ? "badge-success" : "badge-info" %>'>
                                     <%# Eval("Status") %>
                                 </span>
                             </ItemTemplate>
                         </asp:TemplateField>
                     </Columns>
                     <EmptyDataTemplate>
                         <div class="alert alert-light text-center">
                             You have not submitted any feedback yet.
                         </div>
                     </EmptyDataTemplate>
                 </asp:GridView>
            </div>
        </div>
    </div>
</asp:Content>