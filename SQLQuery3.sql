﻿/*
 =======================================================================
 MediEase: Database Schema Update for "Ask a Pharmacist" Feature
 PURPOSE: Adds a new table to store questions from customers and
          answers from pharmacists.
 =======================================================================
*/

-- Drop the table if it already exists to allow for recreation
IF OBJECT_ID('PharmacistQuestions', 'U') IS NOT NULL 
    DROP TABLE PharmacistQuestions;
GO

-- Create the new table
CREATE TABLE PharmacistQuestions (
    QuestionID INT PRIMARY KEY IDENTITY(1,1),
    CustomerID INT NOT NULL,
    Subject NVARCHAR(255) NOT NULL,
    QuestionText NVARCHAR(MAX) NOT NULL,
    QuestionDate DATETIME NOT NULL DEFAULT GETDATE(),
    IsAnswered BIT NOT NULL DEFAULT 0,
    AnswerText NVARCHAR(MAX) NULL,
    AnsweredByPharmacistID INT NULL,
    AnswerDate DATETIME NULL,

    CONSTRAINT FK_PharmacistQuestions_Customers FOREIGN KEY (CustomerID) REFERENCES Users(UserID),
    CONSTRAINT FK_PharmacistQuestions_Pharmacists FOREIGN KEY (AnsweredByPharmacistID) REFERENCES Users(UserID)
);
GO

PRINT 'PharmacistQuestions table created successfully.';
GO
