﻿<%--
 =======================================================================
 FILE: ManageOrders.aspx
 PURPOSE: Allows pharmacists to manage orders.
          **UPDATED** with controls to change order status.
 PLACE IN: The 'Pharmacist' folder in your project root.
 =======================================================================
--%>
<%@ Page Title="Manage All Orders" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="ManageOrders.aspx.cs" Inherits="MediEase.Pharmacist.ManageOrders" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>System-Wide Order History</h1>
            <a href="Dashboard.aspx" class="btn btn-outline-secondary">&larr; Back to Admin Dashboard</a>
        </div>

        <div class="card shadow-sm">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="mb-0">All Orders</h4>
                <div>
                    <label for="StatusFilter">Filter by Status:</label>
                    <asp:DropDownList ID="StatusFilter" runat="server" AutoPostBack="true" 
                        OnSelectedIndexChanged="StatusFilter_SelectedIndexChanged" CssClass="form-control d-inline-block w-auto">
                    </asp:DropDownList>
                </div>
            </div>
            <div class="card-body">
                <asp:GridView ID="OrdersGrid" runat="server"
                    CssClass="table table-hover table-striped"
                    AutoGenerateColumns="False"
                    DataKeyNames="OrderID"
                    GridLines="None"
                    AllowPaging="True"
                    OnPageIndexChanging="OrdersGrid_PageIndexChanging"
                    OnRowDataBound="OrdersGrid_RowDataBound"
                    OnRowCommand="OrdersGrid_RowCommand"
                    PageSize="10">
                    <Columns>
                        <asp:BoundField DataField="OrderID" HeaderText="Order ID" />
                        <asp:BoundField DataField="CustomerName" HeaderText="Customer Name" />
                        <asp:BoundField DataField="OrderDate" HeaderText="Date Placed" DataFormatString="{0:MMM dd, yyyy}" />
                        <asp:BoundField DataField="TotalAmount" HeaderText="Total" DataFormatString="{0:C}" />
                         <asp:TemplateField HeaderText="Current Status">
                            <ItemTemplate>
                                <span class='badge badge-pill <%# GetStatusBadgeClass(Eval("StatusName").ToString()) %>'>
                                    <%# Eval("StatusName") %>
                                </span>
                            </ItemTemplate>
                        </asp:TemplateField>
                        <asp:HyperLinkField DataNavigateUrlFields="OrderID"
                            DataNavigateUrlFormatString="~/Pharmacist/OrderDetails.aspx?OrderID={0}"
                            Text="View Details"
                            ControlStyle-CssClass="btn btn-sm btn-info" />
                        
                        <%-- ADDED: New column for updating status --%>
                        <asp:TemplateField HeaderText="Update Status">
                            <ItemTemplate>
                                <div class="input-group">
                                    <asp:DropDownList ID="StatusDropDown" runat="server" CssClass="form-control form-control-sm"></asp:DropDownList>
                                    <div class="input-group-append">
                                        <asp:Button ID="UpdateButton" runat="server" Text="Update" CssClass="btn btn-sm btn-secondary" CommandName="UpdateStatus" CommandArgument='<%# Eval("OrderID") %>' />
                                    </div>
                                </div>
                            </ItemTemplate>
                        </asp:TemplateField>
                    </Columns>
                     <EmptyDataTemplate>
                        <div class="alert alert-info text-center">
                            There are no orders with the selected status.
                        </div>
                    </EmptyDataTemplate>
                </asp:GridView>
            </div>
        </div>
    </div>
</asp:Content>