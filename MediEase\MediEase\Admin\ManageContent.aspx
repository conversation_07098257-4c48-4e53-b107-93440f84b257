﻿<%--
 =======================================================================
 FILE: ManageContent.aspx (Final Version)
 =======================================================================
--%>
<%@ Page Title="Manage Site Content" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="ManageContent.aspx.cs" Inherits="MediEase.Admin.ManageContent" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>Manage Site Content</h1>
            <a href="Dashboard.aspx" class="btn btn-outline-secondary">&larr; Back to Admin Dashboard</a>
        </div>

        <div class="card shadow-sm">
            <div class="card-header">
                <h4 class="mb-0">Page Content Editor</h4>
            </div>
            <div class="card-body">
                 <asp:PlaceHolder ID="MessagePlaceholder" runat="server" Visible="false">
                    <div class="alert alert-success">
                        Content updated successfully!
                    </div>
                </asp:PlaceHolder>

                <div class="form-group">
                    <label for="PageSelector"><strong>Select a page to edit:</strong></label>
                    <asp:DropDownList ID="PageSelector" runat="server" 
                        AutoPostBack="true" 
                        OnSelectedIndexChanged="PageSelector_SelectedIndexChanged"
                        CssClass="form-control">
                    </asp:DropDownList>
                </div>

                <div class="form-group">
                    <label for="ContentEditor"><strong>Page Content (supports basic HTML):</strong></label>
                    <asp:TextBox ID="ContentEditor" runat="server" 
                        TextMode="MultiLine" 
                        Rows="15" 
                        CssClass="form-control"></asp:TextBox>
                </div>
                
                <asp:Button ID="SaveChangesButton" runat="server" 
                    Text="Save Changes" 
                    CssClass="btn btn-primary"
                    OnClick="SaveChangesButton_Click" />
            </div>
        </div>
    </div>
</asp:Content>