﻿/*
 =======================================================================
 FILE: Contact.aspx.cs (Final Version with Form and Map)
 =======================================================================
*/
using System;
using System.Configuration;
using System.Data.SqlClient;
using System.Net;
using System.Net.Mail;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace MediEase
{
    public partial class Contact : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                LoadPageContent();
            }
        }

        private void LoadPageContent()
        {
            string pageName = "Contact";
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "SELECT ContentTitle, ContentBody FROM CMSContent WHERE PageName = @PageName";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@PageName", pageName);
                    try
                    {
                        con.Open();
                        SqlDataReader reader = cmd.ExecuteReader();
                        if (reader.Read())
                        {
                            ContentTitle.Text = $"<h1>{Server.HtmlEncode(reader["ContentTitle"].ToString())}</h1>";
                            ContentBody.Text = reader["ContentBody"].ToString();
                        }
                        else
                        {
                            ContentTitle.Text = "<h1>Contact & Location</h1>";
                            ContentBody.Text = "<p>Contact information is currently unavailable.</p>";
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine("Error loading Contact content: " + ex.Message);
                    }
                }
            }
        }

        protected void SubmitButton_Click(object sender, EventArgs e)
        {
            if (Page.IsValid)
            {
                try
                {
                    string fromEmail = "<EMAIL>";
                    string fromPassword = "ojtl edhu fqxy tevc"; // Use 16-character App Password from Google
                    string toEmail = "<EMAIL>";

                    MailMessage mailMessage = new MailMessage();
                    mailMessage.From = new MailAddress(fromEmail);
                    mailMessage.To.Add(toEmail);
                    mailMessage.Subject = "Contact Form: " + SubjectBox.Text;
                    mailMessage.IsBodyHtml = true;

                    string emailBody = $@"
                        <h3>New Message from MediEase Contact Form</h3>
                        <p><strong>From:</strong> {NameBox.Text}</p>
                        <p><strong>Email:</strong> {EmailBox.Text}</p>
                        <hr>
                        <p><strong>Message:</strong></p>
                        <p>{MessageBox.Text}</p>
                    ";
                    mailMessage.Body = emailBody;

                    SmtpClient smtpClient = new SmtpClient("smtp.gmail.com", 587);
                    smtpClient.EnableSsl = true;
                    smtpClient.Credentials = new NetworkCredential(fromEmail, fromPassword);

                    smtpClient.Send(mailMessage);

                    FormView.Visible = false;
                    SuccessMessage.Visible = true;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine("Contact Form Email Send Error: " + ex.ToString());
                    // Optionally display an error literal on the page
                }
            }
        }
    }
}