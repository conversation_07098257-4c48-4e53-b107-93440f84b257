﻿/*
 =======================================================================
 FILE: ManageOrders.aspx.cs (Full & Unabridged)
 PURPOSE: Backend logic for the pharmacist's order management page.
          Handles filtering, paging, and updating order statuses.
 PLACE IN: The 'Pharmacist' folder in your project root.
 =======================================================================
*/
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace MediEase.Pharmacist
{
    public partial class ManageOrders : Page
    {
        // Store the list of statuses in a static variable to avoid hitting the DB on every row
        private static List<ListItem> orderStatusList;

        protected void Page_Load(object sender, EventArgs e)
        {
            if (Session["UserID"] == null || Session["RoleName"]?.ToString() != "Pharmacist")
            {
                Response.Redirect("~/Login.aspx");
                return;
            }

            if (!IsPostBack)
            {
                LoadOrderStatusList(); // Load statuses once
                PopulateStatusFilter();
                BindOrdersGrid();
            }
        }

        private void LoadOrderStatusList()
        {
            if (orderStatusList == null)
            {
                orderStatusList = new List<ListItem>();
                string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    const string query = "SELECT StatusID, StatusName FROM OrderStatus ORDER BY StatusID";
                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        con.Open();
                        SqlDataReader reader = cmd.ExecuteReader();
                        while (reader.Read())
                        {
                            orderStatusList.Add(new ListItem(reader["StatusName"].ToString(), reader["StatusID"].ToString()));
                        }
                    }
                }
            }
        }

        private void PopulateStatusFilter()
        {
            StatusFilter.DataSource = orderStatusList;
            StatusFilter.DataTextField = "Text";
            StatusFilter.DataValueField = "Value";
            StatusFilter.DataBind();
            StatusFilter.Items.Insert(0, new ListItem("All Statuses", "0"));

            ListItem defaultItem = StatusFilter.Items.FindByText("Under Review");
            if (defaultItem != null)
            {
                StatusFilter.SelectedValue = defaultItem.Value;
            }
        }

        private void BindOrdersGrid()
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                var queryBuilder = new StringBuilder(@"
                    SELECT o.OrderID, up.FirstName + ' ' + up.LastName AS CustomerName, o.OrderDate, o.TotalAmount, os.StatusName
                    FROM Orders o
                    INNER JOIN UserProfiles up ON o.CustomerID = up.UserID
                    INNER JOIN OrderStatus os ON o.StatusID = os.StatusID
                ");
                var cmd = new SqlCommand();

                if (StatusFilter.SelectedValue != "0")
                {
                    queryBuilder.Append(" WHERE o.StatusID = @StatusID");
                    cmd.Parameters.AddWithValue("@StatusID", StatusFilter.SelectedValue);
                }
                queryBuilder.Append(" ORDER BY o.OrderDate DESC");

                cmd.CommandText = queryBuilder.ToString();
                cmd.Connection = con;
                try
                {
                    con.Open();
                    SqlDataAdapter sda = new SqlDataAdapter(cmd);
                    DataTable dt = new DataTable();
                    sda.Fill(dt);
                    OrdersGrid.DataSource = dt;
                    OrdersGrid.DataBind();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine("Error binding orders for pharmacist: " + ex.Message);
                }
            }
        }

        protected void OrdersGrid_RowDataBound(object sender, GridViewRowEventArgs e)
        {
            if (e.Row.RowType == DataControlRowType.DataRow)
            {
                DropDownList statusDropDown = (DropDownList)e.Row.FindControl("StatusDropDown");
                if (statusDropDown != null)
                {
                    statusDropDown.DataSource = orderStatusList;
                    statusDropDown.DataTextField = "Text";
                    statusDropDown.DataValueField = "Value";
                    statusDropDown.DataBind();
                }
            }
        }

        protected void OrdersGrid_RowCommand(object sender, GridViewCommandEventArgs e)
        {
            if (e.CommandName == "UpdateStatus")
            {
                GridViewRow row = (GridViewRow)(((Control)e.CommandSource).NamingContainer);
                DropDownList statusDropDown = (DropDownList)row.FindControl("StatusDropDown");

                int orderId = Convert.ToInt32(e.CommandArgument);
                int newStatusId = Convert.ToInt32(statusDropDown.SelectedValue);
                string newStatusName = statusDropDown.SelectedItem.Text;

                UpdateOrderStatus(orderId, newStatusId, newStatusName);
                BindOrdersGrid(); // Refresh grid to show changes
            }
        }

        private void UpdateOrderStatus(int orderId, int newStatusId, string newStatusName)
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                con.Open();
                // Get customer email and name for the notification
                string customerEmail = "";
                string customerName = "";
                const string customerQuery = @"SELECT u.Email, up.FirstName FROM Orders o JOIN Users u ON o.CustomerID = u.UserID JOIN UserProfiles up ON u.UserID = up.UserID WHERE o.OrderID = @OrderID";
                using (SqlCommand customerCmd = new SqlCommand(customerQuery, con))
                {
                    customerCmd.Parameters.AddWithValue("@OrderID", orderId);
                    SqlDataReader reader = customerCmd.ExecuteReader();
                    if (reader.Read())
                    {
                        customerEmail = reader["Email"].ToString();
                        customerName = reader["FirstName"].ToString();
                    }
                    reader.Close();
                }

                // Update the order status
                const string updateQuery = "UPDATE Orders SET StatusID = @StatusID WHERE OrderID = @OrderID";
                using (SqlCommand updateCmd = new SqlCommand(updateQuery, con))
                {
                    updateCmd.Parameters.AddWithValue("@StatusID", newStatusId);
                    updateCmd.Parameters.AddWithValue("@OrderID", orderId);
                    int rowsAffected = updateCmd.ExecuteNonQuery();

                    if (rowsAffected > 0)
                    {
                        int pharmacistId = Convert.ToInt32(Session["UserID"]);
                        AuditLogger.LogAction(pharmacistId, "Update Order Status", "Orders", orderId, newValues: $"StatusID = {newStatusId}");
                        EmailService.SendOrderStatusUpdateEmail(customerEmail, customerName, orderId, newStatusName);
                    }
                }
            }
        }

        protected void StatusFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            OrdersGrid.PageIndex = 0;
            BindOrdersGrid();
        }

        protected void OrdersGrid_PageIndexChanging(object sender, GridViewPageEventArgs e)
        {
            OrdersGrid.PageIndex = e.NewPageIndex;
            BindOrdersGrid();
        }

        public string GetStatusBadgeClass(string status)
        {
            switch (status.ToLower())
            {
                case "pending": case "under review": return "badge-warning";
                case "processing": return "badge-info";
                case "shipped": return "badge-success";
                case "delivered": return "badge-success";
                case "cancelled": case "rejected": return "badge-danger";
                default: return "badge-secondary";
            }
        }
    }
}