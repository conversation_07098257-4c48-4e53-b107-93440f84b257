﻿/*
 =======================================================================
 MediEase: Database Schema Update for Health & Wellness Blog
 PURPOSE: Adds a new table to store blog post articles.
 =======================================================================
*/

-- Drop the table if it already exists to allow for recreation
IF OBJECT_ID('BlogPosts', 'U') IS NOT NULL 
    DROP TABLE BlogPosts;
GO

-- Create the new table for blog posts
CREATE TABLE BlogPosts (
    PostID INT PRIMARY KEY IDENTITY(1,1),
    Title NVARCHAR(255) NOT NULL,
    Content NVARCHAR(MAX) NOT NULL,
    ImageUrl NVARCHAR(500) NULL, -- For a header image
    AuthorID INT NOT NULL, -- The UserID of the admin/pharmacist who wrote it
    PublishDate DATETIME NOT NULL DEFAULT GETDATE(),
    IsPublished BIT NOT NULL DEFAULT 0, -- Allows for saving drafts
    LastModifiedDate DATETIME NOT NULL DEFAULT GETDATE(),

    CONSTRAINT FK_BlogPosts_Author FOREIGN KEY (AuthorID) REFERENCES Users(UserID)
);
GO

PRINT 'BlogPosts table created successfully.';
GO

