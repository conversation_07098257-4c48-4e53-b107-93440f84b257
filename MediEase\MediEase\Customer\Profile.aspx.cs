﻿/* FILE: Customer/Profile.aspx.cs */
using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Web.UI;
using System.Web.UI.HtmlControls;
using System.Web.UI.WebControls;

namespace MediEase.Customer
{
    public partial class Profile : Page
    {
        private int customerId;
        protected void Page_Load(object sender, EventArgs e)
        {
            if (Session["UserID"] == null || Session["RoleName"]?.ToString() != "Customer") { Response.Redirect("~/Login.aspx"); return; }
            customerId = Convert.ToInt32(Session["UserID"]);
            if (!IsPostBack) { LoadUserProfile(); BindFamilyProfiles(); }
        }

        private void LoadUserProfile()
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "SELECT FirstName, LastName, PhoneNumber, Address, DateOfBirth FROM UserProfiles WHERE UserID = @UserID";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@UserID", customerId);
                    try { con.Open(); SqlDataReader reader = cmd.ExecuteReader(); if (reader.Read()) { FirstNameBox.Text = reader["FirstName"]?.ToString(); LastNameBox.Text = reader["LastName"]?.ToString(); PhoneBox.Text = reader["PhoneNumber"]?.ToString(); AddressBox.Text = reader["Address"]?.ToString(); if (reader["DateOfBirth"] != DBNull.Value) { DobBox.Text = Convert.ToDateTime(reader["DateOfBirth"]).ToString("yyyy-MM-dd"); } } }
                    catch (Exception ex) { ShowMessage("Error loading profile: " + ex.Message, false); }
                }
            }
        }

        protected void SaveChangesButton_Click(object sender, EventArgs e)
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "UPDATE UserProfiles SET FirstName = @FirstName, LastName = @LastName, PhoneNumber = @Phone, Address = @Address, DateOfBirth = @Dob WHERE UserID = @UserID";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@FirstName", FirstNameBox.Text.Trim()); cmd.Parameters.AddWithValue("@LastName", LastNameBox.Text.Trim()); cmd.Parameters.AddWithValue("@Phone", PhoneBox.Text.Trim()); cmd.Parameters.AddWithValue("@Address", AddressBox.Text.Trim()); cmd.Parameters.AddWithValue("@UserID", customerId);
                    if (DateTime.TryParse(DobBox.Text, out DateTime dob)) { cmd.Parameters.AddWithValue("@Dob", dob); } else { cmd.Parameters.AddWithValue("@Dob", DBNull.Value); }
                    try { con.Open(); cmd.ExecuteNonQuery(); ShowMessage("Profile details updated successfully!", true); }
                    catch (Exception ex) { ShowMessage("Error updating profile: " + ex.Message, false); }
                }
            }
        }

        protected void UpdatePasswordButton_Click(object sender, EventArgs e)
        {
            string currentPassword = CurrentPasswordBox.Text; string newPassword = NewPasswordBox.Text;
            if (string.IsNullOrEmpty(currentPassword) || string.IsNullOrEmpty(newPassword)) { ShowMessage("All password fields are required.", false); return; }
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                try
                {
                    con.Open();
                    const string verifyQuery = "SELECT Password FROM Users WHERE UserID = @UserID";
                    string dbPassword = "";
                    using (SqlCommand verifyCmd = new SqlCommand(verifyQuery, con)) { verifyCmd.Parameters.AddWithValue("@UserID", customerId); object result = verifyCmd.ExecuteScalar(); if (result != null) { dbPassword = result.ToString(); } }
                    if (dbPassword == currentPassword)
                    {
                        const string updateQuery = "UPDATE Users SET Password = @NewPassword WHERE UserID = @UserID";
                        using (SqlCommand updateCmd = new SqlCommand(updateQuery, con)) { updateCmd.Parameters.AddWithValue("@NewPassword", newPassword); updateCmd.Parameters.AddWithValue("@UserID", customerId); updateCmd.ExecuteNonQuery(); ShowMessage("Password updated successfully!", true); CurrentPasswordBox.Text = ""; NewPasswordBox.Text = ""; ConfirmNewPasswordBox.Text = ""; }
                    }
                    else { ShowMessage("Incorrect current password.", false); }
                }
                catch (Exception ex) { ShowMessage("Error updating password: " + ex.Message, false); }
            }
        }

        private void BindFamilyProfiles()
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "SELECT FamilyProfileID, PatientName, Relationship, DateOfBirth FROM FamilyProfiles WHERE PrimaryUserID = @PrimaryUserID ORDER BY PatientName";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@PrimaryUserID", customerId);
                    try { con.Open(); SqlDataAdapter sda = new SqlDataAdapter(cmd); DataTable dt = new DataTable(); sda.Fill(dt); FamilyProfilesGrid.DataSource = dt; FamilyProfilesGrid.DataBind(); }
                    catch (Exception ex) { ShowMessage("Error loading family profiles: " + ex.Message, false); }
                }
            }
        }

        protected void AddFamilyMemberButton_Click(object sender, EventArgs e)
        {
            string name = FamilyNameBox.Text.Trim(); string relationship = FamilyRelationshipBox.Text.Trim();
            if (string.IsNullOrEmpty(name) || string.IsNullOrEmpty(relationship)) { ShowMessage("Family member name and relationship are required.", false); return; }
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "INSERT INTO FamilyProfiles (PrimaryUserID, PatientName, Relationship, DateOfBirth) VALUES (@PrimaryUserID, @Name, @Relationship, @Dob)";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@PrimaryUserID", customerId); cmd.Parameters.AddWithValue("@Name", name); cmd.Parameters.AddWithValue("@Relationship", relationship);
                    if (DateTime.TryParse(FamilyDobBox.Text, out DateTime dob)) { cmd.Parameters.AddWithValue("@Dob", dob); } else { cmd.Parameters.AddWithValue("@Dob", DBNull.Value); }
                    try { con.Open(); cmd.ExecuteNonQuery(); ShowMessage("Family profile added successfully!", true); FamilyNameBox.Text = ""; FamilyRelationshipBox.Text = ""; FamilyDobBox.Text = ""; BindFamilyProfiles(); }
                    catch (Exception ex) { ShowMessage("Error adding family profile: " + ex.Message, false); }
                }
            }
        }

        protected void FamilyProfilesGrid_RowDeleting(object sender, GridViewDeleteEventArgs e)
        {
            int familyProfileId = Convert.ToInt32(FamilyProfilesGrid.DataKeys[e.RowIndex].Value);
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "DELETE FROM FamilyProfiles WHERE FamilyProfileID = @FamilyProfileID AND PrimaryUserID = @PrimaryUserID";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@FamilyProfileID", familyProfileId);
                    cmd.Parameters.AddWithValue("@PrimaryUserID", customerId);
                    try { con.Open(); cmd.ExecuteNonQuery(); ShowMessage("Family profile deleted successfully.", true); }
                    catch (Exception ex) { ShowMessage("Error deleting family profile: " + ex.Message, false); }
                }
            }
            BindFamilyProfiles();
        }

        private void ShowMessage(string message, bool isSuccess) { MessageText.Text = message; MessageAlert.Attributes["class"] = isSuccess ? "alert alert-success" : "alert alert-danger"; MessagePlaceholder.Visible = true; }
    }
}