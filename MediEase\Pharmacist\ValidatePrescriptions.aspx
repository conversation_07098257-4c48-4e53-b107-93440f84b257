﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="ValidatePrescriptions.aspx.cs" Inherits="Pharmacist_ValidatePrescriptions" %>

<!DOCTYPE html>
<html lang="en">
<head runat="server">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Validate Prescriptions - MediEase Pharmacist</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <style>
        body { font-family: 'Inter', sans-serif; }
        .sidebar-link { display: flex; align-items: center; padding: 0.75rem 1rem; border-radius: 0.5rem; transition: background-color 0.2s, color 0.2s; }
        .sidebar-link:hover, .sidebar-link.active { background-color: #0d9488; color: white; }
        .sidebar-link i { width: 1.25rem; margin-right: 0.75rem; }
    </style>
</head>
<body class="bg-gray-100">
    <form id="form1" runat="server">
        <div class="flex h-screen bg-gray-100">
            <!-- Sidebar -->
            <aside class="w-64 bg-teal-600 text-white flex-col p-4 hidden md:flex">
                <div class="flex items-center space-x-2 pb-4 border-b border-teal-500">
                    <i class="fas fa-pills text-3xl"></i>
                    <span class="text-2xl font-bold">MediEase</span>
                </div>
                <nav class="mt-6 flex-grow">
                    <a href="Dashboard.aspx" class="sidebar-link"><i class="fas fa-tachometer-alt fa-fw"></i><span>Dashboard</span></a>
                    <a href="ManageOrders.aspx" class="sidebar-link"><i class="fas fa-tasks fa-fw"></i><span>Manage Orders</span></a>
                    <a href="ManageStock.aspx" class="sidebar-link"><i class="fas fa-boxes fa-fw"></i><span>Manage Stock</span></a>
                    <a href="ValidatePrescriptions.aspx" class="sidebar-link active"><i class="fas fa-file-prescription fa-fw"></i><span>Validate Prescriptions</span></a>
                </nav>
                <div class="pt-4 border-t border-teal-500">
                    <asp:LinkButton ID="btnLogout" runat="server" OnClick="btnLogout_Click" CssClass="sidebar-link w-full"><i class="fas fa-sign-out-alt fa-fw"></i><span>Logout</span></asp:LinkButton>
                </div>
            </aside>

            <!-- Main Content -->
            <main class="flex-1 flex flex-col">
                <header class="bg-white shadow-sm p-4">
                    <h1 class="text-2xl font-bold text-gray-800">Validate Prescriptions</h1>
                </header>

                <div class="flex-1 p-6 overflow-y-auto">
                    <!-- Search and Filter -->
                    <div class="bg-white p-4 rounded-lg shadow mb-6">
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
                            <div class="md:col-span-2">
                                <label class="block text-sm font-medium text-gray-700">Search by Prescription # or Customer</label>
                                <asp:TextBox ID="txtSearch" runat="server" CssClass="mt-1 block w-full input-style"></asp:TextBox>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Filter by Status</label>
                                <asp:DropDownList ID="ddlStatusFilter" runat="server" CssClass="mt-1 block w-full input-style">
                                    <asp:ListItem Value="" Text="All"></asp:ListItem>
                                    <asp:ListItem Value="Pending" Text="Pending" Selected="True"></asp:ListItem>
                                    <asp:ListItem Value="Verified" Text="Verified"></asp:ListItem>
                                    <asp:ListItem Value="Rejected" Text="Rejected"></asp:ListItem>
                                </asp:DropDownList>
                            </div>
                            <div class="flex space-x-2">
                                <asp:Button ID="btnSearch" runat="server" Text="Search" OnClick="btnSearch_Click" CssClass="w-full justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-teal-600 hover:bg-teal-700" />
                                <asp:Button ID="btnClear" runat="server" Text="Clear" OnClick="btnClear_Click" CssClass="w-full justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50" />
                            </div>
                        </div>
                    </div>

                    <!-- Prescriptions Grid -->
                    <div class="bg-white p-6 rounded-lg shadow overflow-x-auto">
                         <asp:GridView ID="gvPrescriptions" runat="server" AutoGenerateColumns="False"
                             DataKeyNames="PrescriptionId"
                             CssClass="min-w-full divide-y divide-gray-200"
                             HeaderStyle-CssClass="bg-gray-50" PagerStyle-CssClass="bg-white p-4"
                             AllowPaging="True" PageSize="15" OnPageIndexChanging="gvPrescriptions_PageIndexChanging">
                             <Columns>
                                 <asp:BoundField DataField="PrescriptionNumber" HeaderText="Prescription #" HeaderStyle-CssClass="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" ItemStyle-CssClass="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900" />
                                 <asp:BoundField DataField="CustomerName" HeaderText="Customer" HeaderStyle-CssClass="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" ItemStyle-CssClass="px-6 py-4 whitespace-nowrap text-sm text-gray-700" />
                                 <asp:BoundField DataField="PrescriptionDate" HeaderText="Prescription Date" DataFormatString="{0:MMM dd, yyyy}" HeaderStyle-CssClass="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" ItemStyle-CssClass="px-6 py-4 whitespace-nowrap text-sm text-gray-500" />
                                 <asp:BoundField DataField="DoctorName" HeaderText="Doctor" HeaderStyle-CssClass="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" ItemStyle-CssClass="px-6 py-4 whitespace-nowrap text-sm text-gray-500" />
                                 <asp:TemplateField HeaderText="Status" HeaderStyle-CssClass="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" ItemStyle-CssClass="px-6 py-4 whitespace-nowrap">
                                     <ItemTemplate>
                                         <span class='px-2 inline-flex text-xs leading-5 font-semibold rounded-full <%# GetStatusClass(Eval("Status").ToString()) %>'>
                                             <%# Eval("Status") %>
                                         </span>
                                     </ItemTemplate>
                                 </asp:TemplateField>
                                 <asp:TemplateField HeaderText="Action" HeaderStyle-CssClass="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider" ItemStyle-CssClass="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                                     <ItemTemplate>
                                         <a href='PrescriptionDetail.aspx?id=<%# Eval("PrescriptionId") %>' class="text-teal-600 hover:text-teal-900">View & Validate</a>
                                     </ItemTemplate>
                                 </asp:TemplateField>
                             </Columns>
                             <EmptyDataTemplate>
                                 <div class="text-center py-8"><p class="text-gray-500">No prescriptions found matching your criteria.</p></div>
                             </EmptyDataTemplate>
                         </asp:GridView>
                    </div>
                </div>
            </main>
        </div>
    </form>
</body>
</html>
