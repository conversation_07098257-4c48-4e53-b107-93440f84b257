﻿using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web.UI;
using System.Text;
using System.Collections.Generic;
using System.Web.UI.WebControls;

public partial class Pharmacist_ManageOrders : System.Web.UI.Page
{
    protected void Page_Load(object sender, EventArgs e)
    {
        // --- SECURITY CHECK ---
        if (Session["UserId"] == null || Session["UserRole"]?.ToString() != "Pharmacist")
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        if (!IsPostBack)
        {
            BindOrdersGrid();
        }
    }

    private void BindOrdersGrid()
    {
        string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
        using (SqlConnection con = new SqlConnection(connectionString))
        {
            StringBuilder queryBuilder = new StringBuilder(@"
                SELECT 
                    o.OrderId, o.OrderNumber, o.OrderDate, o.TotalAmount, o.Status, o.RequiresPrescription,
                    u.FirstName + ' ' + u.LastName AS CustomerName
                FROM Orders o
                INNER JOIN Users u ON o.CustomerId = u.UserId
            ");

            SqlCommand cmd = new SqlCommand();
            List<string> conditions = new List<string>();

            // Search Term Filter (Order Number or Customer Name)
            if (!string.IsNullOrWhiteSpace(txtSearch.Text))
            {
                conditions.Add("(o.OrderNumber LIKE @Search OR u.FirstName LIKE @Search OR u.LastName LIKE @Search)");
                cmd.Parameters.AddWithValue("@Search", "%" + txtSearch.Text.Trim() + "%");
            }

            // Status Filter
            if (!string.IsNullOrEmpty(ddlStatusFilter.SelectedValue))
            {
                conditions.Add("o.Status = @Status");
                cmd.Parameters.AddWithValue("@Status", ddlStatusFilter.SelectedValue);
            }

            // Prescription Filter
            if (!string.IsNullOrEmpty(ddlPrescriptionFilter.SelectedValue))
            {
                conditions.Add("o.RequiresPrescription = @RequiresPrescription");
                cmd.Parameters.AddWithValue("@RequiresPrescription", Convert.ToInt32(ddlPrescriptionFilter.SelectedValue));
            }

            if (conditions.Count > 0)
            {
                queryBuilder.Append(" WHERE " + string.Join(" AND ", conditions));
            }

            queryBuilder.Append(" ORDER BY o.OrderDate DESC");

            cmd.CommandText = queryBuilder.ToString();
            cmd.Connection = con;

            try
            {
                con.Open();
                using (SqlDataAdapter sda = new SqlDataAdapter(cmd))
                {
                    DataTable dt = new DataTable();
                    sda.Fill(dt);
                    gvOrders.DataSource = dt;
                    gvOrders.DataBind();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine(ex.Message);
                // Optionally show an error message on the page
            }
        }
    }

    protected void btnSearch_Click(object sender, EventArgs e)
    {
        gvOrders.PageIndex = 0;
        BindOrdersGrid();
    }

    protected void btnClear_Click(object sender, EventArgs e)
    {
        txtSearch.Text = string.Empty;
        ddlStatusFilter.SelectedIndex = 0;
        ddlPrescriptionFilter.SelectedIndex = 0;
        gvOrders.PageIndex = 0;
        BindOrdersGrid();
    }

    protected void gvOrders_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        gvOrders.PageIndex = e.NewPageIndex;
        BindOrdersGrid();
    }

    protected void btnLogout_Click(object sender, EventArgs e)
    {
        Session.Clear();
        Session.Abandon();
        Response.Redirect("~/Login.aspx");
    }

    public string GetStatusClass(string status)
    {
        switch (status.ToLower())
        {
            case "pending": return "bg-yellow-100 text-yellow-800";
            case "processing": return "bg-blue-100 text-blue-800";
            case "shipped": return "bg-purple-100 text-purple-800";
            case "delivered": return "bg-green-100 text-green-800";
            case "cancelled": return "bg-red-100 text-red-800";
            default: return "bg-gray-100 text-gray-800";
        }
    }
}
