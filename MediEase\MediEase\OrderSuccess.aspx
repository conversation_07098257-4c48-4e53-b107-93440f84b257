﻿<%--
 =======================================================================
 FILE: OrderSuccess.aspx (Final Version)
 =======================================================================
--%>
<%@ Page Title="Order Placed Successfully" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="OrderSuccess.aspx.cs" Inherits="MediEase.OrderSuccess" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-5">
        <asp:PlaceHolder ID="SuccessView" runat="server">
            <div class="text-center py-5 px-3">
                <div class="mb-4">
                    <i class="fas fa-check-circle fa-5x text-success"></i>
                </div>
                <h1 class="display-4">Thank You!</h1>
                <p class="lead">Your order has been placed successfully.</p>
                <hr class="my-4" />
                <p>
                    Your Order ID is: <strong><asp:Label ID="OrderIDLabel" runat="server" Text="0000"></asp:Label></strong>.
                </p>
                <p class="text-muted">
                    Your payment is currently under verification. You will receive an on-site notification once your order is approved.
                </p>
                <div class="mt-4">
                    <a href="Default.aspx" class="btn btn-primary btn-lg">Return to Home Page</a>
                    <a href="Customer/Dashboard.aspx" class="btn btn-secondary btn-lg">View Order History</a>
                </div>
            </div>
        </asp:PlaceHolder>

        <asp:PlaceHolder ID="ErrorView" runat="server" Visible="false">
             <div class="alert alert-danger text-center">
                <h2>Invalid Request</h2>
                <p>No order details were found. Please place an order through the checkout page.</p>
                <a href="Shop.aspx" class="btn btn-primary">Return to Shop</a>
            </div>
        </asp:PlaceHolder>
    </div>
</asp:Content>