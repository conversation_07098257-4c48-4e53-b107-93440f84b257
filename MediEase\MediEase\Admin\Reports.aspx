﻿<%--
 =======================================================================
 FILE: Reports.aspx
 PURPOSE: Allows an administrator to view comprehensive reports on
          sales and orders with filtering.
 PLACE IN: The 'Admin' folder in your project root.
 =======================================================================
--%>
<%@ Page Title="System Reports" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="Reports.aspx.cs" Inherits="MediEase.Admin.Reports" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>System Reports</h1>
            <a href="Dashboard.aspx" class="btn btn-outline-secondary">&larr; Back to Admin Dashboard</a>
        </div>

        <div class="card shadow-sm mb-4">
            <div class="card-header">
                <h5 class="mb-0">Generate Order Report</h5>
            </div>
            <div class="card-body">
                <div class="form-row align-items-end">
                    <div class="form-group col-md-4">
                        <label>Start Date</label>
                        <asp:TextBox ID="StartDateBox" runat="server" CssClass="form-control" TextMode="Date"></asp:TextBox>
                    </div>
                    <div class="form-group col-md-4">
                        <label>End Date</label>
                        <asp:TextBox ID="EndDateBox" runat="server" CssClass="form-control" TextMode="Date"></asp:TextBox>
                    </div>
                    <div class="form-group col-md-2">
                        <label>Status</label>
                        <asp:DropDownList ID="StatusFilter" runat="server" CssClass="form-control"></asp:DropDownList>
                    </div>
                    <div class="form-group col-md-2">
                        <asp:Button ID="GenerateReportButton" runat="server" Text="Generate" CssClass="btn btn-primary btn-block" OnClick="GenerateReportButton_Click" />
                    </div>
                </div>
            </div>
        </div>

        <asp:PlaceHolder ID="ReportView" runat="server" Visible="false">
            <h4 class="mb-3">Report for <asp:Literal ID="DateRangeLabel" runat="server"></asp:Literal></h4>
            <div class="row text-center mb-4">
                <div class="col-md-4">
                    <div class="card bg-light"><div class="card-body">
                        <h6 class="card-title">Total Revenue</h6>
                        <p class="h3 text-success"><asp:Literal ID="TotalRevenueLabel" runat="server"></asp:Literal></p>
                    </div></div>
                </div>
                <div class="col-md-4">
                     <div class="card bg-light"><div class="card-body">
                        <h6 class="card-title">Total Orders</h6>
                        <p class="h3"><asp:Literal ID="TotalOrdersLabel" runat="server"></asp:Literal></p>
                    </div></div>
                </div>
                <div class="col-md-4">
                     <div class="card bg-light"><div class="card-body">
                        <h6 class="card-title">Average Order Value</h6>
                        <p class="h3"><asp:Literal ID="AvgOrderValueLabel" runat="server"></asp:Literal></p>
                    </div></div>
                </div>
            </div>
            <div class="card shadow-sm">
                <div class="card-body">
                    <asp:GridView ID="OrdersReportGrid" runat="server"
                        CssClass="table table-striped"
                        AutoGenerateColumns="False"
                        GridLines="None"
                        EmptyDataText="No order data found for the selected criteria.">
                        <Columns>
                            <asp:BoundField DataField="OrderID" HeaderText="Order ID" />
                            <asp:BoundField DataField="CustomerName" HeaderText="Customer" />
                            <asp:BoundField DataField="OrderDate" HeaderText="Date" DataFormatString="{0:d}" />
                            <asp:BoundField DataField="StatusName" HeaderText="Status" />
                            <asp:BoundField DataField="TotalAmount" HeaderText="Total" DataFormatString="{0:C}" />
                        </Columns>
                    </asp:GridView>
                </div>
            </div>
        </asp:PlaceHolder>
    </div>
</asp:Content>