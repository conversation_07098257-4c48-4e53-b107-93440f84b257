﻿<%--
 =======================================================================
 FILE: ManageFeedback.aspx
 PURPOSE: Allows pharmacists to view and manage feedback and complaints
          submitted by customers.
 PLACE IN: The 'Pharmacist' folder in your project root.
 =======================================================================
--%>
<%@ Page Title="Manage Customer Feedback" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="ManageFeedback.aspx.cs" Inherits="MediEase.Pharmacist.ManageFeedback" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>Manage Customer Feedback</h1>
            <a href="Dashboard.aspx" class="btn btn-outline-secondary">&larr; Back to Dashboard</a>
        </div>

        <div class="card shadow-sm">
            <div class="card-header">
                <ul class="nav nav-tabs card-header-tabs">
                    <li class="nav-item">
                        <asp:LinkButton ID="OpenTab" runat="server" CssClass="nav-link" OnClick="Tab_Click" CommandArgument="Open">Open</asp:LinkButton>
                    </li>
                    <li class="nav-item">
                        <asp:LinkButton ID="ResolvedTab" runat="server" CssClass="nav-link" OnClick="Tab_Click" CommandArgument="Resolved">Resolved</asp:LinkButton>
                    </li>
                </ul>
            </div>
            <div class="card-body">
                <asp:GridView ID="FeedbackGrid" runat="server"
                    CssClass="table table-hover table-striped"
                    AutoGenerateColumns="False"
                    DataKeyNames="FeedbackID"
                    GridLines="None"
                    AllowPaging="True"
                    OnPageIndexChanging="FeedbackGrid_PageIndexChanging"
                    PageSize="10">
                    <Columns>
                        <asp:BoundField DataField="FeedbackID" HeaderText="ID" />
                        <asp:BoundField DataField="CustomerName" HeaderText="Customer" />
                        <asp:BoundField DataField="Subject" HeaderText="Subject" />
                        <asp:BoundField DataField="SubmissionDate" HeaderText="Date Sent" DataFormatString="{0:MMM dd,<x_bin_342>}" />
                        <asp:HyperLinkField DataNavigateUrlFields="FeedbackID"
                            DataNavigateUrlFormatString="~/Pharmacist/FeedbackDetails.aspx?FeedbackID={0}"
                            Text="View &amp; Respond"
                            ControlStyle-CssClass="btn btn-sm btn-primary" />
                    </Columns>
                    <EmptyDataTemplate>
                        <div class="alert alert-info text-center">
                            There are no feedback submissions in this category.
                        </div>
                    </EmptyDataTemplate>
                </asp:GridView>
            </div>
        </div>
    </div>
</asp:Content>