using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class Admin_SystemSettings : System.Web.UI.Page
{
    protected void Page_Load(object sender, EventArgs e)
    {
        // --- SECURITY CHECK ---
        if (Session["UserId"] == null || Session["UserRole"]?.ToString() != "Admin")
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        if (!IsPostBack)
        {
            LoadSettings();
        }
    }

    /// <summary>
    /// Loads all editable settings from the database and binds them to the repeater.
    /// </summary>
    private void LoadSettings()
    {
        string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
        using (SqlConnection con = new SqlConnection(connectionString))
        {
            string query = "SELECT SettingKey, SettingValue, Description FROM SystemSettings WHERE IsEditable = 1 ORDER BY Category, SettingKey";
            try
            {
                SqlDataAdapter sda = new SqlDataAdapter(query, con);
                DataTable dt = new DataTable();
                sda.Fill(dt);
                rptSettings.DataSource = dt;
                rptSettings.DataBind();
            }
            catch (Exception ex)
            {
                ShowMessage($"Error loading settings: {ex.Message}", "error");
            }
        }
    }

    /// <summary>
    /// Handles the click event for the Save Changes button.
    /// </summary>
    protected void btnSaveChanges_Click(object sender, EventArgs e)
    {
        int adminUserId = (int)Session["UserId"];
        string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;

        using (SqlConnection con = new SqlConnection(connectionString))
        {
            try
            {
                con.Open();
                foreach (RepeaterItem item in rptSettings.Items)
                {
                    HiddenField hdnSettingKey = (HiddenField)item.FindControl("hdnSettingKey");
                    HiddenField hdnOldValue = (HiddenField)item.FindControl("hdnOldValue");
                    TextBox txtSettingValue = (TextBox)item.FindControl("txtSettingValue");

                    string key = hdnSettingKey.Value;
                    string oldValue = hdnOldValue.Value;
                    string newValue = txtSettingValue.Text;

                    // Only update if the value has actually changed
                    if (oldValue != newValue)
                    {
                        string query = @"UPDATE SystemSettings SET SettingValue = @SettingValue, ModifiedDate = @ModifiedDate, ModifiedBy = @ModifiedBy WHERE SettingKey = @SettingKey";
                        using (SqlCommand cmd = new SqlCommand(query, con))
                        {
                            cmd.Parameters.AddWithValue("@SettingValue", newValue);
                            cmd.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);
                            cmd.Parameters.AddWithValue("@ModifiedBy", adminUserId);
                            cmd.Parameters.AddWithValue("@SettingKey", key);
                            cmd.ExecuteNonQuery();

                            // Log this change to the audit table
                            LogAudit(adminUserId, key, oldValue, newValue, con);
                        }
                    }
                }
                ShowMessage("Settings saved successfully!", "success");
            }
            catch (Exception ex)
            {
                ShowMessage($"Error saving settings: {ex.Message}", "error");
            }
        }

        // Reload the settings to reflect the changes (including the new "old" value)
        LoadSettings();
    }

    /// <summary>
    /// Logs a setting change to the AuditLogs table.
    /// </summary>
    private void LogAudit(int adminUserId, string settingKey, string oldValue, string newValue, SqlConnection con)
    {
        string auditQuery = @"
            INSERT INTO AuditLogs (UserId, Action, EntityType, OldValues, NewValues, IPAddress) 
            VALUES (@UserId, @Action, @EntityType, @OldValues, @NewValues, @IPAddress)";

        using (SqlCommand auditCmd = new SqlCommand(auditQuery, con))
        {
            auditCmd.Parameters.AddWithValue("@UserId", adminUserId);
            auditCmd.Parameters.AddWithValue("@Action", "Update Setting");
            auditCmd.Parameters.AddWithValue("@EntityType", "SystemSetting");
            auditCmd.Parameters.AddWithValue("@OldValues", $"Key: {settingKey}, Value: {oldValue}");
            auditCmd.Parameters.AddWithValue("@NewValues", $"Key: {settingKey}, Value: {newValue}");
            auditCmd.Parameters.AddWithValue("@IPAddress", Request.UserHostAddress);
            auditCmd.ExecuteNonQuery();
        }
    }

    protected void btnLogout_Click(object sender, EventArgs e)
    {
        Session.Clear();
        Session.Abandon();
        Response.Redirect("~/Login.aspx");
    }

    private void ShowMessage(string message, string type)
    {
        string cssClass = (type == "success")
            ? "bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg relative mb-4"
            : "bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg relative mb-4";

        litMessage.Text = $"<div class='{cssClass}' role='alert'>{message}</div>";
    }
}
