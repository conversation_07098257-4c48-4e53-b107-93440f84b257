﻿<%--
 =======================================================================
 FILE: QuestionDetails.aspx (Final Version)
 =======================================================================
--%>
<%@ Page Title="Question Details" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="QuestionDetails.aspx.cs" Inherits="MediEase.Customer.QuestionDetails" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-5">
        <asp:PlaceHolder ID="QuestionView" runat="server">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>Question Details</h1>
                <a href="Ask.aspx" class="btn btn-outline-secondary">&larr; Back to My Questions</a>
            </div>

            <div class="card shadow-sm">
                <div class="card-header">
                    <h4 class="mb-0">Subject: <asp:Literal ID="SubjectLabel" runat="server"></asp:Literal></h4>
                    <small class="text-muted">Asked on <asp:Literal ID="QuestionDateLabel" runat="server"></asp:Literal></small>
                </div>
                <div class="card-body">
                    <h5>Your Question:</h5>
                    <p style="white-space: pre-wrap;"><asp:Literal ID="QuestionTextLiteral" runat="server"></asp:Literal></p>
                </div>
                
                <asp:PlaceHolder ID="AnswerView" runat="server" Visible="false">
                    <div class="card-footer bg-light">
                         <h5>Pharmacist's Response:</h5>
                         <p style="white-space: pre-wrap;"><asp:Literal ID="AnswerTextLiteral" runat="server"></asp:Literal></p>
                         <small class="text-muted">Answered on <asp:Literal ID="AnswerDateLabel" runat="server"></asp:Literal></small>
                    </div>
                </asp:PlaceHolder>
            </div>
        </asp:PlaceHolder>

        <asp:PlaceHolder ID="ErrorView" runat="server" Visible="false">
             <div class="alert alert-danger text-center">
                <h2>Question Not Found</h2>
                <p>The question you are looking for does not exist or you do not have permission to view it.</p>
                <a href="Ask.aspx" class="btn btn-primary">Return to My Questions</a>
            </div>
        </asp:PlaceHolder>
    </div>
</asp:Content>