﻿/*
 =======================================================================
 FILE: Invoice.aspx.cs
 PURPOSE: Backend logic for the printable invoice page. Fetches all
          necessary order and customer data to populate the invoice.
 PLACE IN: The 'Pharmacist' folder in your project root.
 =======================================================================
*/
using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace MediEase.Pharmacist
{
    public partial class Invoice : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            // Allow both Pharmacist and Admin to view invoices
            if (Session["UserID"] == null || (Session["RoleName"]?.ToString() != "Pharmacist" && Session["RoleName"]?.ToString() != "Admin"))
            {
                Response.Redirect("~/Login.aspx");
                return;
            }

            if (!IsPostBack)
            {
                if (int.TryParse(Request.QueryString["OrderID"], out int orderId))
                {
                    LoadInvoiceData(orderId);
                }
                else
                {
                    ShowErrorView();
                }
            }
        }

        private void LoadInvoiceData(int orderId)
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                // This query gets all primary details for the order and customer
                const string mainQuery = @"
                    SELECT 
                        o.OrderID, o.OrderDate, o.TotalAmount, o.ShippingAddress,
                        up.FirstName, up.LastName, up.PhoneNumber,
                        d.DiscountPercentage
                    FROM Orders o
                    INNER JOIN UserProfiles up ON o.CustomerID = up.UserID
                    LEFT JOIN Discounts d ON o.DiscountID = d.DiscountID
                    WHERE o.OrderID = @OrderID;
                ";

                // Query to get all items for the order
                const string itemsQuery = @"
                    SELECT p.Name, oi.Quantity, oi.PricePerUnit 
                    FROM OrderItems oi 
                    INNER JOIN Products p ON oi.ProductID = p.ProductID 
                    WHERE oi.OrderID = @OrderID;
                ";

                // Query to get redeemed points for the order
                const string pointsQuery = "SELECT ISNULL(SUM(Points), 0) AS RedeemedPoints FROM LoyaltyPoints WHERE OrderID = @OrderID AND TransactionType = 'Redeemed'";

                try
                {
                    con.Open();
                    SqlDataAdapter sda = new SqlDataAdapter(itemsQuery, con);
                    sda.SelectCommand.Parameters.AddWithValue("@OrderID", orderId);
                    DataTable itemsDt = new DataTable();
                    sda.Fill(itemsDt);

                    // If no items, the order is invalid/empty, so show error
                    if (itemsDt.Rows.Count == 0)
                    {
                        ShowErrorView();
                        return;
                    }

                    // Bind the items to the repeater
                    OrderItemsRepeater.DataSource = itemsDt;
                    OrderItemsRepeater.DataBind();

                    // Calculate subtotal from the items
                    decimal subtotal = itemsDt.AsEnumerable().Sum(row => row.Field<int>("Quantity") * row.Field<decimal>("PricePerUnit"));

                    decimal discountAmount = 0;
                    decimal pointsDiscount = 0;

                    // Get main order details
                    using (SqlCommand mainCmd = new SqlCommand(mainQuery, con))
                    {
                        mainCmd.Parameters.AddWithValue("@OrderID", orderId);
                        using (SqlDataReader reader = mainCmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                Page.Title = $"Invoice - Order #{orderId}";
                                OrderIDLabel.Text = reader["OrderID"].ToString();
                                InvoiceNumberLabel.Text = reader["OrderID"].ToString();
                                OrderDateLabel.Text = Convert.ToDateTime(reader["OrderDate"]).ToString("MMMM dd, yyyy");
                                CustomerNameLabel.Text = $"{reader["FirstName"]} {reader["LastName"]}";
                                ShippingAddressLabel.Text = Server.HtmlEncode(reader["ShippingAddress"].ToString()).Replace("\n", "<br/>");
                                CustomerPhoneLabel.Text = reader["PhoneNumber"].ToString();
                                GrandTotalLabel.Text = Convert.ToDecimal(reader["TotalAmount"]).ToString("C");

                                // Calculate discount if a percentage was applied
                                if (reader["DiscountPercentage"] != DBNull.Value)
                                {
                                    decimal percentage = Convert.ToDecimal(reader["DiscountPercentage"]);
                                    discountAmount = subtotal * (percentage / 100);
                                }
                            }
                            else
                            {
                                ShowErrorView();
                                return;
                            }
                        }
                    }

                    // Get redeemed points value
                    using (SqlCommand pointsCmd = new SqlCommand(pointsQuery, con))
                    {
                        pointsCmd.Parameters.AddWithValue("@OrderID", orderId);
                        int redeemedPoints = Convert.ToInt32(pointsCmd.ExecuteScalar());
                        if (redeemedPoints < 0) // Redeemed points are stored as negative
                        {
                            decimal pointsToDollarRatio = SettingsService.GetDecimalSetting("PointsValueInDollars", 100.0m);
                            if (pointsToDollarRatio > 0)
                            {
                                pointsDiscount = Math.Abs(redeemedPoints) / pointsToDollarRatio;
                            }
                        }
                    }

                    // Display totals
                    SubtotalLabel.Text = subtotal.ToString("C");
                    DiscountLabel.Text = (discountAmount + pointsDiscount).ToString("C");

                    InvoiceView.Visible = true;
                    ErrorView.Visible = false;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine("Error loading invoice data: " + ex.Message);
                    ShowErrorView();
                }
            }
        }

        private void ShowErrorView()
        {
            InvoiceView.Visible = false;
            ErrorView.Visible = true;
        }
    }
}