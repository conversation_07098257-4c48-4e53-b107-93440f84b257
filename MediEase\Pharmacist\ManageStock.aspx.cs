﻿using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web.UI;
using System.Text;
using System.Web.UI.WebControls;

public partial class Pharmacist_ManageStock : System.Web.UI.Page
{
    protected void Page_Load(object sender, EventArgs e)
    {
        // --- SECURITY CHECK ---
        if (Session["UserId"] == null || Session["UserRole"]?.ToString() != "Pharmacist")
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        if (!IsPostBack)
        {
            BindStockGrid();
        }
    }

    private void BindStockGrid()
    {
        string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
        using (SqlConnection con = new SqlConnection(connectionString))
        {
            StringBuilder queryBuilder = new StringBuilder("SELECT MedicineId, Name, StockQuantity, ReorderLevel FROM Medicines");
            SqlCommand cmd = new SqlCommand();

            // Filtering logic
            if (!string.IsNullOrWhiteSpace(txtSearch.Text) || !string.IsNullOrEmpty(ddlStockFilter.SelectedValue))
            {
                queryBuilder.Append(" WHERE 1=1 ");
                if (!string.IsNullOrWhiteSpace(txtSearch.Text))
                {
                    queryBuilder.Append(" AND Name LIKE @Search");
                    cmd.Parameters.AddWithValue("@Search", "%" + txtSearch.Text.Trim() + "%");
                }
                if (!string.IsNullOrEmpty(ddlStockFilter.SelectedValue))
                {
                    switch (ddlStockFilter.SelectedValue)
                    {
                        case "InStock": queryBuilder.Append(" AND StockQuantity > ReorderLevel"); break;
                        case "LowStock": queryBuilder.Append(" AND StockQuantity <= ReorderLevel AND StockQuantity > 0"); break;
                        case "OutOfStock": queryBuilder.Append(" AND StockQuantity <= 0"); break;
                    }
                }
            }

            queryBuilder.Append(" ORDER BY Name");

            cmd.CommandText = queryBuilder.ToString();
            cmd.Connection = con;

            using (SqlDataAdapter sda = new SqlDataAdapter(cmd))
            {
                DataTable dt = new DataTable();
                sda.Fill(dt);
                gvStock.DataSource = dt;
                gvStock.DataBind();
            }
        }
    }

    protected void btnSearch_Click(object sender, EventArgs e)
    {
        gvStock.PageIndex = 0;
        BindStockGrid();
    }

    protected void btnClear_Click(object sender, EventArgs e)
    {
        txtSearch.Text = string.Empty;
        ddlStockFilter.SelectedIndex = 0;
        gvStock.PageIndex = 0;
        BindStockGrid();
    }

    protected void gvStock_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        gvStock.PageIndex = e.NewPageIndex;
        BindStockGrid();
    }

    protected void gvStock_RowEditing(object sender, GridViewEditEventArgs e)
    {
        gvStock.EditIndex = e.NewEditIndex;
        BindStockGrid();
    }

    protected void gvStock_RowCancelingEdit(object sender, GridViewCancelEditEventArgs e)
    {
        gvStock.EditIndex = -1;
        BindStockGrid();
    }

    protected void gvStock_RowUpdating(object sender, GridViewUpdateEventArgs e)
    {
        int medicineId = Convert.ToInt32(gvStock.DataKeys[e.RowIndex].Value);
        int previousStock = Convert.ToInt32(gvStock.DataKeys[e.RowIndex].Values["StockQuantity"]);
        TextBox txtNewStock = (TextBox)gvStock.Rows[e.RowIndex].FindControl("txtNewStock");

        if (!int.TryParse(txtNewStock.Text, out int newStock) || newStock < 0)
        {
            ShowMessage("Please enter a valid, non-negative stock quantity.", "error");
            return;
        }

        string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
        using (SqlConnection con = new SqlConnection(connectionString))
        {
            con.Open();
            SqlTransaction transaction = con.BeginTransaction();
            try
            {
                // 1. Update the Medicines table
                string updateQuery = "UPDATE Medicines SET StockQuantity = @NewStock WHERE MedicineId = @MedicineId";
                using (SqlCommand updateCmd = new SqlCommand(updateQuery, con, transaction))
                {
                    updateCmd.Parameters.AddWithValue("@NewStock", newStock);
                    updateCmd.Parameters.AddWithValue("@MedicineId", medicineId);
                    updateCmd.ExecuteNonQuery();
                }

                // 2. Log the change in StockMovements
                int quantityChange = newStock - previousStock;
                if (quantityChange != 0)
                {
                    string movementType = (quantityChange > 0) ? "IN" : "OUT";
                    string movementQuery = @"INSERT INTO StockMovements (MedicineId, MovementType, Quantity, PreviousStock, NewStock, Notes, CreatedBy)
                                             VALUES (@MedicineId, @MovementType, @Quantity, @PreviousStock, @NewStock, @Notes, @CreatedBy)";
                    using (SqlCommand movementCmd = new SqlCommand(movementQuery, con, transaction))
                    {
                        movementCmd.Parameters.AddWithValue("@MedicineId", medicineId);
                        movementCmd.Parameters.AddWithValue("@MovementType", movementType);
                        movementCmd.Parameters.AddWithValue("@Quantity", Math.Abs(quantityChange));
                        movementCmd.Parameters.AddWithValue("@PreviousStock", previousStock);
                        movementCmd.Parameters.AddWithValue("@NewStock", newStock);
                        movementCmd.Parameters.AddWithValue("@Notes", "Manual stock adjustment by pharmacist.");
                        movementCmd.Parameters.AddWithValue("@CreatedBy", (int)Session["UserId"]);
                        movementCmd.ExecuteNonQuery();
                    }
                }

                transaction.Commit();
                ShowMessage("Stock updated successfully.", "success");
            }
            catch (Exception ex)
            {
                transaction.Rollback();
                ShowMessage($"Error updating stock: {ex.Message}", "error");
            }
        }

        gvStock.EditIndex = -1;
        BindStockGrid();
    }

    protected void btnLogout_Click(object sender, EventArgs e)
    {
        Session.Clear();
        Session.Abandon();
        Response.Redirect("~/Login.aspx");
    }

    private void ShowMessage(string message, string type)
    {
        string cssClass = (type == "success")
            ? "bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg relative mb-4"
            : "bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg relative mb-4";

        litMessage.Text = $"<div class='{cssClass}' role='alert'>{message}</div>";
    }
}
