<%@ Page Language="C#" AutoEventWireup="true" CodeFile="ProductManagement.aspx.cs" Inherits="Admin_ProductManagement" %>

<!DOCTYPE html>
<html lang="en">
<head runat="server">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Management - MediEase Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <style>
        body { font-family: 'Inter', sans-serif; }
        .sidebar-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            transition: background-color 0.2s, color 0.2s;
        }
        .sidebar-link:hover, .sidebar-link.active {
            background-color: #0d9488; /* teal-700 */
            color: white;
        }
        .sidebar-link i {
            width: 1.25rem;
            margin-right: 0.75rem;
        }
    </style>
</head>
<body class="bg-gray-100">
    <form id="form1" runat="server">
        <div class="flex h-screen bg-gray-100">
            <!-- Sidebar -->
            <aside class="w-64 bg-teal-600 text-white flex flex-col p-4">
                <div class="flex items-center space-x-2 pb-4 border-b border-teal-500">
                    <i class="fas fa-pills text-3xl"></i>
                    <span class="text-2xl font-bold">MediEase</span>
                </div>
                <nav class="mt-6 flex-grow">
                    <a href="Dashboard.aspx" class="sidebar-link">
                        <i class="fas fa-tachometer-alt fa-fw"></i>
                        <span>Dashboard</span>
                    </a>
                    <a href="UserManagement.aspx" class="sidebar-link">
                        <i class="fas fa-users-cog fa-fw"></i>
                        <span>User Management</span>
                    </a>
                    <a href="ProductManagement.aspx" class="sidebar-link active">
                        <i class="fas fa-capsules fa-fw"></i>
                        <span>Products</span>
                    </a>
                     <a href="Reports.aspx" class="sidebar-link">
                        <i class="fas fa-chart-line fa-fw"></i>
                        <span>Reports</span>
                    </a>
                    <a href="SystemSettings.aspx" class="sidebar-link">
                        <i class="fas fa-cogs fa-fw"></i>
                        <span>System Settings</span>
                    </a>
                     <a href="AuditLog.aspx" class="sidebar-link">
                        <i class="fas fa-clipboard-list fa-fw"></i>
                        <span>Audit Log</span>
                    </a>
                </nav>
                <div class="pt-4 border-t border-teal-500">
                    <asp:LinkButton ID="btnLogout" runat="server" OnClick="btnLogout_Click" CssClass="sidebar-link w-full">
                        <i class="fas fa-sign-out-alt fa-fw"></i>
                        <span>Logout</span>
                    </asp:LinkButton>
                </div>
            </aside>

            <!-- Main Content -->
            <main class="flex-1 flex flex-col">
                <header class="bg-white shadow-sm p-4 flex justify-between items-center">
                    <h1 class="text-2xl font-bold text-gray-800">Product Management</h1>
                    <asp:Button ID="btnAddProduct" runat="server" Text="<i class='fas fa-plus mr-2'></i>Add New Product" OnClick="btnAddProduct_Click" CssClass="px-4 py-2 bg-teal-600 text-white font-semibold rounded-lg shadow-md hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-opacity-75" />
                </header>

                <div class="flex-1 p-6 overflow-y-auto">
                    <!-- Search and Filter Controls -->
                    <div class="bg-white p-4 rounded-lg shadow mb-6">
                        <div class="grid grid-cols-1 md:grid-cols-5 gap-4 items-end">
                            <div class="md:col-span-2">
                                <label class="block text-sm font-medium text-gray-700">Search by Name</label>
                                <asp:TextBox ID="txtSearch" runat="server" CssClass="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teal-500 focus:border-teal-500"></asp:TextBox>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Category</label>
                                <asp:DropDownList ID="ddlCategoryFilter" runat="server" CssClass="mt-1 block w-full px-3 py-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-teal-500 focus:border-teal-500" DataValueField="CategoryId" DataTextField="Name" AppendDataBoundItems="true">
                                     <asp:ListItem Value="" Text="All Categories"></asp:ListItem>
                                </asp:DropDownList>
                            </div>
                             <div>
                                <label class="block text-sm font-medium text-gray-700">Stock Status</label>
                                <asp:DropDownList ID="ddlStockFilter" runat="server" CssClass="mt-1 block w-full px-3 py-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-teal-500 focus:border-teal-500">
                                    <asp:ListItem Value="" Text="All"></asp:ListItem>
                                    <asp:ListItem Value="InStock" Text="In Stock"></asp:ListItem>
                                    <asp:ListItem Value="LowStock" Text="Low Stock"></asp:ListItem>
                                    <asp:ListItem Value="OutOfStock" Text="Out of Stock"></asp:ListItem>
                                </asp:DropDownList>
                            </div>
                            <div class="flex space-x-2">
                                <asp:Button ID="btnSearch" runat="server" Text="Search" OnClick="btnSearch_Click" CssClass="w-full justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500" />
                                <asp:Button ID="btnClear" runat="server" Text="Clear" OnClick="btnClear_Click" CssClass="w-full justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500" />
                            </div>
                        </div>
                    </div>

                    <!-- Products Grid -->
                    <div class="bg-white p-6 rounded-lg shadow overflow-x-auto">
                         <asp:GridView ID="gvProducts" runat="server" AutoGenerateColumns="False"
                             OnRowCommand="gvProducts_RowCommand"
                             DataKeyNames="MedicineId"
                             CssClass="min-w-full divide-y divide-gray-200"
                             HeaderStyle-CssClass="bg-gray-50" PagerStyle-CssClass="bg-white p-4"
                             AllowPaging="True" PageSize="10" OnPageIndexChanging="gvProducts_PageIndexChanging">
                             <Columns>
                                 <asp:BoundField DataField="MedicineId" HeaderText="ID" HeaderStyle-CssClass="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" ItemStyle-CssClass="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900" />
                                 <asp:BoundField DataField="Name" HeaderText="Product Name" HeaderStyle-CssClass="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" ItemStyle-CssClass="px-6 py-4 whitespace-nowrap text-sm text-gray-700" />
                                 <asp:BoundField DataField="CategoryName" HeaderText="Category" HeaderStyle-CssClass="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" ItemStyle-CssClass="px-6 py-4 whitespace-nowrap text-sm text-gray-500" />
                                 <asp:BoundField DataField="Price" HeaderText="Price" DataFormatString="{0:C}" HeaderStyle-CssClass="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" ItemStyle-CssClass="px-6 py-4 whitespace-nowrap text-sm text-gray-500" />
                                 <asp:BoundField DataField="StockQuantity" HeaderText="Stock" HeaderStyle-CssClass="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" ItemStyle-CssClass="px-6 py-4 whitespace-nowrap text-sm text-gray-500" />
                                 
                                 <asp:TemplateField HeaderText="Status" HeaderStyle-CssClass="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" ItemStyle-CssClass="px-6 py-4 whitespace-nowrap">
                                     <ItemTemplate>
                                         <span class='px-2 inline-flex text-xs leading-5 font-semibold rounded-full <%# (bool)Eval("IsActive") ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800" %>'>
                                             <%# (bool)Eval("IsActive") ? "Active" : "Inactive" %>
                                         </span>
                                     </ItemTemplate>
                                 </asp:TemplateField>

                                 <asp:TemplateField HeaderText="Actions" HeaderStyle-CssClass="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider" ItemStyle-CssClass="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                                     <ItemTemplate>
                                         <asp:LinkButton ID="btnEdit" runat="server" CommandName="EditProduct" CommandArgument='<%# Eval("MedicineId") %>' ToolTip="Edit Product" CssClass="text-teal-600 hover:text-teal-900 mr-3"><i class="fas fa-edit"></i></asp:LinkButton>
                                         <asp:LinkButton ID="btnDelete" runat="server" CommandName="DeleteProduct" CommandArgument='<%# Eval("MedicineId") %>' ToolTip="Delete Product" CssClass="text-red-600 hover:text-red-900" OnClientClick='return confirm("Are you sure you want to delete this product? This cannot be undone.");'><i class="fas fa-trash-alt"></i></asp:LinkButton>
                                     </ItemTemplate>
                                 </asp:TemplateField>
                             </Columns>
                             <EmptyDataTemplate>
                                 <div class="text-center py-8">
                                     <p class="mt-2 text-gray-500">No products found matching your criteria.</p>
                                 </div>
                             </EmptyDataTemplate>
                         </asp:GridView>
                    </div>
                </div>
            </main>
        </div>
    </form>
</body>
</html>
