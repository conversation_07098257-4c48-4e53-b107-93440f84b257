   .winmd.dll.exe ?   =C:\Users\<USER>\Desktop\Project\MediEase\Admin\EditProduct.aspx9C:\Users\<USER>\Desktop\Project\MediEase\Admin\Reports.aspx<C:\Users\<USER>\Desktop\Project\MediEase\App_Data\MediEase.mdf@C:\Users\<USER>\Desktop\Project\MediEase\App_Data\MediEase_log.ldfHC:\Users\<USER>\Desktop\Project\MediEase\Customer\CustomerOrderDetail.aspxAC:\Users\<USER>\Desktop\Project\MediEase\Customer\OrderHistory.aspxBC:\Users\<USER>\Desktop\Project\MediEase\Customer\Prescriptions.aspx<C:\Users\<USER>\Desktop\Project\MediEase\Customer\Profile.aspx>C:\Users\<USER>\Desktop\Project\MediEase\Customer\Reminders.aspxCC:\Users\<USER>\Desktop\Project\MediEase\Pharmacist\ManageOrders.aspxBC:\Users\<USER>\Desktop\Project\MediEase\Pharmacist\ManageStock.aspxIC:\Users\<USER>\Desktop\Project\MediEase\Pharmacist\PrescriptionDetail.aspxLC:\Users\<USER>\Desktop\Project\MediEase\Pharmacist\ValidatePrescriptions.aspxOC:\Users\<USER>\Desktop\Project\MediEase\Scripts\WebForms\MSAjax\MicrosoftAjax.jsbC:\Users\<USER>\Desktop\Project\MediEase\Scripts\WebForms\MSAjax\MicrosoftAjaxApplicationServices.js]C:\Users\<USER>\Desktop\Project\MediEase\Scripts\WebForms\MSAjax\MicrosoftAjaxComponentModel.jsSC:\Users\<USER>\Desktop\Project\MediEase\Scripts\WebForms\MSAjax\MicrosoftAjaxCore.js\C:\Users\<USER>\Desktop\Project\MediEase\Scripts\WebForms\MSAjax\MicrosoftAjaxGlobalization.jsVC:\Users\<USER>\Desktop\Project\MediEase\Scripts\WebForms\MSAjax\MicrosoftAjaxHistory.jsVC:\Users\<USER>\Desktop\Project\MediEase\Scripts\WebForms\MSAjax\MicrosoftAjaxNetwork.js\C:\Users\<USER>\Desktop\Project\MediEase\Scripts\WebForms\MSAjax\MicrosoftAjaxSerialization.jsTC:\Users\<USER>\Desktop\Project\MediEase\Scripts\WebForms\MSAjax\MicrosoftAjaxTimer.jsWC:\Users\<USER>\Desktop\Project\MediEase\Scripts\WebForms\MSAjax\MicrosoftAjaxWebForms.jsZC:\Users\<USER>\Desktop\Project\MediEase\Scripts\WebForms\MSAjax\MicrosoftAjaxWebServices.js1C:\Users\<USER>\Desktop\Project\MediEase\Web.config;C:\Users\<USER>\Desktop\Project\MediEase\Admin\Dashboard.aspx>C:\Users\<USER>\Desktop\Project\MediEase\Customer\Dashboard.aspx3C:\Users\<USER>\Desktop\Project\MediEase\Default.aspx1C:\Users\<USER>\Desktop\Project\MediEase\Login.aspx4C:\Users\<USER>\Desktop\Project\MediEase\Register.aspx@C:\Users\<USER>\Desktop\Project\MediEase\Pharmacist\Dashboard.aspx@C:\Users\<USER>\Desktop\Project\MediEase\Admin\UserManagement.aspxCC:\Users\<USER>\Desktop\Project\MediEase\Admin\ProductManagement.aspx@C:\Users\<USER>\Desktop\Project\MediEase\Admin\SystemSettings.aspx2C:\Users\<USER>\Desktop\Project\MediEase\Global.asax6C:\Users\<USER>\Desktop\Project\MediEase\Scripts\Site.js6C:\Users\<USER>\Desktop\Project\MediEase\packages.config7C:\Users\<USER>\Desktop\Project\MediEase\Web.Debug.config9C:\Users\<USER>\Desktop\Project\MediEase\Web.Release.configZC:\Users\<USER>\Desktop\Project\packages\BCrypt.Net-Next.4.0.3\lib\net48\BCrypt.Net-Next.dllZC:\Users\<USER>\Desktop\Project\packages\EntityFramework.6.4.4\lib\net45\EntityFramework.dlldC:\Users\<USER>\Desktop\Project\packages\EntityFramework.6.4.4\lib\net45\EntityFramework.SqlServer.dllgC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.CSharp.dllC:\Users\<USER>\Desktop\Project\packages\Microsoft.AspNet.ScriptManager.MSAjax.5.0.0\lib\net45\Microsoft.ScriptManager.MSAjax.dllvC:\Users\<USER>\Desktop\Project\packages\Microsoft.Web.Infrastructure.*******\lib\net40\Microsoft.Web.Infrastructure.dll_C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\mscorlib.dll[C:\Users\<USER>\Desktop\Project\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll|C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.ComponentModel.DataAnnotations.dllkC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Configuration.dllbC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Core.dlltC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Data.DataSetExtensions.dllbC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Data.dll]C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.dlleC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Drawing.dllpC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.EnterpriseServices.dlluC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.ApplicationServices.dllaC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.dllmC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.DynamicData.dllhC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.Entity.dlllC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.Extensions.dlljC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.Services.dllaC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Xml.dllfC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Xml.Linq.dll       SC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\   Full                 {CandidateAssemblyFiles}{HintPathFromItem}{TargetFrameworkDirectory}B{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}
{RawFileName}+C:\Users\<USER>\Desktop\Project\MediEase\bin\     B{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}ZC:\Users\<USER>\Desktop\Project\MediEase\obj\Debug\DesignTimeResolveAssemblyReferences.cache   SC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\[C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\.NETFramework,Version=v4.8.NET Framework 4.8v4.8msil
v4.0.30319         