﻿<%--
 =======================================================================
 FILE: FeedbackDetails.aspx
 PURPOSE: Displays a single customer feedback/complaint and provides a
          form for the pharmacist to submit a response.
 PLACE IN: The 'Pharmacist' folder in your project root.
 =======================================================================
--%>
<%@ Page Title="Respond to Feedback" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="FeedbackDetails.aspx.cs" Inherits="MediEase.Pharmacist.FeedbackDetails" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-5">
        <asp:PlaceHolder ID="FeedbackView" runat="server">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2>Respond to Feedback</h2>
                            <p class="mb-0">Feedback ID #<asp:Label ID="FeedbackIDLabel" runat="server"></asp:Label></p>
                        </div>
                        <a href="ManageFeedback.aspx" class="btn btn-outline-secondary">&larr; Back to All Feedback</a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <p class="mb-1"><strong>From:</strong> <asp:Label ID="CustomerNameLabel" runat="server"></asp:Label></p>
                        <p class="mb-1"><strong>Date Sent:</strong> <asp:Label ID="SubmissionDateLabel" runat="server"></asp:Label></p>
                        <p class="mb-1"><strong>Subject:</strong> <asp:Label ID="SubjectLabel" runat="server"></asp:Label></p>
                    </div>
                    <div class="alert alert-secondary">
                        <strong>Customer's Message:</strong>
                        <p class="mt-2" style="white-space: pre-wrap;"><asp:Literal ID="MessageLiteral" runat="server"></asp:Literal></p>
                    </div>

                    <hr />
                    
                    <asp:PlaceHolder ID="RespondedView" runat="server" Visible="false">
                        <h4>Your Response</h4>
                         <div class="alert alert-success">
                            <p style="white-space: pre-wrap;"><asp:Literal ID="ResponseTextLiteral" runat="server"></asp:Literal></p>
                        </div>
                         <p class="text-muted">You responded to this on <asp:Label ID="ResponseDateLabel" runat="server"></asp:Label>.</p>
                    </asp:PlaceHolder>

                    <asp:PlaceHolder ID="ResponseFormView" runat="server">
                         <h4>Your Response</h4>
                        <div class="form-group">
                            <asp:TextBox ID="ResponseBox" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="7" placeholder="Write your response here..."></asp:TextBox>
                            <asp:RequiredFieldValidator runat="server" ControlToValidate="ResponseBox" ErrorMessage="A response is required." CssClass="text-danger" Display="Dynamic" />
                        </div>
                        <asp:Button ID="SubmitResponseButton" runat="server" Text="Save & Mark as Resolved" CssClass="btn btn-primary" OnClick="SubmitResponseButton_Click" />
                    </asp:PlaceHolder>
                </div>
            </div>
        </asp:PlaceHolder>

        <asp:PlaceHolder ID="ErrorView" runat="server" Visible="false">
             <div class="alert alert-danger text-center">
                <h2>Feedback Not Found</h2>
                <p>The feedback you are looking for does not exist.</p>
                <a href="ManageFeedback.aspx" class="btn btn-primary">Return to Feedback List</a>
            </div>
        </asp:PlaceHolder>
    </div>
</asp:Content>