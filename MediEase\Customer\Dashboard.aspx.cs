using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web.UI;

public partial class Customer_Dashboard : System.Web.UI.Page
{
    protected void Page_Load(object sender, EventArgs e)
    {
        // --- SECURITY CHECK ---
        // If the user is not logged in or is not a customer, redirect to the login page.
        if (Session["UserId"] == null || Session["UserRole"]?.ToString() != "Customer")
        {
            Response.Redirect("~/Login.aspx");
            return; // Stop further execution of the page
        }

        if (!IsPostBack)
        {
            LoadDashboardData();
        }
    }

    /// <summary>
    /// Loads all data required for the customer dashboard.
    /// </summary>
    private void LoadDashboardData()
    {
        int userId = (int)Session["UserId"];
        string firstName = Session["UserFirstName"]?.ToString() ?? "Customer";

        // Set welcome message and user initial
        litWelcomeMessage.Text = $"Welcome, {firstName}!";
        litUserInitial.Text = $"<div class='w-10 h-10 rounded-full bg-teal-500 text-white flex items-center justify-center font-bold text-lg'>{firstName[0]}</div>";

        string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
        using (SqlConnection con = new SqlConnection(connectionString))
        {
            try
            {
                con.Open();
                BindDashboardStats(userId, con);
                BindRecentOrders(userId, con);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Dashboard Load Error: {ex.Message}");
                // Handle error, maybe show an error message on the page
                litWelcomeMessage.Text = "Error loading dashboard data.";
            }
        }
    }

    /// <summary>
    /// Fetches and binds statistics like total orders and loyalty points.
    /// </summary>
    private void BindDashboardStats(int userId, SqlConnection con)
    {
        // Using a single query to fetch multiple stats for efficiency
        string query = @"
            SELECT 
                (SELECT ISNULL(LoyaltyPoints, 0) FROM Users WHERE UserId = @UserId) AS LoyaltyPoints,
                (SELECT COUNT(OrderId) FROM Orders WHERE CustomerId = @UserId) AS TotalOrders,
                (SELECT COUNT(ReminderId) FROM HealthReminders WHERE UserId = @UserId AND IsActive = 1) AS ActiveReminders;";

        using (SqlCommand cmd = new SqlCommand(query, con))
        {
            cmd.Parameters.AddWithValue("@UserId", userId);
            using (SqlDataReader reader = cmd.ExecuteReader())
            {
                if (reader.Read())
                {
                    litLoyaltyPoints.Text = reader["LoyaltyPoints"].ToString();
                    litTotalOrders.Text = reader["TotalOrders"].ToString();
                    litActiveReminders.Text = reader["ActiveReminders"].ToString();
                }
            }
        }
    }

    /// <summary>
    /// Fetches the top 5 recent orders for the customer and binds them to the GridView.
    /// </summary>
    private void BindRecentOrders(int userId, SqlConnection con)
    {
        string query = "SELECT TOP 5 OrderId, OrderNumber, OrderDate, TotalAmount, Status FROM Orders WHERE CustomerId = @UserId ORDER BY OrderDate DESC";
        using (SqlCommand cmd = new SqlCommand(query, con))
        {
            cmd.Parameters.AddWithValue("@UserId", userId);
            using (SqlDataAdapter sda = new SqlDataAdapter(cmd))
            {
                DataTable dt = new DataTable();
                sda.Fill(dt);
                gvRecentOrders.DataSource = dt;
                gvRecentOrders.DataBind();
            }
        }
    }

    /// <summary>
    /// Handles the click event for the logout button.
    /// </summary>
    protected void btnLogout_Click(object sender, EventArgs e)
    {
        Session.Clear();
        Session.Abandon();
        Response.Redirect("~/Login.aspx");
    }

    /// <summary>
    /// Helper function to return a Tailwind CSS class based on the order status for styling.
    /// </summary>
    protected string GetStatusClass(string status)
    {
        switch (status.ToLower())
        {
            case "pending":
                return "bg-yellow-100 text-yellow-800";
            case "shipped":
            case "processing":
                return "bg-blue-100 text-blue-800";
            case "delivered":
                return "bg-green-100 text-green-800";
            case "cancelled":
                return "bg-red-100 text-red-800";
            default:
                return "bg-gray-100 text-gray-800";
        }
    }
}
