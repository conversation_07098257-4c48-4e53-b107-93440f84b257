﻿/* FILE: Admin/AuditTrail.aspx.cs */
using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace MediEase.Admin
{
    public partial class AuditTrail : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (Session["UserID"] == null || Session["RoleName"]?.ToString() != "Admin") { Response.Redirect("~/Login.aspx"); return; }
            if (!IsPostBack) { BindAuditTrailGrid(); }
        }

        private void BindAuditTrailGrid()
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "SELECT at.AuditID, at.Timestamp, ISNULL(up.FirstName + ' ' + up.LastName, 'System') AS UserName, at.ActionType, at.TableName, at.RecordID, at.OldValues, at.NewValues FROM AuditTrail at LEFT JOIN UserProfiles up ON at.UserID = up.UserID ORDER BY at.Timestamp DESC;";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    try
                    {
                        con.Open();
                        SqlDataAdapter sda = new SqlDataAdapter(cmd);
                        DataTable dt = new DataTable();
                        sda.Fill(dt);
                        AuditTrailGrid.DataSource = dt;
                        AuditTrailGrid.DataBind();
                    }
                    catch (Exception ex) { System.Diagnostics.Debug.WriteLine("Error binding audit trail: " + ex.Message); }
                }
            }
        }

        protected void AuditTrailGrid_PageIndexChanging(object sender, GridViewPageEventArgs e)
        {
            AuditTrailGrid.PageIndex = e.NewPageIndex;
            BindAuditTrailGrid();
        }

        public string FormatDetails(object tableName, object recordId, object oldValues, object newValues)
        {
            string details = $"Table: {tableName}, Record ID: {recordId}";
            if (oldValues != DBNull.Value && !string.IsNullOrEmpty(oldValues.ToString()))
            {
                details += $"<br/>Old: {Server.HtmlEncode(oldValues.ToString())}";
            }
            if (newValues != DBNull.Value && !string.IsNullOrEmpty(newValues.ToString()))
            {
                details += $"<br/>New: {Server.HtmlEncode(newValues.ToString())}";
            }
            return details;
        }
    }
}