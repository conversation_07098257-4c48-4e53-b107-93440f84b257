﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="Prescriptions.aspx.cs" Inherits="Customer_Prescriptions" %>

<!DOCTYPE html>
<html lang="en">
<head runat="server">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Prescriptions - MediEase</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <style>
        body { font-family: 'Inter', sans-serif; }
        .sidebar-link { display: flex; align-items: center; padding: 0.75rem 1rem; border-radius: 0.5rem; transition: background-color 0.2s, color 0.2s; }
        .sidebar-link:hover, .sidebar-link.active { background-color: #0d9488; color: white; }
        .sidebar-link i { width: 1.25rem; margin-right: 0.75rem; }
        .validation-error { color: #D32F2F; font-size: 0.875rem; margin-top: 0.25rem; }
    </style>
</head>
<body class="bg-gray-100">
    <form id="form1" runat="server">
        <div class="flex h-screen bg-gray-100">
            <!-- Sidebar -->
            <aside class="w-64 bg-teal-600 text-white flex-col p-4 hidden md:flex">
                <div class="flex items-center space-x-2 pb-4 border-b border-teal-500">
                    <i class="fas fa-pills text-3xl"></i><span class="text-2xl font-bold">MediEase</span>
                </div>
                <nav class="mt-6 flex-grow">
                    <a href="Dashboard.aspx" class="sidebar-link"><i class="fas fa-tachometer-alt fa-fw"></i><span>Dashboard</span></a>
                    <a href="Profile.aspx" class="sidebar-link"><i class="fas fa-user-edit fa-fw"></i><span>My Profile</span></a>
                    <a href="OrderHistory.aspx" class="sidebar-link"><i class="fas fa-history fa-fw"></i><span>Order History</span></a>
                    <a href="Prescriptions.aspx" class="sidebar-link active"><i class="fas fa-file-prescription fa-fw"></i><span>Prescriptions</span></a>
                    <a href="Reminders.aspx" class="sidebar-link"><i class="fas fa-bell fa-fw"></i><span>Reminders</span></a>
                </nav>
                <div class="pt-4 border-t border-teal-500">
                    <asp:LinkButton ID="btnLogout" runat="server" OnClick="btnLogout_Click" CssClass="sidebar-link w-full"><i class="fas fa-sign-out-alt fa-fw"></i><span>Logout</span></asp:LinkButton>
                </div>
            </aside>

            <!-- Main Content -->
            <main class="flex-1 flex flex-col">
                <header class="bg-white shadow-sm p-4">
                    <h1 class="text-2xl font-bold text-gray-800">My Prescriptions</h1>
                </header>

                <div class="flex-1 p-6 overflow-y-auto">
                    <!-- Server Message -->
                    <asp:Literal ID="litMessage" runat="server"></asp:Literal>

                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <!-- Left Column: Upload Form -->
                        <div class="lg:col-span-1 bg-white p-8 rounded-lg shadow h-fit">
                            <h2 class="text-xl font-bold text-gray-800 mb-6">Upload New Prescription</h2>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Doctor's Name *</label>
                                    <asp:TextBox ID="txtDoctorName" runat="server" CssClass="mt-1 block w-full input-style"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvDoctorName" runat="server" ControlToValidate="txtDoctorName" ErrorMessage="Doctor's name is required." CssClass="validation-error" Display="Dynamic"></asp:RequiredFieldValidator>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Prescription Date *</label>
                                    <asp:TextBox ID="txtPrescriptionDate" runat="server" TextMode="Date" CssClass="mt-1 block w-full input-style"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvPrescriptionDate" runat="server" ControlToValidate="txtPrescriptionDate" ErrorMessage="Prescription date is required." CssClass="validation-error" Display="Dynamic"></asp:RequiredFieldValidator>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Prescription File (JPG, PNG, PDF) *</label>
                                    <asp:FileUpload ID="fileUploadPrescription" runat="server" CssClass="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-teal-50 file:text-teal-700 hover:file:bg-teal-100"/>
                                    <asp:RequiredFieldValidator ID="rfvFileUpload" runat="server" ControlToValidate="fileUploadPrescription" ErrorMessage="A prescription file is required." CssClass="validation-error" Display="Dynamic"></asp:RequiredFieldValidator>
                                </div>
                                <div class="pt-2">
                                    <asp:Button ID="btnUpload" runat="server" Text="Upload Prescription" OnClick="btnUpload_Click" CssClass="w-full justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-teal-600 hover:bg-teal-700"/>
                                </div>
                            </div>
                        </div>

                        <!-- Right Column: Prescription History -->
                        <div class="lg:col-span-2 bg-white p-6 rounded-lg shadow">
                            <h2 class="text-xl font-bold text-gray-800 mb-4">Prescription History</h2>
                            <asp:GridView ID="gvPrescriptions" runat="server" AutoGenerateColumns="False"
                                DataKeyNames="PrescriptionId"
                                CssClass="min-w-full divide-y divide-gray-200"
                                HeaderStyle-CssClass="bg-gray-50" PagerStyle-CssClass="bg-white p-4"
                                AllowPaging="True" PageSize="5" OnPageIndexChanging="gvPrescriptions_PageIndexChanging">
                                <Columns>
                                    <asp:BoundField DataField="PrescriptionNumber" HeaderText="Number" HeaderStyle-CssClass="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase" ItemStyle-CssClass="px-6 py-4 whitespace-nowrap text-sm text-gray-900" />
                                    <asp:BoundField DataField="DoctorName" HeaderText="Doctor" HeaderStyle-CssClass="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase" ItemStyle-CssClass="px-6 py-4 whitespace-nowrap text-sm text-gray-500" />
                                    <asp:BoundField DataField="PrescriptionDate" HeaderText="Date" DataFormatString="{0:MMM dd, yyyy}" HeaderStyle-CssClass="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase" ItemStyle-CssClass="px-6 py-4 whitespace-nowrap text-sm text-gray-500" />
                                    <asp:TemplateField HeaderText="Status" HeaderStyle-CssClass="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase" ItemStyle-CssClass="px-6 py-4 whitespace-nowrap">
                                        <ItemTemplate>
                                            <span class='px-2 inline-flex text-xs leading-5 font-semibold rounded-full <%# GetStatusClass(Eval("Status").ToString()) %>'>
                                                <%# Eval("Status") %>
                                            </span>
                                        </ItemTemplate>
                                    </asp:TemplateField>
                                    <asp:TemplateField HeaderText="File" HeaderStyle-CssClass="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase" ItemStyle-CssClass="px-6 py-4 whitespace-nowrap text-center">
                                        <ItemTemplate>
                                            <a href='<%# ResolveUrl("~/" + Eval("FilePath")) %>' target="_blank" class="text-teal-600 hover:text-teal-900"><i class="fas fa-eye"></i> View</a>
                                        </ItemTemplate>
                                    </asp:TemplateField>
                                </Columns>
                                <EmptyDataTemplate>
                                    <div class="text-center py-8"><p class="text-gray-500">You have not uploaded any prescriptions.</p></div>
                                </EmptyDataTemplate>
                            </asp:GridView>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </form>
</body>
</html>
