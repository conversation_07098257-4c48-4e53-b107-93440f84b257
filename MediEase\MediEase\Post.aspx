﻿<%--
 =======================================================================
 FILE: Post.aspx
 PURPOSE: Displays the full content of a single blog post.
 =======================================================================
--%>
<%@ Page Title="Blog Post" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="Post.aspx.cs" Inherits="MediEase.Post" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-5">
        <div class="row">
            <div class="col-lg-8 mx-auto">

                <asp:PlaceHolder ID="PostView" runat="server">
                    <asp:Image ID="PostImage" runat="server" CssClass="img-fluid rounded mb-4" />

                    <h1 class="mb-3"><asp:Literal ID="PostTitle" runat="server"></asp:Literal></h1>

                    <p class="text-muted">
                        Posted on <asp:Literal ID="PostDate" runat="server"></asp:Literal> 
                        by <asp:Literal ID="PostAuthor" runat="server"></asp:Literal>
                    </p>

                    <hr />

                    <div class="post-content">
                        <asp:Literal ID="PostContent" runat="server"></asp:Literal>
                    </div>

                    <hr />
                    <a href="Blog.aspx" class="btn btn-outline-primary">&larr; Back to Blog</a>

                </asp:PlaceHolder>

                <asp:PlaceHolder ID="ErrorView" runat="server" Visible="false">
                     <div class="alert alert-danger text-center">
                        <h2>Post Not Found</h2>
                        <p>The article you are looking for does not exist or has not been published yet.</p>
                        <a href="Blog.aspx" class="btn btn-primary">Return to Blog</a>
                    </div>
                </asp:PlaceHolder>

            </div>
        </div>
    </div>
</asp:Content>