﻿/*
 =======================================================================
 FILE: Default.aspx.cs (Final Version with Images)
 =======================================================================
*/
using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace MediEase
{
    public partial class Default : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                BindFeaturedProducts();
            }
        }

        private void BindFeaturedProducts()
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = @"
                    SELECT TOP 8 
                        p.ProductID, p.Name, p.ImageUrl, b.BrandName, pr.Price 
                    FROM Products p
                    LEFT JOIN Brands b ON p.BrandID = b.BrandID
                    INNER JOIN (
                        SELECT ProductID, Price, ROW_NUMBER() OVER(PARTITION BY ProductID ORDER BY EffectiveDate DESC) as rn
                        FROM Pricing
                    ) pr ON p.ProductID = pr.ProductID AND pr.rn = 1
                    WHERE p.IsActive = 1 AND p.IsApproved = 1
                    ORDER BY p.ProductID DESC;
                ";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    try
                    {
                        con.Open();
                        SqlDataAdapter sda = new SqlDataAdapter(cmd);
                        DataTable dt = new DataTable();
                        sda.Fill(dt);

                        if (dt.Rows.Count > 0)
                        {
                            FeaturedProductsRepeater.DataSource = dt;
                            FeaturedProductsRepeater.DataBind();
                        }
                        else
                        {
                            NoProductsMessage.Visible = true;
                        }
                    }
                    catch (Exception ex)
                    {
                        NoProductsMessage.Visible = true;
                        System.Diagnostics.Debug.WriteLine("Error fetching featured products: " + ex.Message);
                    }
                }
            }
        }

        protected void FeaturedProductsRepeater_ItemDataBound(object sender, RepeaterItemEventArgs e)
        {
            if (e.Item.ItemType == ListItemType.Item || e.Item.ItemType == ListItemType.AlternatingItem)
            {
                Image productImage = (Image)e.Item.FindControl("ProductImage");
                if (productImage != null)
                {
                    string imageUrl = DataBinder.Eval(e.Item.DataItem, "ImageUrl") as string;
                    if (string.IsNullOrEmpty(imageUrl))
                    {
                        productImage.ImageUrl = "~/Images/Products/default_product.png";
                    }
                    else
                    {
                        productImage.ImageUrl = ResolveUrl(imageUrl);
                    }
                }
            }
        }
    }
}