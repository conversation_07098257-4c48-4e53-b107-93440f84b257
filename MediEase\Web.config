﻿<?xml version="1.0" encoding="utf-8"?>

<!--
  For more information on how to configure your ASP.NET application, please visit
  https://go.microsoft.com/fwlink/?LinkId=169433
-->
<configuration>

	<!-- Connection String for your MediEase Database -->
	<!-- The C# code uses the name 'MediEaseDB' to find this string. -->
	<!-- This assumes your MediEase.mdf file is in the App_Data folder. -->
	<connectionStrings>
		<add name="MediEaseDB"
			 connectionString="Data Source=(LocalDB)\MSSQLLocalDB;AttachDbFilename=|DataDirectory|\MediEase.mdf;Integrated Security=True"
			 providerName="System.Data.SqlClient"/>
	</connectionStrings>

	<system.web>
		<!--
      Compilation debug="true" enables debugging symbols, which is useful during development.
      Set debug="false" before deploying to a production environment for better performance.
    -->
		<compilation debug="true" targetFramework="4.7.2"/>

		<!--
      httpRuntime targetFramework specifies the .NET framework version compatibility.
      It should match the targetFramework in the compilation tag.
    -->
		<httpRuntime targetFramework="4.7.2"/>

		<!-- 
      Session state configuration. 'InProc' (In-Process) is the default.
      The timeout is set to 30 minutes, meaning user sessions will expire after 30 minutes of inactivity.
    -->
		<sessionState mode="InProc" timeout="30" />

		<!--
      Pages configuration can be used to set properties for all pages in the application.
    -->
		<pages>
			<namespaces>
				<add namespace="System.Web.Optimization" />
			</namespaces>
			<controls>
				<add assembly="Microsoft.AspNet.Web.Optimization.WebForms" namespace="Microsoft.AspNet.Web.Optimization.WebForms" tagPrefix="webopt" />
			</controls>
		</pages>
	</system.web>

	<runtime>
		<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
			<dependentAssembly>
				<assemblyIdentity name="Antlr3.Runtime" publicKeyToken="eb42632606e9261f"/>
				<bindingRedirect oldVersion="0.0.0.0-3.5.0.2" newVersion="3.5.0.2"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed"/>
				<bindingRedirect oldVersion="0.0.0.0-12.0.0.0" newVersion="12.0.0.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35"/>
				<bindingRedirect oldVersion="0.0.0.0-1.6.5135.21930" newVersion="1.6.5135.21930"/>
			</dependentAssembly>
		</assemblyBinding>
	</runtime>

	<!-- Required for .NET Framework 4.5+ to avoid validation errors with some server controls. -->
	<appSettings>
		<add key="ValidationSettings:UnobtrusiveValidationMode" value="None" />
	</appSettings>

</configuration>
