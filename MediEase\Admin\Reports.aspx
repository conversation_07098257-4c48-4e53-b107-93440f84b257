﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="Reports.aspx.cs" Inherits="Admin_Reports" %>

<!DOCTYPE html>
<html lang="en">
<head runat="server">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reports - MediEase Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <style>
        body { font-family: 'Inter', sans-serif; }
        .sidebar-link { display: flex; align-items: center; padding: 0.75rem 1rem; border-radius: 0.5rem; transition: background-color 0.2s, color 0.2s; }
        .sidebar-link:hover, .sidebar-link.active { background-color: #0d9488; color: white; }
        .sidebar-link i { width: 1.25rem; margin-right: 0.75rem; }
    </style>
</head>
<body class="bg-gray-100">
    <form id="form1" runat="server">
        <div class="flex h-screen bg-gray-100">
            <!-- Sidebar -->
            <aside class="w-64 bg-teal-600 text-white flex flex-col p-4">
                <div class="flex items-center space-x-2 pb-4 border-b border-teal-500">
                    <i class="fas fa-pills text-3xl"></i>
                    <span class="text-2xl font-bold">MediEase</span>
                </div>
                <nav class="mt-6 flex-grow">
                    <a href="Dashboard.aspx" class="sidebar-link"><i class="fas fa-tachometer-alt fa-fw"></i><span>Dashboard</span></a>
                    <a href="UserManagement.aspx" class="sidebar-link"><i class="fas fa-users-cog fa-fw"></i><span>User Management</span></a>
                    <a href="ProductManagement.aspx" class="sidebar-link"><i class="fas fa-capsules fa-fw"></i><span>Products</span></a>
                    <a href="Reports.aspx" class="sidebar-link active"><i class="fas fa-chart-line fa-fw"></i><span>Reports</span></a>
                    <a href="SystemSettings.aspx" class="sidebar-link"><i class="fas fa-cogs fa-fw"></i><span>System Settings</span></a>
                    <a href="AuditLog.aspx" class="sidebar-link"><i class="fas fa-clipboard-list fa-fw"></i><span>Audit Log</span></a>
                </nav>
                <div class="pt-4 border-t border-teal-500">
                    <asp:LinkButton ID="btnLogout" runat="server" OnClick="btnLogout_Click" CssClass="sidebar-link w-full"><i class="fas fa-sign-out-alt fa-fw"></i><span>Logout</span></asp:LinkButton>
                </div>
            </aside>

            <!-- Main Content -->
            <main class="flex-1 flex flex-col">
                <header class="bg-white shadow-sm p-4">
                    <h1 class="text-2xl font-bold text-gray-800">Sales & Stock Reports</h1>
                </header>

                <div class="flex-1 p-6 overflow-y-auto">
                    <!-- Date Filter -->
                    <div class="bg-white p-4 rounded-lg shadow mb-6">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Start Date</label>
                                <asp:TextBox ID="txtStartDate" runat="server" TextMode="Date" CssClass="mt-1 block w-full input-style"></asp:TextBox>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">End Date</label>
                                <asp:TextBox ID="txtEndDate" runat="server" TextMode="Date" CssClass="mt-1 block w-full input-style"></asp:TextBox>
                            </div>
                            <div>
                                <asp:Button ID="btnGenerateReport" runat="server" Text="Generate Report" OnClick="btnGenerateReport_Click" CssClass="w-full justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500" />
                            </div>
                        </div>
                    </div>

                    <!-- Sales Summary -->
                    <div class="bg-white p-6 rounded-lg shadow mb-6">
                        <h2 class="text-xl font-bold text-gray-800 mb-4">Sales Summary</h2>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div class="bg-gray-50 p-4 rounded-lg text-center">
                                <p class="text-gray-500 text-sm">Total Revenue</p>
                                <asp:Literal ID="litTotalRevenue" runat="server" Text="$0.00" CssClass="text-2xl font-bold"></asp:Literal>
                            </div>
                            <div class="bg-gray-50 p-4 rounded-lg text-center">
                                <p class="text-gray-500 text-sm">Total Orders</p>
                                <asp:Literal ID="litTotalOrders" runat="server" Text="0" CssClass="text-2xl font-bold"></asp:Literal>
                            </div>
                            <div class="bg-gray-50 p-4 rounded-lg text-center">
                                <p class="text-gray-500 text-sm">Average Order Value</p>
                                <asp:Literal ID="litAvgOrderValue" runat="server" Text="$0.00" CssClass="text-2xl font-bold"></asp:Literal>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Report Grids -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Best-Selling Products -->
                        <div class="bg-white p-6 rounded-lg shadow">
                            <h2 class="text-xl font-bold text-gray-800 mb-4">Best-Selling Products</h2>
                            <asp:GridView ID="gvBestSellers" runat="server" AutoGenerateColumns="False" CssClass="w-full text-sm text-left text-gray-500" HeaderStyle-CssClass="text-xs text-gray-700 uppercase bg-gray-50">
                                <Columns>
                                    <asp:BoundField DataField="Name" HeaderText="Product" />
                                    <asp:BoundField DataField="TotalQuantity" HeaderText="Units Sold" ItemStyle-CssClass="text-center" HeaderStyle-CssClass="text-center" />
                                    <asp:BoundField DataField="TotalSales" HeaderText="Total Sales" DataFormatString="{0:C}" ItemStyle-CssClass="text-right" HeaderStyle-CssClass="text-right" />
                                </Columns>
                            </asp:GridView>
                        </div>

                        <!-- Low Stock Report -->
                        <div class="bg-white p-6 rounded-lg shadow">
                            <h2 class="text-xl font-bold text-gray-800 mb-4">Stock Level Report</h2>
                             <asp:GridView ID="gvLowStock" runat="server" AutoGenerateColumns="False" CssClass="w-full text-sm text-left text-gray-500" HeaderStyle-CssClass="text-xs text-gray-700 uppercase bg-gray-50">
                                <Columns>
                                    <asp:BoundField DataField="Name" HeaderText="Product" />
                                    <asp:BoundField DataField="StockQuantity" HeaderText="Current Stock" ItemStyle-CssClass="text-center" HeaderStyle-CssClass="text-center" />
                                    <asp:BoundField DataField="ReorderLevel" HeaderText="Reorder Level" ItemStyle-CssClass="text-center" HeaderStyle-CssClass="text-center" />
                                </Columns>
                            </asp:GridView>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </form>
</body>
</html>
