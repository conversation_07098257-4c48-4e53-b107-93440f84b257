using System;
using System.Web.UI;
using System.Data.SqlClient;
using System.Configuration;

public partial class _Default : System.Web.UI.Page
{
    protected void Page_Load(object sender, EventArgs e)
    {
        // We only want to bind the data on the initial page load,
        // not on subsequent postbacks (e.g., button clicks).
        if (!IsPostBack)
        {
            BindFeaturedProducts();
        }
    }

    /// <summary>
    /// Fetches featured products from the database and binds them to the repeater control.
    /// </summary>
    private void BindFeaturedProducts()
    {
        // Get the connection string from the Web.config file.
        // Make sure you have a connection string named "MediEaseDB".
        // Example for Web.config:
        // <connectionStrings>
        //   <add name="MediEaseDB" connectionString="Data Source=(LocalDB)\MSSQLLocalDB;AttachDbFilename=|DataDirectory|\MediEase.mdf;Integrated Security=True" providerName="System.Data.SqlClient"/>
        // </connectionStrings>
        string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"]?.ConnectionString;

        if (string.IsNullOrEmpty(connectionString))
        {
            // Handle the case where the connection string is missing
            System.Diagnostics.Debug.WriteLine("Connection string 'MediEaseDB' not found in Web.config.");
            return;
        }

        // SQL query to get top 5 featured products, ordered by the number of purchases.
        string query = "SELECT TOP 5 Name, Price, ImageUrl, PackSize FROM Medicines WHERE IsFeatured = 1 AND IsActive = 1 ORDER BY PurchaseCount DESC";

        // Using "using" statements ensures that resources are properly disposed of.
        using (SqlConnection con = new SqlConnection(connectionString))
        {
            using (SqlCommand cmd = new SqlCommand(query, con))
            {
                try
                {
                    con.Open();
                    // Use a SqlDataReader for efficient, forward-only reading of data.
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        rptFeaturedProducts.DataSource = reader;
                        rptFeaturedProducts.DataBind(); // Binds the data to the repeater.
                    }
                }
                catch (Exception ex)
                {
                    // Log the exception to help with debugging.
                    System.Diagnostics.Debug.WriteLine("Error fetching featured products: " + ex.Message);
                    // Optionally, display a friendly error message to the user on the page.
                }
            }
        }
    }
}
