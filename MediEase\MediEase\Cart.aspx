﻿<%--
 =======================================================================
 FILE: Cart.aspx (Final Version)
 =======================================================================
--%>
<%@ Page Title="Shopping Cart" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="Cart.aspx.cs" Inherits="MediEase.Cart" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-5">
        <h1 class="mb-4">Your Shopping Cart</h1>
        <asp:PlaceHolder ID="CartView" runat="server">
            <div class="row">
                <div class="col-lg-8">
                    <asp:Repeater ID="CartRepeater" runat="server" OnItemCommand="CartRepeater_ItemCommand">
                        <HeaderTemplate>
                            <div class="card shadow-sm">
                                <div class="card-header bg-light d-none d-md-flex"><div class="row w-100"><div class="col-md-5"><strong>Product</strong></div><div class="col-md-2 text-center"><strong>Price</strong></div><div class="col-md-3 text-center"><strong>Quantity</strong></div><div class="col-md-2 text-right"><strong>Total</strong></div></div></div>
                                <ul class="list-group list-group-flush">
                        </HeaderTemplate>
                        <ItemTemplate>
                            <li class="list-group-item">
                                <div class="row align-items-center">
                                    <div class="col-12 col-md-5 mb-2 mb-md-0">
                                        <div class="media"><img src="<%# Eval("ImageUrl") %>" alt="<%# Eval("ProductName") %>" class="mr-3 rounded" width="60"><div class="media-body"><h5 class="mt-0"><%# Eval("ProductName") %></h5></div></div>
                                    </div>
                                    <div class="col-4 col-md-2 text-md-center"><span class="d-md-none">Price: </span>$<%# Eval("Price", "{0:N2}") %></div>
                                    <div class="col-8 col-md-3 text-md-center">
                                        <div class="input-group justify-content-center">
                                             <asp:TextBox ID="QuantityBox" runat="server" CssClass="form-control text-center" Text='<%# Eval("Quantity") %>' style="max-width: 70px;"></asp:TextBox>
                                             <div class="input-group-append">
                                                 <asp:Button ID="UpdateButton" runat="server" Text="Update" CommandName="Update" CommandArgument='<%# Eval("ProductID") %>' CssClass="btn btn-sm btn-outline-secondary" />
                                                 <asp:Button ID="RemoveButton" runat="server" Text="&times;" CommandName="Remove" CommandArgument='<%# Eval("ProductID") %>' CssClass="btn btn-sm btn-danger" />
                                             </div>
                                        </div>
                                    </div>
                                    <div class="col-12 col-md-2 text-right mt-2 mt-md-0"><strong>$<%# Eval("Total", "{0:N2}") %></strong></div>
                                </div>
                            </li>
                        </ItemTemplate>
                         <FooterTemplate></ul></div></FooterTemplate>
                    </asp:Repeater>
                </div>
                <div class="col-lg-4">
                    <div class="card shadow-sm sticky-top" style="top: 80px;">
                        <div class="card-body">
                            <h4 class="card-title">Cart Summary</h4><hr />
                            <div class="d-flex justify-content-between"><span>Subtotal</span><strong><asp:Label ID="SubtotalLabel" runat="server" Text="$0.00"></asp:Label></strong></div>
                             <div class="d-flex justify-content-between mt-2"><span>Shipping</span><span>FREE</span></div>
                            <hr />
                             <div class="d-flex justify-content-between h5"><span>Total</span><strong><asp:Label ID="TotalLabel" runat="server" Text="$0.00"></asp:Label></strong></div>
                            <hr />
                            <asp:Button ID="CheckoutButton" runat="server" Text="Proceed to Checkout" CssClass="btn btn-primary btn-block btn-lg" OnClick="CheckoutButton_Click" />
                            <a href="Shop.aspx" class="btn btn-secondary btn-block mt-2">Continue Shopping</a>
                        </div>
                    </div>
                </div>
            </div>
        </asp:PlaceHolder>
        <asp:PlaceHolder ID="EmptyCartView" runat="server" Visible="false">
             <div class="text-center py-5"><i class="fas fa-shopping-cart fa-4x text-muted"></i><h2 class="mt-3">Your Cart is Empty</h2><p>Looks like you haven't added anything to your cart yet.</p><a href="Shop.aspx" class="btn btn-primary mt-3">Start Shopping</a></div>
        </asp:PlaceHolder>
    </div>
</asp:Content>