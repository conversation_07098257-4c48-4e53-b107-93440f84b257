using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web.UI;
using System.Text;
using System.Collections.Generic;
using System.Web.UI.WebControls;

public partial class Admin_ProductManagement : System.Web.UI.Page
{
    protected void Page_Load(object sender, EventArgs e)
    {
        // --- SECURITY CHECK ---
        if (Session["UserId"] == null || Session["UserRole"]?.ToString() != "Admin")
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        if (!IsPostBack)
        {
            PopulateFilterDropDowns();
            BindProductsGrid();
        }
    }

    /// <summary>
    /// Populates the category filter dropdown from the database.
    /// </summary>
    private void PopulateFilterDropDowns()
    {
        string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
        using (SqlConnection con = new SqlConnection(connectionString))
        {
            try
            {
                string query = "SELECT CategoryId, Name FROM Categories WHERE IsActive = 1 ORDER BY Name";
                SqlDataAdapter sda = new SqlDataAdapter(query, con);
                DataTable dt = new DataTable();
                sda.Fill(dt);
                ddlCategoryFilter.DataSource = dt;
                ddlCategoryFilter.DataBind();
                ddlCategoryFilter.Items.Insert(0, new ListItem("All Categories", ""));
            }
            catch (Exception ex)
            {
                // Log error or show a message
                System.Diagnostics.Debug.WriteLine(ex.Message);
            }
        }
    }

    /// <summary>
    /// Fetches products from the database based on filter criteria and binds them to the GridView.
    /// </summary>
    private void BindProductsGrid()
    {
        string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
        using (SqlConnection con = new SqlConnection(connectionString))
        {
            StringBuilder queryBuilder = new StringBuilder(@"
                SELECT 
                    m.MedicineId, m.Name, m.Price, m.StockQuantity, m.IsActive, 
                    c.Name AS CategoryName 
                FROM Medicines m
                LEFT JOIN Categories c ON m.CategoryId = c.CategoryId
            ");

            SqlCommand cmd = new SqlCommand();
            List<string> conditions = new List<string>();

            // Search Term Filter
            if (!string.IsNullOrWhiteSpace(txtSearch.Text))
            {
                conditions.Add("m.Name LIKE @Search");
                cmd.Parameters.AddWithValue("@Search", "%" + txtSearch.Text.Trim() + "%");
            }

            // Category Filter
            if (!string.IsNullOrEmpty(ddlCategoryFilter.SelectedValue))
            {
                conditions.Add("m.CategoryId = @CategoryId");
                cmd.Parameters.AddWithValue("@CategoryId", ddlCategoryFilter.SelectedValue);
            }

            // Stock Status Filter
            if (!string.IsNullOrEmpty(ddlStockFilter.SelectedValue))
            {
                switch (ddlStockFilter.SelectedValue)
                {
                    case "InStock":
                        conditions.Add("m.StockQuantity > m.ReorderLevel");
                        break;
                    case "LowStock":
                        conditions.Add("m.StockQuantity <= m.ReorderLevel AND m.StockQuantity > 0");
                        break;
                    case "OutOfStock":
                        conditions.Add("m.StockQuantity <= 0");
                        break;
                }
            }

            if (conditions.Count > 0)
            {
                queryBuilder.Append(" WHERE " + string.Join(" AND ", conditions));
            }

            queryBuilder.Append(" ORDER BY m.Name");

            cmd.CommandText = queryBuilder.ToString();
            cmd.Connection = con;

            using (SqlDataAdapter sda = new SqlDataAdapter(cmd))
            {
                DataTable dt = new DataTable();
                sda.Fill(dt);
                gvProducts.DataSource = dt;
                gvProducts.DataBind();
            }
        }
    }

    protected void btnSearch_Click(object sender, EventArgs e)
    {
        gvProducts.PageIndex = 0;
        BindProductsGrid();
    }

    protected void btnClear_Click(object sender, EventArgs e)
    {
        txtSearch.Text = string.Empty;
        ddlCategoryFilter.SelectedIndex = 0;
        ddlStockFilter.SelectedIndex = 0;
        gvProducts.PageIndex = 0;
        BindProductsGrid();
    }

    protected void gvProducts_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        gvProducts.PageIndex = e.NewPageIndex;
        BindProductsGrid();
    }

    protected void btnAddProduct_Click(object sender, EventArgs e)
    {
        Response.Redirect("EditProduct.aspx"); // Redirect to a new page for adding/editing
    }

    protected void gvProducts_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        int medicineId = Convert.ToInt32(e.CommandArgument);

        if (e.CommandName == "EditProduct")
        {
            Response.Redirect($"EditProduct.aspx?id={medicineId}");
        }
        else if (e.CommandName == "DeleteProduct")
        {
            // Note: For a real-world app, it's often better to set an 'IsDeleted' flag 
            // than to permanently delete, to preserve order history integrity.
            // This example performs a hard delete for simplicity.
            DeleteProduct(medicineId);
        }
    }

    private void DeleteProduct(int medicineId)
    {
        // Add deletion logic here
        // Remember to also delete related records or handle foreign key constraints
        // For now, we will just re-bind the grid
        BindProductsGrid();
    }

    protected void btnLogout_Click(object sender, EventArgs e)
    {
        Session.Clear();
        Session.Abandon();
        Response.Redirect("~/Login.aspx");
    }
}
