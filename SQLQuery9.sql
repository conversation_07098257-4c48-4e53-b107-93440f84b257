﻿-- Check if the column already exists before trying to add it
IF NOT EXISTS (SELECT * FROM sys.columns 
               WHERE Name = N'ResponseText' 
               AND Object_ID = Object_ID(N'Feedback'))
BEGIN
    ALTER TABLE Feedback
    ADD ResponseText NVARCHAR(MAX) NULL;
    PRINT 'ResponseText column added to Feedback table successfully.';
END

-- Check if the column already exists before trying to add it
IF NOT EXISTS (SELECT * FROM sys.columns 
               WHERE Name = N'DiscountID' 
               AND Object_ID = Object_ID(N'Orders'))
BEGIN
    ALTER TABLE Orders
    ADD DiscountID INT NULL; -- Add nullable column to store the ID of the discount used

    -- Add a foreign key constraint to link it to the Discounts table
    ALTER TABLE Orders
    ADD CONSTRAINT FK_Orders_Discounts FOREIGN KEY (DiscountID) REFERENCES Discounts(DiscountID);

    PRINT 'DiscountID column added to Orders table successfully.';
END