﻿<%--
 =======================================================================
 FILE: EditPost.aspx
 PURPOSE: Provides a form for administrators to write new blog posts
          and edit existing ones.
 PLACE IN: The 'Admin' folder in your project root.
 =======================================================================
--%>
<%@ Page Title="Edit Post" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="EditPost.aspx.cs" Inherits="MediEase.Admin.EditPost" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><asp:Literal ID="PageTitle" runat="server" Text="Write New Post"></asp:Literal></h1>
            <a href="ManageBlog.aspx" class="btn btn-outline-secondary">&larr; Back to All Posts</a>
        </div>
        
        <asp:PlaceHolder ID="MessagePlaceholder" runat="server" Visible="false">
            <div class="alert alert-success" id="SuccessAlert" runat="server">
                Post saved successfully!
            </div>
        </asp:PlaceHolder>

        <div class="card shadow-sm">
            <div class="card-body">
                <div class="form-group">
                    <label><strong>Post Title</strong></label>
                    <asp:TextBox ID="TitleBox" runat="server" CssClass="form-control form-control-lg"></asp:TextBox>
                    <asp:RequiredFieldValidator runat="server" ControlToValidate="TitleBox" ErrorMessage="Title is required." CssClass="text-danger" />
                </div>
                <div class="form-group">
                    <label><strong>Header Image</strong> (Optional)</label>
                    <asp:FileUpload ID="ImageUpload" runat="server" CssClass="form-control-file" />
                    <asp:Image ID="CurrentImage" runat="server" CssClass="mt-2 img-fluid rounded" style="max-height: 200px;" Visible="false" />
                </div>
                <div class="form-group">
                    <label><strong>Content</strong> (Basic HTML is supported)</label>
                    <asp:TextBox ID="ContentBox" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="20"></asp:TextBox>
                    <asp:RequiredFieldValidator runat="server" ControlToValidate="ContentBox" ErrorMessage="Content is required." CssClass="text-danger" />
                </div>
                <div class="form-group">
                    <asp:CheckBox ID="IsPublishedCheckBox" runat="server" Text=" Mark as Published" />
                </div>
                <hr />
                <asp:Button ID="SavePostButton" runat="server" Text="Save Post" CssClass="btn btn-primary btn-lg" OnClick="SavePostButton_Click" />
                 <asp:HiddenField ID="PostIdField" runat="server" Value="0" />
            </div>
        </div>
    </div>
</asp:Content>
