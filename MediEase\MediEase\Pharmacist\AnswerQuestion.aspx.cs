﻿/*
 =======================================================================
 FILE: AnswerQuestion.aspx.cs
 PURPOSE: Backend logic for viewing a single customer question and
          submitting a pharmacist's answer to the database.
 PLACE IN: The 'Pharmacist' folder in your project root.
 =======================================================================
*/
using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Web.UI;

namespace MediEase.Pharmacist
{
    public partial class AnswerQuestion : Page
    {
        private int questionId; // Class-level variable to hold the QuestionID

        protected void Page_Load(object sender, EventArgs e)
        {
            if (Session["UserID"] == null || Session["RoleName"]?.ToString() != "Pharmacist")
            {
                Response.Redirect("~/Login.aspx");
                return;
            }

            // Safely get the QuestionID from the query string
            if (!int.TryParse(Request.QueryString["QuestionID"], out questionId))
            {
                ShowErrorView();
                return;
            }

            if (!IsPostBack)
            {
                LoadQuestionDetails();
            }
        }

        private void LoadQuestionDetails()
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                // This query joins multiple tables to get all necessary info for the question and potential answer.
                const string query = @"
                    SELECT 
                        q.Subject, q.QuestionText, q.QuestionDate, q.IsAnswered, q.AnswerText, q.AnswerDate,
                        cust.FirstName + ' ' + cust.LastName AS CustomerName,
                        pharm.FirstName + ' ' + pharm.LastName AS PharmacistName
                    FROM PharmacistQuestions q
                    INNER JOIN UserProfiles cust ON q.CustomerID = cust.UserID
                    LEFT JOIN UserProfiles pharm ON q.AnsweredByPharmacistID = pharm.UserID
                    WHERE q.QuestionID = @QuestionID;
                ";

                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@QuestionID", questionId);
                    try
                    {
                        con.Open();
                        SqlDataReader reader = cmd.ExecuteReader();
                        if (reader.Read())
                        {
                            // Populate the labels and literals with question data
                            QuestionIDLabel.Text = questionId.ToString();
                            CustomerNameLabel.Text = reader["CustomerName"].ToString();
                            QuestionDateLabel.Text = Convert.ToDateTime(reader["QuestionDate"]).ToString("MMMM dd, yyyy");
                            SubjectLabel.Text = Server.HtmlEncode(reader["Subject"].ToString());
                            QuestionTextLiteral.Text = Server.HtmlEncode(reader["QuestionText"].ToString());

                            bool isAnswered = Convert.ToBoolean(reader["IsAnswered"]);
                            if (isAnswered)
                            {
                                // If already answered, show the read-only view
                                AnsweredView.Visible = true;
                                AnswerFormView.Visible = false;
                                AnswerTextLiteral.Text = Server.HtmlEncode(reader["AnswerText"].ToString());
                                PharmacistNameLabel.Text = reader["PharmacistName"].ToString();
                                AnswerDateLabel.Text = Convert.ToDateTime(reader["AnswerDate"]).ToString("MMMM dd, yyyy");
                            }
                            else
                            {
                                // If not yet answered, show the form to submit an answer
                                AnsweredView.Visible = false;
                                AnswerFormView.Visible = true;
                            }
                        }
                        else
                        {
                            // If no question with the given ID is found
                            ShowErrorView();
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine("Error loading question details: " + ex.Message);
                        ShowErrorView();
                    }
                }
            }
        }

        protected void SubmitAnswerButton_Click(object sender, EventArgs e)
        {
            if (Page.IsValid)
            {
                string answer = AnswerBox.Text.Trim();
                int pharmacistId = Convert.ToInt32(Session["UserID"]);

                string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    const string query = @"
                        UPDATE PharmacistQuestions 
                        SET AnswerText = @AnswerText, 
                            IsAnswered = 1, 
                            AnsweredByPharmacistID = @PharmacistID, 
                            AnswerDate = GETDATE()
                        WHERE QuestionID = @QuestionID;
                    ";
                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        cmd.Parameters.AddWithValue("@AnswerText", answer);
                        cmd.Parameters.AddWithValue("@PharmacistID", pharmacistId);
                        cmd.Parameters.AddWithValue("@QuestionID", questionId);

                        try
                        {
                            con.Open();
                            cmd.ExecuteNonQuery();

                            // After submitting, reload the page to show the answer in the read-only view
                            LoadQuestionDetails();
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine("Error submitting answer: " + ex.Message);
                            // You can add a literal to show an error message on the page
                        }
                    }
                }
            }
        }

        private void ShowErrorView()
        {
            QuestionView.Visible = false;
            ErrorView.Visible = true;
        }
    }
}
