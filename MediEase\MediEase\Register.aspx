﻿<%--
 =======================================================================
 FILE: Register.aspx (Final Version)
 =======================================================================
--%>
<%@ Page Title="Register" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="Register.aspx.cs" Inherits="MediEase.Register" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
     <div class="row justify-content-center">
        <div class="col-md-8 col-lg-7">
            <div class="card mt-5 shadow-sm">
                <div class="card-body p-4">
                    <h2 class="card-title text-center mb-4">Create a New Account</h2>
                    <asp:Literal runat="server" ID="ErrorMessage" />
                    <div class="form-row">
                         <div class="form-group col-md-6">
                            <label>First Name</label>
                            <asp:TextBox runat="server" ID="FirstName" CssClass="form-control" placeholder="John" />
                            <asp:RequiredFieldValidator runat="server" ControlToValidate="FirstName" CssClass="text-danger" ErrorMessage="First name is required." Display="Dynamic" />
                        </div>
                         <div class="form-group col-md-6">
                            <label>Last Name</label>
                            <asp:TextBox runat="server" ID="LastName" CssClass="form-control" placeholder="Doe" />
                            <asp:RequiredFieldValidator runat="server" ControlToValidate="LastName" CssClass="text-danger" ErrorMessage="Last name is required." Display="Dynamic" />
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Email</label>
                        <asp:TextBox runat="server" ID="Email" CssClass="form-control" TextMode="Email" placeholder="<EMAIL>" />
                        <asp:RequiredFieldValidator runat="server" ControlToValidate="Email" CssClass="text-danger" ErrorMessage="The email field is required." Display="Dynamic" />
                         <asp:RegularExpressionValidator runat="server" ControlToValidate="Email" CssClass="text-danger" ErrorMessage="Please enter a valid email address." ValidationExpression="\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*" Display="Dynamic" />
                    </div>
                    <div class="form-group">
                        <label>Password</label>
                        <asp:TextBox runat="server" ID="Password" CssClass="form-control" TextMode="Password" />
                        <asp:RequiredFieldValidator runat="server" ControlToValidate="Password" CssClass="text-danger" ErrorMessage="The password field is required." Display="Dynamic" />
                    </div>
                    <div class="form-group">
                        <label>Confirm Password</label>
                        <asp:TextBox runat="server" ID="ConfirmPassword" CssClass="form-control" TextMode="Password" />
                        <asp:CompareValidator runat="server" ControlToValidate="ConfirmPassword" ControlToCompare="Password" CssClass="text-danger" ErrorMessage="The password and confirmation password do not match." Display="Dynamic" />
                    </div>
                    <div class="form-group">
                        <asp:Button runat="server" OnClick="CreateUser_Click" Text="Register" CssClass="btn btn-primary btn-block" />
                    </div>
                    <p class="text-center mt-3">
                         <asp:HyperLink runat="server" ID="LoginHyperLink" NavigateUrl="~/Login.aspx">Already have an account? Log in</asp:HyperLink>
                    </p>
                </div>
            </div>
        </div>
    </div>
</asp:Content>