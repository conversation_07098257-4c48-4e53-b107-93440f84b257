﻿/* FILE: Customer/Reminders.aspx.cs */
using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace MediEase.Customer
{
    public partial class Reminders : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (Session["UserID"] == null || Session["RoleName"]?.ToString() != "Customer") { Response.Redirect("~/Login.aspx?returnUrl=" + Server.UrlEncode(Request.Url.AbsolutePath)); return; }
            if (!IsPostBack) { PopulatePurchasedProducts(); BindRemindersGrid(); }
        }

        private void PopulatePurchasedProducts()
        {
            int customerId = Convert.ToInt32(Session["UserID"]);
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "SELECT DISTINCT p.ProductID, p.Name FROM Products p INNER JOIN OrderItems oi ON p.ProductID = oi.ProductID INNER JOIN Orders o ON oi.OrderID = o.OrderID WHERE o.CustomerID = @CustomerID ORDER BY p.Name;";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@CustomerID", customerId);
                    try { con.Open(); SqlDataAdapter sda = new SqlDataAdapter(cmd); DataTable dt = new DataTable(); sda.Fill(dt); ProductDropDown.DataSource = dt; ProductDropDown.DataTextField = "Name"; ProductDropDown.DataValueField = "ProductID"; ProductDropDown.DataBind(); ProductDropDown.Items.Insert(0, new ListItem("-- Select a Medication --", "")); }
                    catch (Exception ex) { System.Diagnostics.Debug.WriteLine("Error populating purchased products: " + ex.Message); }
                }
            }
        }

        private void BindRemindersGrid()
        {
            int customerId = Convert.ToInt32(Session["UserID"]);
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "SELECT r.ReminderID, p.Name, r.ReminderTime, r.Frequency FROM Reminders r INNER JOIN Products p ON r.ProductID = p.ProductID WHERE r.CustomerID = @CustomerID AND r.IsActive = 1 ORDER BY r.ReminderTime;";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@CustomerID", customerId);
                    try { con.Open(); SqlDataAdapter sda = new SqlDataAdapter(cmd); DataTable dt = new DataTable(); sda.Fill(dt); RemindersGrid.DataSource = dt; RemindersGrid.DataBind(); }
                    catch (Exception ex) { System.Diagnostics.Debug.WriteLine("Error binding reminders grid: " + ex.Message); }
                }
            }
        }

        protected void SetReminderButton_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(ProductDropDown.SelectedValue) || string.IsNullOrEmpty(TimeBox.Text)) { return; }

            int customerId = Convert.ToInt32(Session["UserID"]);
            int productId = Convert.ToInt32(ProductDropDown.SelectedValue);
            string productName = ProductDropDown.SelectedItem.Text;
            TimeSpan reminderTime = TimeSpan.Parse(TimeBox.Text);
            string frequency = FrequencyDropDown.SelectedValue;

            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "INSERT INTO Reminders (CustomerID, ProductID, ReminderTime, Frequency, IsActive) VALUES (@CustomerID, @ProductID, @Time, @Frequency, 1)";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@CustomerID", customerId);
                    cmd.Parameters.AddWithValue("@ProductID", productId);
                    cmd.Parameters.AddWithValue("@Time", reminderTime);
                    cmd.Parameters.AddWithValue("@Frequency", frequency);

                    try
                    {
                        con.Open();
                        cmd.ExecuteNonQuery();
                        MessagePlaceholder.Visible = true;

                        // Create on-site notification and send confirmation email
                        string notificationMessage = $"A new reminder has been set for {productName}.";
                        NotificationService.CreateNotification(customerId, notificationMessage, "~/Customer/Reminders.aspx");

                        string customerName = Session["UserName"]?.ToString() ?? "Valued Customer"; // Get user's name if available
                        string userEmail = GetCustomerEmail(customerId);
                        EmailService.SendNewReminderConfirmationEmail(userEmail, customerName, productName, reminderTime.ToString(@"hh\:mm"), frequency);

                        BindRemindersGrid();
                    }
                    catch (Exception ex) { System.Diagnostics.Debug.WriteLine("Error setting reminder: " + ex.Message); }
                }
            }
        }

        protected void RemindersGrid_RowDeleting(object sender, GridViewDeleteEventArgs e)
        {
            int reminderId = Convert.ToInt32(RemindersGrid.DataKeys[e.RowIndex].Value);
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "UPDATE Reminders SET IsActive = 0 WHERE ReminderID = @ReminderID AND CustomerID = @CustomerID";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@ReminderID", reminderId);
                    cmd.Parameters.AddWithValue("@CustomerID", Convert.ToInt32(Session["UserID"]));
                    try { con.Open(); cmd.ExecuteNonQuery(); }
                    catch (Exception ex) { System.Diagnostics.Debug.WriteLine("Error deleting reminder: " + ex.Message); }
                }
            }
            BindRemindersGrid();
        }

        private string GetCustomerEmail(int customerId)
        {
            string email = "";
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "SELECT Email FROM Users WHERE UserID = @UserID";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@UserID", customerId);
                    con.Open();
                    object result = cmd.ExecuteScalar();
                    if (result != null) { email = result.ToString(); }
                }
            }
            return email;
        }
    }
}