﻿

-- =============================================
-- TABLE 1: USERS (Core user management)
-- =============================================
CREATE TABLE [Users] (
    [UserId] INT IDENTITY(1,1) PRIMARY KEY,
    [Email] NVARCHAR(100) NOT NULL UNIQUE,
    [PasswordHash] NVARCHAR(255) NOT NULL,
    [FirstName] NVARCHAR(50) NOT NULL,
    [LastName] NVARCHAR(50) NOT NULL,
    [PhoneNumber] NVARCHAR(20),
    [Role] NVARCHAR(20) NOT NULL DEFAULT 'Customer',
    [IsActive] BIT NOT NULL DEFAULT 1,
    [IsEmailVerified] BIT NOT NULL DEFAULT 0,
    [EmailVerificationToken] NVARCHAR(255),
    [PasswordResetToken] NVARCHAR(255),
    [PasswordResetExpiry] DATETIME,
    [LastLoginDate] DATETIME,
    [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE(),
    [ModifiedDate] DATETIME,
    [Address] NVARCHAR(500),
    [City] NVARCHAR(100),
    [State] NVARCHAR(100),
    [PostalCode] NVARCHAR(20),
    [Country] NVARCHAR(100) DEFAULT 'USA',
    [DateOfBirth] DATE,
    [Gender] NVARCHAR(10),
    [LoyaltyPoints] INT DEFAULT 0,
    [PreferredLanguage] NVARCHAR(10) DEFAULT 'en',
    [LicenseNumber] NVARCHAR(50),
    [LicenseExpiry] DATE,
    [Specialization] NVARCHAR(100),
    [ProfilePicture] NVARCHAR(255),
    [Bio] NVARCHAR(1000)
);

-- =============================================
-- TABLE 2: CATEGORIES (Medicine categories)
-- =============================================
CREATE TABLE [Categories] (
    [CategoryId] INT IDENTITY(1,1) PRIMARY KEY,
    [Name] NVARCHAR(100) NOT NULL UNIQUE,
    [Description] NVARCHAR(500),
    [ImageUrl] NVARCHAR(255),
    [IsActive] BIT NOT NULL DEFAULT 1,
    [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE()
);

-- =============================================
-- TABLE 3: BRANDS (Pharmaceutical brands)
-- =============================================
CREATE TABLE [Brands] (
    [BrandId] INT IDENTITY(1,1) PRIMARY KEY,
    [Name] NVARCHAR(100) NOT NULL UNIQUE,
    [Description] NVARCHAR(500),
    [LogoUrl] NVARCHAR(255),
    [Website] NVARCHAR(255),
    [IsActive] BIT NOT NULL DEFAULT 1,
    [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE()
);

-- =============================================
-- TABLE 4: MEDICINES (Product catalog)
-- =============================================
CREATE TABLE [Medicines] (
    [MedicineId] INT IDENTITY(1,1) PRIMARY KEY,
    [Name] NVARCHAR(200) NOT NULL,
    [GenericName] NVARCHAR(200),
    [Description] NVARCHAR(2000),
    [CategoryId] INT NOT NULL,
    [BrandId] INT,
    [Price] DECIMAL(10,2) NOT NULL,
    [DiscountPercentage] DECIMAL(5,2) DEFAULT 0,
    [CostPrice] DECIMAL(10,2),
    [StockQuantity] INT NOT NULL DEFAULT 0,
    [MinStockLevel] INT DEFAULT 10,
    [MaxStockLevel] INT DEFAULT 1000,
    [ReorderLevel] INT DEFAULT 20,
    [PrescriptionRequired] BIT NOT NULL DEFAULT 0,
    [Dosage] NVARCHAR(100),
    [DosageForm] NVARCHAR(50),
    [Strength] NVARCHAR(50),
    [PackSize] INT DEFAULT 1,
    [NDCNumber] NVARCHAR(50),
    [LotNumber] NVARCHAR(50),
    [ExpiryDate] DATE,
    [ManufactureDate] DATE,
    [IsFeatured] BIT NOT NULL DEFAULT 0,
    [IsNewArrival] BIT NOT NULL DEFAULT 0,
    [IsBestSeller] BIT NOT NULL DEFAULT 0,
    [AverageRating] DECIMAL(3,2) DEFAULT 0,
    [ReviewCount] INT DEFAULT 0,
    [PurchaseCount] INT DEFAULT 0,
    [ImageUrl] NVARCHAR(255),
    [ThumbnailUrl] NVARCHAR(255),
    [SEOTitle] NVARCHAR(200),
    [SEODescription] NVARCHAR(500),
    [SEOKeywords] NVARCHAR(500),
    [IsActive] BIT NOT NULL DEFAULT 1,
    [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE(),
    [ModifiedDate] DATETIME,
    [CreatedBy] INT,
    [ModifiedBy] INT,
    FOREIGN KEY ([CategoryId]) REFERENCES [Categories]([CategoryId]),
    FOREIGN KEY ([BrandId]) REFERENCES [Brands]([BrandId]),
    FOREIGN KEY ([CreatedBy]) REFERENCES [Users]([UserId]),
    FOREIGN KEY ([ModifiedBy]) REFERENCES [Users]([UserId])
);

-- =============================================
-- TABLE 5: ORDERS (Customer orders)
-- =============================================
CREATE TABLE [Orders] (
    [OrderId] INT IDENTITY(1,1) PRIMARY KEY,
    [OrderNumber] NVARCHAR(50) NOT NULL UNIQUE,
    [CustomerId] INT NOT NULL,
    [Status] NVARCHAR(20) NOT NULL DEFAULT 'Pending',
    [OrderDate] DATETIME NOT NULL DEFAULT GETDATE(),
    [ExpectedDeliveryDate] DATETIME,
    [ActualDeliveryDate] DATETIME,
    [Subtotal] DECIMAL(10,2) NOT NULL,
    [TaxAmount] DECIMAL(10,2) DEFAULT 0,
    [ShippingCost] DECIMAL(10,2) DEFAULT 0,
    [DiscountAmount] DECIMAL(10,2) DEFAULT 0,
    [TotalAmount] DECIMAL(10,2) NOT NULL,
    [PaymentMethod] NVARCHAR(50),
    [PaymentStatus] NVARCHAR(20) DEFAULT 'Pending',
    [PaymentReference] NVARCHAR(100),
    [PaymentTransactionId] NVARCHAR(100),
    [PaymentDate] DATETIME,
    [InvoiceGenerated] BIT DEFAULT 0,
    [InvoiceDate] DATETIME,
    [InvoiceNumber] NVARCHAR(50),
    [ShippingAddress] NVARCHAR(500) NOT NULL,
    [ShippingCity] NVARCHAR(100),
    [ShippingState] NVARCHAR(100),
    [ShippingPostalCode] NVARCHAR(20),
    [ShippingCountry] NVARCHAR(100),
    [ContactPhone] NVARCHAR(20),
    [ContactEmail] NVARCHAR(100),
    [TrackingNumber] NVARCHAR(100),
    [CourierService] NVARCHAR(100),
    [DeliveryInstructions] NVARCHAR(500),
    [RequiresPrescription] BIT DEFAULT 0,
    [PrescriptionVerified] BIT DEFAULT 0,
    [VerifiedBy] INT,
    [VerificationDate] DATETIME,
    [VerificationNotes] NVARCHAR(500),
    [IsUrgent] BIT DEFAULT 0,
    [IsGift] BIT DEFAULT 0,
    [Notes] NVARCHAR(1000),
    [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE(),
    [ModifiedDate] DATETIME,
    [CreatedBy] INT,
    [ModifiedBy] INT,
    FOREIGN KEY ([CustomerId]) REFERENCES [Users]([UserId]),
    FOREIGN KEY ([VerifiedBy]) REFERENCES [Users]([UserId]),
    FOREIGN KEY ([CreatedBy]) REFERENCES [Users]([UserId]),
    FOREIGN KEY ([ModifiedBy]) REFERENCES [Users]([UserId])
);

-- =============================================
-- TABLE 6: ORDER ITEMS (Order line items)
-- =============================================
CREATE TABLE [OrderItems] (
    [OrderItemId] INT IDENTITY(1,1) PRIMARY KEY,
    [OrderId] INT NOT NULL,
    [MedicineId] INT NOT NULL,
    [Quantity] INT NOT NULL,
    [UnitPrice] DECIMAL(10,2) NOT NULL,
    [DiscountAmount] DECIMAL(10,2) DEFAULT 0,
    [TotalPrice] DECIMAL(10,2) NOT NULL,
    [SpecialInstructions] NVARCHAR(500),
    [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE(),
    FOREIGN KEY ([OrderId]) REFERENCES [Orders]([OrderId]) ON DELETE CASCADE,
    FOREIGN KEY ([MedicineId]) REFERENCES [Medicines]([MedicineId])
);

-- =============================================
-- TABLE 7: SHOPPING CART (Shopping cart items)
-- =============================================
CREATE TABLE [ShoppingCart] (
    [CartId] INT IDENTITY(1,1) PRIMARY KEY,
    [UserId] INT NOT NULL,
    [MedicineId] INT NOT NULL,
    [Quantity] INT NOT NULL,
    [AddedDate] DATETIME NOT NULL DEFAULT GETDATE(),
    [ModifiedDate] DATETIME,
    FOREIGN KEY ([UserId]) REFERENCES [Users]([UserId]) ON DELETE CASCADE,
    FOREIGN KEY ([MedicineId]) REFERENCES [Medicines]([MedicineId]),
    UNIQUE ([UserId], [MedicineId])
);

-- =============================================
-- TABLE 8: PRESCRIPTIONS (Prescription uploads)
-- =============================================
CREATE TABLE [Prescriptions] (
    [PrescriptionId] INT IDENTITY(1,1) PRIMARY KEY,
    [UserId] INT NOT NULL,
    [PrescriptionNumber] NVARCHAR(50) NOT NULL UNIQUE,
    [DoctorName] NVARCHAR(100) NOT NULL,
    [DoctorLicense] NVARCHAR(50),
    [DoctorPhone] NVARCHAR(20),
    [DoctorEmail] NVARCHAR(100),
    [ClinicName] NVARCHAR(200),
    [ClinicAddress] NVARCHAR(500),
    [PrescriptionDate] DATE NOT NULL,
    [ExpiryDate] DATE,
    [Diagnosis] NVARCHAR(500),
    [Instructions] NVARCHAR(1000),
    [OriginalFileName] NVARCHAR(255),
    [FilePath] NVARCHAR(500),
    [FileSize] BIGINT,
    [FileType] NVARCHAR(50),
    [AIProcessed] BIT DEFAULT 0,
    [AIExtractedText] NVARCHAR(MAX),
    [AIConfidenceScore] DECIMAL(5,2),
    [AIProcessingDate] DATETIME,
    [Status] NVARCHAR(20) DEFAULT 'Pending',
    [VerifiedBy] INT,
    [VerificationDate] DATETIME,
    [VerificationNotes] NVARCHAR(1000),
    [RejectionReason] NVARCHAR(500),
    [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE(),
    [ModifiedDate] DATETIME,
    FOREIGN KEY ([UserId]) REFERENCES [Users]([UserId]),
    FOREIGN KEY ([VerifiedBy]) REFERENCES [Users]([UserId])
);

-- =============================================
-- TABLE 9: PRESCRIPTION ITEMS (Prescription medications)
-- =============================================
CREATE TABLE [PrescriptionItems] (
    [PrescriptionItemId] INT IDENTITY(1,1) PRIMARY KEY,
    [PrescriptionId] INT NOT NULL,
    [MedicineId] INT,
    [MedicineName] NVARCHAR(200) NOT NULL,
    [Dosage] NVARCHAR(100),
    [Frequency] NVARCHAR(100),
    [Duration] NVARCHAR(100),
    [Quantity] INT,
    [Instructions] NVARCHAR(500),
    [IsDispensed] BIT DEFAULT 0,
    [DispensedDate] DATETIME,
    [DispensedBy] INT,
    FOREIGN KEY ([PrescriptionId]) REFERENCES [Prescriptions]([PrescriptionId]) ON DELETE CASCADE,
    FOREIGN KEY ([MedicineId]) REFERENCES [Medicines]([MedicineId]),
    FOREIGN KEY ([DispensedBy]) REFERENCES [Users]([UserId])
);

-- =============================================
-- TABLE 10: FAMILY PROFILES (Family member management)
-- =============================================
CREATE TABLE [FamilyProfiles] (
    [FamilyProfileId] INT IDENTITY(1,1) PRIMARY KEY,
    [UserId] INT NOT NULL,
    [MemberName] NVARCHAR(100) NOT NULL,
    [Relationship] NVARCHAR(50),
    [DateOfBirth] DATE,
    [Gender] NVARCHAR(10),
    [BloodGroup] NVARCHAR(10),
    [Allergies] NVARCHAR(1000),
    [MedicalConditions] NVARCHAR(1000),
    [EmergencyContact] NVARCHAR(100),
    [EmergencyPhone] NVARCHAR(20),
    [IsActive] BIT DEFAULT 1,
    [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE(),
    FOREIGN KEY ([UserId]) REFERENCES [Users]([UserId]) ON DELETE CASCADE
);

-- =============================================
-- TABLE 11: HEALTH REMINDERS (Medication reminders)
-- =============================================
CREATE TABLE [HealthReminders] (
    [ReminderId] INT IDENTITY(1,1) PRIMARY KEY,
    [UserId] INT NOT NULL,
    [FamilyProfileId] INT,
    [Title] NVARCHAR(200) NOT NULL,
    [Description] NVARCHAR(1000),
    [ReminderType] NVARCHAR(50),
    [NextReminderDate] DATETIME NOT NULL,
    [Frequency] NVARCHAR(50),
    [FrequencyValue] INT DEFAULT 1,
    [EndDate] DATETIME,
    [IsActive] BIT DEFAULT 1,
    [IsCompleted] BIT DEFAULT 0,
    [CompletedDate] DATETIME,
    [SnoozeCount] INT DEFAULT 0,
    [LastSnoozedDate] DATETIME,
    [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE(),
    FOREIGN KEY ([UserId]) REFERENCES [Users]([UserId]) ON DELETE CASCADE,
    FOREIGN KEY ([FamilyProfileId]) REFERENCES [FamilyProfiles]([FamilyProfileId])
);

-- =============================================
-- TABLE 12: MEDICINE REVIEWS (Product reviews)
-- =============================================
CREATE TABLE [MedicineReviews] (
    [ReviewId] INT IDENTITY(1,1) PRIMARY KEY,
    [MedicineId] INT NOT NULL,
    [UserId] INT NOT NULL,
    [OrderId] INT,
    [Rating] INT NOT NULL CHECK ([Rating] >= 1 AND [Rating] <= 5),
    [Title] NVARCHAR(200),
    [ReviewText] NVARCHAR(2000),
    [IsVerifiedPurchase] BIT DEFAULT 0,
    [IsApproved] BIT DEFAULT 0,
    [ApprovedBy] INT,
    [ApprovedDate] DATETIME,
    [HelpfulVotes] INT DEFAULT 0,
    [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE(),
    [ModifiedDate] DATETIME,
    FOREIGN KEY ([MedicineId]) REFERENCES [Medicines]([MedicineId]),
    FOREIGN KEY ([UserId]) REFERENCES [Users]([UserId]),
    FOREIGN KEY ([OrderId]) REFERENCES [Orders]([OrderId]),
    FOREIGN KEY ([ApprovedBy]) REFERENCES [Users]([UserId])
);

-- =============================================
-- TABLE 13: LOYALTY TRANSACTIONS (Points tracking)
-- =============================================
CREATE TABLE [LoyaltyTransactions] (
    [TransactionId] INT IDENTITY(1,1) PRIMARY KEY,
    [UserId] INT NOT NULL,
    [OrderId] INT,
    [TransactionType] NVARCHAR(20) NOT NULL,
    [Points] INT NOT NULL,
    [Description] NVARCHAR(500),
    [ExpiryDate] DATETIME,
    [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE(),
    FOREIGN KEY ([UserId]) REFERENCES [Users]([UserId]),
    FOREIGN KEY ([OrderId]) REFERENCES [Orders]([OrderId])
);

-- =============================================
-- TABLE 14: OFFERS (Coupons and promotions)
-- =============================================
CREATE TABLE [Offers] (
    [OfferId] INT IDENTITY(1,1) PRIMARY KEY,
    [Code] NVARCHAR(50) NOT NULL UNIQUE,
    [Title] NVARCHAR(200) NOT NULL,
    [Description] NVARCHAR(1000),
    [OfferType] NVARCHAR(20) NOT NULL,
    [DiscountValue] DECIMAL(10,2) NOT NULL,
    [MinOrderAmount] DECIMAL(10,2) DEFAULT 0,
    [MaxDiscountAmount] DECIMAL(10,2),
    [UsageLimit] INT,
    [UsageCount] INT DEFAULT 0,
    [UserUsageLimit] INT DEFAULT 1,
    [StartDate] DATETIME NOT NULL,
    [EndDate] DATETIME NOT NULL,
    [IsActive] BIT DEFAULT 1,
    [ApplicableCategories] NVARCHAR(500),
    [ApplicableMedicines] NVARCHAR(500),
    [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE(),
    [CreatedBy] INT,
    FOREIGN KEY ([CreatedBy]) REFERENCES [Users]([UserId])
);

-- =============================================
-- TABLE 15: USER OFFER USAGE (Coupon usage tracking)
-- =============================================
CREATE TABLE [UserOfferUsage] (
    [UsageId] INT IDENTITY(1,1) PRIMARY KEY,
    [UserId] INT NOT NULL,
    [OfferId] INT NOT NULL,
    [OrderId] INT NOT NULL,
    [DiscountAmount] DECIMAL(10,2) NOT NULL,
    [UsedDate] DATETIME NOT NULL DEFAULT GETDATE(),
    FOREIGN KEY ([UserId]) REFERENCES [Users]([UserId]),
    FOREIGN KEY ([OfferId]) REFERENCES [Offers]([OfferId]),
    FOREIGN KEY ([OrderId]) REFERENCES [Orders]([OrderId])
);

-- =============================================
-- TABLE 16: NOTIFICATIONS (User notifications)
-- =============================================
CREATE TABLE [Notifications] (
    [NotificationId] INT IDENTITY(1,1) PRIMARY KEY,
    [UserId] INT NOT NULL,
    [Title] NVARCHAR(200) NOT NULL,
    [Message] NVARCHAR(1000) NOT NULL,
    [NotificationType] NVARCHAR(50),
    [RelatedEntityType] NVARCHAR(50),
    [RelatedEntityId] INT,
    [IsRead] BIT DEFAULT 0,
    [ReadDate] DATETIME,
    [Priority] NVARCHAR(20) DEFAULT 'Normal',
    [ExpiryDate] DATETIME,
    [ActionUrl] NVARCHAR(500),
    [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE(),
    FOREIGN KEY ([UserId]) REFERENCES [Users]([UserId]) ON DELETE CASCADE
);

-- =============================================
-- TABLE 17: CHAT SESSIONS (AI chatbot sessions)
-- =============================================
CREATE TABLE [ChatSessions] (
    [SessionId] INT IDENTITY(1,1) PRIMARY KEY,
    [UserId] INT,
    [SessionToken] NVARCHAR(255) NOT NULL UNIQUE,
    [StartDate] DATETIME NOT NULL DEFAULT GETDATE(),
    [EndDate] DATETIME,
    [IsActive] BIT DEFAULT 1,
    [UserAgent] NVARCHAR(500),
    [IPAddress] NVARCHAR(45),
    FOREIGN KEY ([UserId]) REFERENCES [Users]([UserId])
);

-- =============================================
-- TABLE 18: CHAT MESSAGES (Chat conversation history)
-- =============================================
CREATE TABLE [ChatMessages] (
    [MessageId] INT IDENTITY(1,1) PRIMARY KEY,
    [SessionId] INT NOT NULL,
    [MessageType] NVARCHAR(20) NOT NULL,
    [Message] NVARCHAR(MAX) NOT NULL,
    [Timestamp] DATETIME NOT NULL DEFAULT GETDATE(),
    [AIModel] NVARCHAR(100),
    [ResponseTime] INT,
    [TokensUsed] INT,
    [ConfidenceScore] DECIMAL(5,2),
    FOREIGN KEY ([SessionId]) REFERENCES [ChatSessions]([SessionId]) ON DELETE CASCADE
);

-- =============================================
-- TABLE 19: AI RECOMMENDATIONS (AI-generated suggestions)
-- =============================================
CREATE TABLE [AIRecommendations] (
    [RecommendationId] INT IDENTITY(1,1) PRIMARY KEY,
    [UserId] INT,
    [SessionId] INT,
    [RecommendationType] NVARCHAR(50),
    [MedicineId] INT,
    [RecommendationText] NVARCHAR(2000),
    [ConfidenceScore] DECIMAL(5,2),
    [Reasoning] NVARCHAR(1000),
    [IsAccepted] BIT,
    [UserFeedback] NVARCHAR(500),
    [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE(),
    FOREIGN KEY ([UserId]) REFERENCES [Users]([UserId]),
    FOREIGN KEY ([SessionId]) REFERENCES [ChatSessions]([SessionId]),
    FOREIGN KEY ([MedicineId]) REFERENCES [Medicines]([MedicineId])
);

-- =============================================
-- TABLE 20: SYSTEM SETTINGS (Application configuration)
-- =============================================
CREATE TABLE [SystemSettings] (
    [SettingId] INT IDENTITY(1,1) PRIMARY KEY,
    [SettingKey] NVARCHAR(100) NOT NULL UNIQUE,
    [SettingValue] NVARCHAR(2000),
    [Description] NVARCHAR(500),
    [DataType] NVARCHAR(20) DEFAULT 'String',
    [Category] NVARCHAR(50),
    [IsEditable] BIT DEFAULT 1,
    [ModifiedDate] DATETIME,
    [ModifiedBy] INT,
    FOREIGN KEY ([ModifiedBy]) REFERENCES [Users]([UserId])
);

-- =============================================
-- TABLE 21: AUDIT LOGS (User activity tracking)
-- =============================================
CREATE TABLE [AuditLogs] (
    [LogId] INT IDENTITY(1,1) PRIMARY KEY,
    [UserId] INT,
    [Action] NVARCHAR(100) NOT NULL,
    [EntityType] NVARCHAR(50),
    [EntityId] INT,
    [OldValues] NVARCHAR(MAX),
    [NewValues] NVARCHAR(MAX),
    [IPAddress] NVARCHAR(45),
    [UserAgent] NVARCHAR(500),
    [Timestamp] DATETIME NOT NULL DEFAULT GETDATE(),
    FOREIGN KEY ([UserId]) REFERENCES [Users]([UserId])
);

-- =============================================
-- TABLE 22: ERROR LOGS (Error and exception logging)
-- =============================================
CREATE TABLE [ErrorLogs] (
    [ErrorId] INT IDENTITY(1,1) PRIMARY KEY,
    [ErrorMessage] NVARCHAR(2000) NOT NULL,
    [StackTrace] NVARCHAR(MAX),
    [Source] NVARCHAR(200),
    [UserId] INT,
    [RequestUrl] NVARCHAR(500),
    [HttpMethod] NVARCHAR(10),
    [IPAddress] NVARCHAR(45),
    [UserAgent] NVARCHAR(500),
    [Severity] NVARCHAR(20) DEFAULT 'Error',
    [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE(),
    FOREIGN KEY ([UserId]) REFERENCES [Users]([UserId])
);

-- =============================================
-- TABLE 23: MEDICINE IMAGES (Product images)
-- =============================================
CREATE TABLE [MedicineImages] (
    [ImageId] INT IDENTITY(1,1) PRIMARY KEY,
    [MedicineId] INT NOT NULL,
    [ImageUrl] NVARCHAR(255) NOT NULL,
    [AltText] NVARCHAR(200),
    [IsPrimary] BIT NOT NULL DEFAULT 0,
    [DisplayOrder] INT DEFAULT 0,
    [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE(),
    FOREIGN KEY ([MedicineId]) REFERENCES [Medicines]([MedicineId]) ON DELETE CASCADE
);

-- =============================================
-- TABLE 24: STOCK MOVEMENTS (Inventory tracking)
-- =============================================
CREATE TABLE [StockMovements] (
    [MovementId] INT IDENTITY(1,1) PRIMARY KEY,
    [MedicineId] INT NOT NULL,
    [MovementType] NVARCHAR(20) NOT NULL,
    [Quantity] INT NOT NULL,
    [PreviousStock] INT NOT NULL,
    [NewStock] INT NOT NULL,
    [UnitCost] DECIMAL(10,2),
    [TotalCost] DECIMAL(10,2),
    [Reference] NVARCHAR(100),
    [Notes] NVARCHAR(500),
    [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE(),
    [CreatedBy] INT,
    FOREIGN KEY ([MedicineId]) REFERENCES [Medicines]([MedicineId]),
    FOREIGN KEY ([CreatedBy]) REFERENCES [Users]([UserId])
);

-- =============================================
-- TABLE 25: MEDICINE INTERACTIONS (Drug interactions)
-- =============================================
CREATE TABLE [MedicineInteractions] (
    [InteractionId] INT IDENTITY(1,1) PRIMARY KEY,
    [MedicineId1] INT NOT NULL,
    [MedicineId2] INT NOT NULL,
    [InteractionType] NVARCHAR(50),
    [Description] NVARCHAR(1000),
    [Severity] NVARCHAR(20),
    [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE(),
    FOREIGN KEY ([MedicineId1]) REFERENCES [Medicines]([MedicineId]),
    FOREIGN KEY ([MedicineId2]) REFERENCES [Medicines]([MedicineId])
);

-- =============================================
-- PERFORMANCE INDEXES
-- =============================================

-- Users Table Indexes
CREATE INDEX IX_Users_Email ON [Users]([Email]);
CREATE INDEX IX_Users_Role ON [Users]([Role]);
CREATE INDEX IX_Users_IsActive ON [Users]([IsActive]);

-- Medicines Table Indexes
CREATE INDEX IX_Medicines_Category ON [Medicines]([CategoryId]);
CREATE INDEX IX_Medicines_Brand ON [Medicines]([BrandId]);
CREATE INDEX IX_Medicines_Price ON [Medicines]([Price]);
CREATE INDEX IX_Medicines_Stock ON [Medicines]([StockQuantity]);
CREATE INDEX IX_Medicines_Featured ON [Medicines]([IsFeatured]);
CREATE INDEX IX_Medicines_Active ON [Medicines]([IsActive]);
CREATE INDEX IX_Medicines_Name ON [Medicines]([Name]);

-- Orders Table Indexes
CREATE INDEX IX_Orders_Customer ON [Orders]([CustomerId]);
CREATE INDEX IX_Orders_Status ON [Orders]([Status]);
CREATE INDEX IX_Orders_Date ON [Orders]([OrderDate]);
CREATE INDEX IX_Orders_PaymentStatus ON [Orders]([PaymentStatus]);
CREATE INDEX IX_Orders_OrderNumber ON [Orders]([OrderNumber]);

-- Order Items Table Indexes
CREATE INDEX IX_OrderItems_Order ON [OrderItems]([OrderId]);
CREATE INDEX IX_OrderItems_Medicine ON [OrderItems]([MedicineId]);

-- Shopping Cart Table Indexes
CREATE INDEX IX_ShoppingCart_User ON [ShoppingCart]([UserId]);

-- Prescriptions Table Indexes
CREATE INDEX IX_Prescriptions_User ON [Prescriptions]([UserId]);
CREATE INDEX IX_Prescriptions_Status ON [Prescriptions]([Status]);
CREATE INDEX IX_Prescriptions_Date ON [Prescriptions]([PrescriptionDate]);
CREATE INDEX IX_Prescriptions_Number ON [Prescriptions]([PrescriptionNumber]);

-- Additional Indexes
CREATE INDEX IX_FamilyProfiles_User ON [FamilyProfiles]([UserId]);
CREATE INDEX IX_HealthReminders_User ON [HealthReminders]([UserId]);
CREATE INDEX IX_HealthReminders_Date ON [HealthReminders]([NextReminderDate]);
CREATE INDEX IX_MedicineReviews_Medicine ON [MedicineReviews]([MedicineId]);
CREATE INDEX IX_MedicineReviews_User ON [MedicineReviews]([UserId]);
CREATE INDEX IX_LoyaltyTransactions_User ON [LoyaltyTransactions]([UserId]);
CREATE INDEX IX_Notifications_User ON [Notifications]([UserId]);
CREATE INDEX IX_Notifications_Read ON [Notifications]([IsRead]);
CREATE INDEX IX_ChatSessions_User ON [ChatSessions]([UserId]);
CREATE INDEX IX_ChatMessages_Session ON [ChatMessages]([SessionId]);
CREATE INDEX IX_StockMovements_Medicine ON [StockMovements]([MedicineId]);
CREATE INDEX IX_StockMovements_Date ON [StockMovements]([CreatedDate]);

-- =============================================
-- SAMPLE DATA INSERTION
-- =============================================

-- Insert Categories
INSERT INTO [Categories] ([Name], [Description], [IsActive]) VALUES
('Pain Relief', 'Medications for pain management and relief', 1),
('Antibiotics', 'Prescription antibiotics for bacterial infections', 1),
('Vitamins & Supplements', 'Nutritional supplements and vitamins', 1),
('Cold & Flu', 'Medications for cold and flu symptoms', 1),
('Digestive Health', 'Medications for digestive and stomach issues', 1),
('Heart & Blood Pressure', 'Cardiovascular medications', 1),
('Diabetes Care', 'Medications and supplies for diabetes management', 1),
('Skin Care', 'Topical medications and skin treatments', 1),
('Eye Care', 'Eye drops and vision care products', 1),
('First Aid', 'Emergency and first aid supplies', 1),
('Mental Health', 'Medications for mental health conditions', 1),
('Respiratory', 'Medications for respiratory conditions', 1),
('Women Health', 'Medications specific to women health', 1),
('Men Health', 'Medications specific to men health', 1),
('Child Care', 'Medications and products for children', 1);

-- Insert Brands
INSERT INTO [Brands] ([Name], [Description], [Website], [IsActive]) VALUES
('Generic', 'Generic pharmaceutical products', 'https://generic.com', 1),
('Pfizer', 'Leading pharmaceutical company', 'https://pfizer.com', 1),
('Johnson & Johnson', 'Healthcare and pharmaceutical products', 'https://jnj.com', 1),
('Bayer', 'German pharmaceutical and life sciences company', 'https://bayer.com', 1),
('GSK', 'GlaxoSmithKline pharmaceutical products', 'https://gsk.com', 1),
('Novartis', 'Swiss multinational pharmaceutical company', 'https://novartis.com', 1),
('Merck', 'American multinational pharmaceutical company', 'https://merck.com', 1),
('Abbott', 'Healthcare and pharmaceutical products', 'https://abbott.com', 1),
('Roche', 'Swiss multinational healthcare company', 'https://roche.com', 1),
('Sanofi', 'French multinational pharmaceutical company', 'https://sanofi.com', 1),
('AstraZeneca', 'British-Swedish multinational pharmaceutical company', 'https://astrazeneca.com', 1),
('Bristol Myers Squibb', 'American multinational pharmaceutical company', 'https://bms.com', 1),
('Eli Lilly', 'American pharmaceutical company', 'https://lilly.com', 1),
('Gilead Sciences', 'American biopharmaceutical company', 'https://gilead.com', 1),
('Amgen', 'American multinational biopharmaceutical company', 'https://amgen.com', 1);

-- Insert Sample Users (Admin, Pharmacist, Customer)
INSERT INTO [Users] ([Email], [PasswordHash], [FirstName], [LastName], [PhoneNumber], [Role], [IsActive], [IsEmailVerified], [Address], [City], [State], [PostalCode], [Country], [LoyaltyPoints]) VALUES
('<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'System', 'Administrator', '******-0001', 'Admin', 1, 1, '123 Admin Street', 'New York', 'NY', '10001', 'USA', 0),
('<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'John', 'Pharmacist', '******-0002', 'Pharmacist', 1, 1, '456 Pharmacy Lane', 'Los Angeles', 'CA', '90001', 'USA', 0),
('<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Jane', 'Customer', '******-0003', 'Customer', 1, 1, '789 Customer Road', 'Chicago', 'IL', '60601', 'USA', 150),
('<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Dr. Michael', 'Smith', '******-0004', 'Customer', 1, 1, '321 Medical Center', 'Houston', 'TX', '77001', 'USA', 75),
('<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Sarah', 'Johnson', '******-0005', 'Customer', 1, 1, '654 Wellness Ave', 'Phoenix', 'AZ', '85001', 'USA', 200);

-- Insert Sample Medicines (Comprehensive list)
INSERT INTO [Medicines] ([Name], [GenericName], [Description], [CategoryId], [BrandId], [Price], [DiscountPercentage], [CostPrice], [StockQuantity], [MinStockLevel], [PrescriptionRequired], [Dosage], [DosageForm], [Strength], [IsFeatured], [IsActive], [AverageRating], [ReviewCount], [PurchaseCount]) VALUES
-- Pain Relief Medicines
('Tylenol Extra Strength', 'Acetaminophen', 'Fast-acting pain relief for headaches, muscle aches, and fever', 1, 3, 12.99, 10, 8.50, 150, 20, 0, '500mg', 'Tablet', '500mg', 1, 1, 4.5, 25, 120),
('Advil Liqui-Gels', 'Ibuprofen', 'Fast pain relief in liquid-filled capsules', 1, 3, 15.49, 15, 10.20, 200, 25, 0, '200mg', 'Capsule', '200mg', 1, 1, 4.3, 18, 95),
('Aspirin 325mg', 'Aspirin', 'Pain relief and heart health support', 1, 4, 8.99, 5, 6.00, 300, 30, 0, '325mg', 'Tablet', '325mg', 0, 1, 4.1, 12, 80),
('Motrin IB', 'Ibuprofen', 'Ibuprofen pain reliever and fever reducer', 1, 3, 11.99, 8, 8.00, 180, 20, 0, '200mg', 'Tablet', '200mg', 0, 1, 4.2, 15, 65),

-- Antibiotics (Prescription Required)
('Amoxicillin 500mg', 'Amoxicillin', 'Broad-spectrum antibiotic for bacterial infections', 2, 1, 25.99, 0, 18.50, 75, 15, 1, '500mg', 'Capsule', '500mg', 0, 1, 4.6, 8, 45),
('Azithromycin Z-Pack', 'Azithromycin', 'Antibiotic for respiratory and skin infections', 2, 2, 35.99, 5, 28.00, 60, 10, 1, '250mg', 'Tablet', '250mg', 0, 1, 4.4, 6, 32),
('Ciprofloxacin 500mg', 'Ciprofloxacin', 'Fluoroquinolone antibiotic for various infections', 2, 1, 42.50, 0, 32.00, 45, 8, 1, '500mg', 'Tablet', '500mg', 0, 1, 4.3, 4, 28),

-- Vitamins & Supplements
('Vitamin D3 1000 IU', 'Cholecalciferol', 'Essential vitamin for bone health and immune support', 3, 1, 18.99, 20, 12.00, 300, 40, 0, '1000 IU', 'Tablet', '1000 IU', 1, 1, 4.7, 35, 180),
('Multivitamin Complete', 'Multivitamin', 'Complete daily vitamin and mineral supplement', 3, 8, 24.99, 15, 16.50, 250, 35, 0, '1 tablet', 'Tablet', 'Daily', 1, 1, 4.4, 28, 145),
('Vitamin C 1000mg', 'Ascorbic Acid', 'Immune system support and antioxidant', 3, 1, 14.99, 12, 10.00, 400, 50, 0, '1000mg', 'Tablet', '1000mg', 0, 1, 4.5, 22, 160),
('Omega-3 Fish Oil', 'Fish Oil', 'Heart and brain health support', 3, 8, 29.99, 18, 20.00, 180, 25, 0, '1000mg', 'Softgel', '1000mg', 1, 1, 4.6, 31, 125),

-- Cold & Flu
('Robitussin DM', 'Dextromethorphan', 'Cough suppressant and expectorant', 4, 3, 11.99, 5, 8.50, 120, 20, 0, '15mg/5ml', 'Syrup', '15mg/5ml', 0, 1, 4.2, 14, 75),
('Sudafed PE', 'Phenylephrine', 'Nasal decongestant for sinus pressure', 4, 3, 9.99, 8, 7.00, 150, 25, 0, '10mg', 'Tablet', '10mg', 0, 1, 4.0, 11, 55),
('Mucinex', 'Guaifenesin', 'Expectorant to loosen chest congestion', 4, 1, 16.99, 10, 12.00, 100, 15, 0, '600mg', 'Tablet', '600mg', 0, 1, 4.3, 9, 42),

-- Digestive Health
('Pepto-Bismol', 'Bismuth Subsalicylate', 'Relief from upset stomach, nausea, and diarrhea', 5, 3, 9.99, 0, 7.50, 180, 25, 0, '262mg', 'Tablet', '262mg', 0, 1, 4.1, 16, 85),
('Tums Extra Strength', 'Calcium Carbonate', 'Fast relief from heartburn and acid indigestion', 5, 1, 7.99, 12, 5.50, 220, 30, 0, '750mg', 'Chewable', '750mg', 0, 1, 4.2, 19, 95),
('Imodium A-D', 'Loperamide', 'Anti-diarrheal medication', 5, 3, 12.99, 6, 9.00, 140, 20, 0, '2mg', 'Capsule', '2mg', 0, 1, 4.4, 13, 68),

-- Heart & Blood Pressure (Prescription Required)
('Lisinopril 10mg', 'Lisinopril', 'ACE inhibitor for high blood pressure', 6, 1, 32.50, 0, 25.00, 90, 15, 1, '10mg', 'Tablet', '10mg', 0, 1, 4.5, 12, 55),
('Metoprolol 50mg', 'Metoprolol', 'Beta-blocker for heart conditions', 6, 1, 28.75, 0, 22.00, 85, 12, 1, '50mg', 'Tablet', '50mg', 0, 1, 4.3, 8, 38),
('Atorvastatin 20mg', 'Atorvastatin', 'Statin for cholesterol management', 6, 2, 45.99, 5, 35.00, 70, 10, 1, '20mg', 'Tablet', '20mg', 0, 1, 4.4, 15, 62),

-- Diabetes Care (Prescription Required)
('Metformin 500mg', 'Metformin HCl', 'Type 2 diabetes medication', 7, 1, 28.75, 0, 22.00, 100, 15, 1, '500mg', 'Tablet', '500mg', 0, 1, 4.6, 18, 78),
('Insulin Glargine', 'Insulin Glargine', 'Long-acting insulin for diabetes', 7, 9, 125.99, 8, 95.00, 35, 5, 1, '100 units/ml', 'Injection', '100 units/ml', 0, 1, 4.7, 22, 45),

-- Skin Care
('Hydrocortisone Cream 1%', 'Hydrocortisone', 'Topical anti-inflammatory for skin irritation', 8, 1, 7.99, 0, 5.50, 250, 35, 0, '1%', 'Cream', '1%', 0, 1, 4.3, 21, 110),
('Neosporin Ointment', 'Neomycin/Polymyxin/Bacitracin', 'Antibiotic ointment for minor cuts and scrapes', 8, 3, 8.99, 10, 6.50, 200, 30, 0, '1 oz', 'Ointment', 'Triple Antibiotic', 1, 1, 4.5, 17, 88),

-- Eye Care
('Artificial Tears', 'Polyethylene Glycol', 'Lubricating eye drops for dry eyes', 9, 1, 6.99, 10, 4.50, 200, 30, 0, '0.4%', 'Drops', '0.4%', 0, 1, 4.2, 14, 72),
('Visine Eye Drops', 'Tetrahydrozoline', 'Redness relief eye drops', 9, 3, 5.99, 8, 4.00, 180, 25, 0, '0.05%', 'Drops', '0.05%', 0, 1, 4.0, 12, 65),

-- First Aid
('Band-Aid Adhesive Bandages', 'Adhesive Bandages', 'Sterile adhesive bandages for wound care', 10, 3, 4.99, 15, 3.00, 500, 75, 0, 'Assorted', 'Bandage', 'Various', 1, 1, 4.6, 45, 250),
('Hydrogen Peroxide 3%', 'Hydrogen Peroxide', 'Antiseptic for cleaning wounds', 10, 1, 3.99, 5, 2.50, 300, 40, 0, '3%', 'Solution', '3%', 0, 1, 4.4, 18, 125);

-- Insert System Settings
INSERT INTO [SystemSettings] ([SettingKey], [SettingValue], [Description], [Category], [IsEditable]) VALUES
('SiteName', 'MediEase Pharmacy', 'Name of the pharmacy system', 'General', 1),
('SiteEmail', '<EMAIL>', 'Primary contact email', 'General', 1),
('SitePhone', '******-MEDIEASE', 'Primary contact phone number', 'General', 1),
('SiteAddress', '123 Healthcare Blvd, Medical City, MC 12345', 'Physical address of pharmacy', 'General', 1),
('Currency', 'USD', 'Default currency for pricing', 'General', 1),
('CurrencySymbol', '$', 'Currency symbol to display', 'General', 1),
('TaxRate', '8.5', 'Default tax rate percentage', 'Financial', 1),
('ShippingCost', '5.99', 'Standard shipping cost', 'Financial', 1),
('FreeShippingThreshold', '50.00', 'Minimum order for free shipping', 'Financial', 1),
('ExpressShippingCost', '12.99', 'Express shipping cost', 'Financial', 1),
('LoyaltyPointsRate', '1', 'Points earned per dollar spent', 'Loyalty', 1),
('LoyaltyRedemptionRate', '100', 'Points needed for $1 discount', 'Loyalty', 1),
('LoyaltyPointsExpiry', '365', 'Days before loyalty points expire', 'Loyalty', 1),
('MaxFileUploadSize', '5242880', 'Maximum file upload size in bytes (5MB)', 'System', 1),
('AllowedFileTypes', '.pdf,.jpg,.jpeg,.png', 'Allowed file types for uploads', 'System', 1),
('SessionTimeout', '30', 'Session timeout in minutes', 'Security', 1),
('PasswordMinLength', '8', 'Minimum password length', 'Security', 1),
('PasswordRequireSpecialChar', 'true', 'Require special characters in password', 'Security', 1),
('MaxLoginAttempts', '5', 'Maximum failed login attempts before lockout', 'Security', 1),
('AccountLockoutDuration', '30', 'Account lockout duration in minutes', 'Security', 1),
('EnableEmailNotifications', 'true', 'Enable email notifications', 'Notifications', 1),
('EnableSMSNotifications', 'false', 'Enable SMS notifications', 'Notifications', 1),
('EnablePushNotifications', 'true', 'Enable push notifications', 'Notifications', 1),
('AIEnabled', 'true', 'Enable AI features', 'AI', 1),
('ChatbotEnabled', 'true', 'Enable chatbot functionality', 'AI', 1),
('AIModel', 'deepseek/deepseek-r1-0528-qwen3-8b:free', 'AI model to use', 'AI', 1),
('OpenRouterApiKey', '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', 'OpenRouter API key', 'AI', 0),
('PrescriptionVerificationRequired', 'true', 'Require prescription verification', 'Medical', 1),
('PrescriptionExpiryDays', '365', 'Days before prescription expires', 'Medical', 1),
('AutoApproveReviews', 'false', 'Automatically approve product reviews', 'Reviews', 1),
('MinReviewLength', '10', 'Minimum review text length', 'Reviews', 1),
('MaxReviewLength', '2000', 'Maximum review text length', 'Reviews', 1),
('MaintenanceMode', 'false', 'Enable maintenance mode', 'System', 1),
('MaintenanceMessage', 'System is under maintenance. Please try again later.', 'Maintenance mode message', 'System', 1),
('BackupFrequency', 'Daily', 'Database backup frequency', 'System', 1),
('LogRetentionDays', '90', 'Days to retain log files', 'System', 1),
('EnableAuditLogging', 'true', 'Enable audit logging', 'System', 1),
('BusinessHours', 'Mon-Fri: 8AM-8PM, Sat: 9AM-6PM, Sun: 10AM-4PM', 'Business operating hours', 'General', 1),
('EmergencyContact', '******-EMERGENCY', 'Emergency contact number', 'General', 1),
('PharmacyLicense', 'PH-12345-NY', 'Pharmacy license number', 'Legal', 1),
('DEANumber', '*********', 'DEA registration number', 'Legal', 1);

-- Insert Sample Family Profiles
INSERT INTO [FamilyProfiles] ([UserId], [MemberName], [Relationship], [DateOfBirth], [Gender], [BloodGroup], [Allergies], [MedicalConditions], [EmergencyContact], [EmergencyPhone], [IsActive]) VALUES
(3, 'Jane Customer', 'Self', '1985-06-15', 'Female', 'O+', 'Penicillin, Shellfish', 'Hypertension, Diabetes Type 2', 'John Customer', '******-0010', 1),
(3, 'John Customer', 'Spouse', '1983-03-22', 'Male', 'A+', 'None', 'None', 'Jane Customer', '******-0003', 1),
(3, 'Emily Customer', 'Daughter', '2010-09-08', 'Female', 'O+', 'Peanuts', 'Asthma', 'Jane Customer', '******-0003', 1),
(5, 'Sarah Johnson', 'Self', '1990-12-03', 'Female', 'B+', 'Latex', 'Migraine', 'Mike Johnson', '******-0015', 1),
(5, 'Mike Johnson', 'Spouse', '1988-07-18', 'Male', 'B+', 'None', 'High Cholesterol', 'Sarah Johnson', '******-0005', 1);

-- Insert Sample Health Reminders
INSERT INTO [HealthReminders] ([UserId], [FamilyProfileId], [Title], [Description], [ReminderType], [NextReminderDate], [Frequency], [FrequencyValue], [IsActive]) VALUES
(3, 1, 'Blood Pressure Medication', 'Take Lisinopril 10mg every morning', 'Medication', DATEADD(HOUR, 8, CAST(GETDATE() AS DATE)), 'Daily', 1, 1),
(3, 1, 'Diabetes Check', 'Check blood sugar levels', 'Checkup', DATEADD(HOUR, 7, CAST(GETDATE() AS DATE)), 'Daily', 1, 1),
(3, 3, 'Asthma Inhaler', 'Use rescue inhaler if needed', 'Medication', DATEADD(HOUR, 12, CAST(GETDATE() AS DATE)), 'As Needed', 1, 1),
(5, 4, 'Migraine Prevention', 'Take preventive medication', 'Medication', DATEADD(HOUR, 20, CAST(GETDATE() AS DATE)), 'Daily', 1, 1),
(5, 5, 'Cholesterol Medication', 'Take Atorvastatin before bed', 'Medication', DATEADD(HOUR, 22, CAST(GETDATE() AS DATE)), 'Daily', 1, 1);

-- Insert Sample Offers
INSERT INTO [Offers] ([Code], [Title], [Description], [OfferType], [DiscountValue], [MinOrderAmount], [MaxDiscountAmount], [UsageLimit], [UserUsageLimit], [StartDate], [EndDate], [IsActive], [ApplicableCategories], [CreatedBy]) VALUES
('WELCOME10', 'Welcome Discount', 'Get 10% off your first order', 'PERCENTAGE', 10.00, 25.00, 20.00, 1000, 1, GETDATE(), DATEADD(MONTH, 3, GETDATE()), 1, '1,3,4,5,8,9,10', 1),
('HEALTH20', 'Health & Wellness', '20% off vitamins and supplements', 'PERCENTAGE', 20.00, 50.00, 50.00, 500, 2, GETDATE(), DATEADD(MONTH, 2, GETDATE()), 1, '3', 1),
('FREESHIP', 'Free Shipping', 'Free shipping on orders over $30', 'FREE_SHIPPING', 0.00, 30.00, NULL, NULL, NULL, GETDATE(), DATEADD(MONTH, 6, GETDATE()), 1, NULL, 1),
('SAVE5', 'Save $5', '$5 off orders over $40', 'FIXED_AMOUNT', 5.00, 40.00, 5.00, 200, 1, GETDATE(), DATEADD(MONTH, 1, GETDATE()), 1, NULL, 1),
('LOYALTY15', 'Loyalty Reward', '15% off for loyal customers', 'PERCENTAGE', 15.00, 75.00, 25.00, 100, 1, GETDATE(), DATEADD(MONTH, 4, GETDATE()), 1, NULL, 1);

-- Insert Sample Medicine Reviews
INSERT INTO [MedicineReviews] ([MedicineId], [UserId], [Rating], [Title], [ReviewText], [IsVerifiedPurchase], [IsApproved]) VALUES
(1, 3, 5, 'Excellent pain relief', 'Works great for headaches and muscle pain. Fast acting and long lasting.', 1, 1),
(1, 5, 4, 'Good product', 'Effective for pain relief, though takes a bit longer to work than expected.', 1, 1),
(8, 3, 5, 'Great vitamin supplement', 'High quality vitamin D3. Easy to swallow and good value for money.', 1, 1),
(8, 4, 5, 'Recommended by doctor', 'My doctor recommended this brand. Excellent quality and effectiveness.', 1, 1),
(22, 5, 4, 'Helpful for dry eyes', 'Good relief for dry eyes, especially during long computer work sessions.', 1, 1),
(2, 4, 4, 'Fast acting', 'Liquid gels work faster than regular tablets. Good for quick pain relief.', 1, 1),
(9, 3, 5, 'Complete nutrition', 'Great multivitamin with all essential nutrients. Feel more energetic.', 1, 1);

-- Insert Sample Notifications
INSERT INTO [Notifications] ([UserId], [Title], [Message], [NotificationType], [Priority], [IsRead]) VALUES
(3, 'Welcome to MediEase!', 'Thank you for joining MediEase. Explore our wide range of medicines and health products.', 'WELCOME', 'Normal', 0),
(3, 'Order Confirmation', 'Your order #ORD20241201001 has been confirmed and is being processed.', 'ORDER_UPDATE', 'High', 1),
(3, 'Prescription Reminder', 'Your prescription for Lisinopril is due for refill in 3 days.', 'REMINDER', 'High', 0),
(5, 'Special Offer', 'Get 20% off on all vitamins and supplements. Use code HEALTH20.', 'PROMOTION', 'Normal', 0),
(2, 'New Prescription', 'A new prescription has been uploaded and requires verification.', 'PRESCRIPTION_UPLOAD', 'High', 0);

-- Insert Sample Stock Movements
INSERT INTO [StockMovements] ([MedicineId], [MovementType], [Quantity], [PreviousStock], [NewStock], [UnitCost], [Reference], [Notes], [CreatedBy]) VALUES
(1, 'IN', 100, 50, 150, 8.50, 'PO-2024-001', 'Initial stock replenishment', 1),
(2, 'IN', 150, 50, 200, 10.20, 'PO-2024-002', 'Bulk purchase from supplier', 1),
(8, 'IN', 200, 100, 300, 12.00, 'PO-2024-003', 'Vitamin D3 restock', 1),
(1, 'OUT', 5, 150, 145, 8.50, 'ORD20241201001', 'Customer order fulfillment', 2),
(8, 'OUT', 10, 300, 290, 12.00, 'ORD20241201002', 'Customer order fulfillment', 2);

-- Create sequences for auto-generating numbers
CREATE SEQUENCE OrderNumberSequence
    START WITH 1001
    INCREMENT BY 1
    MINVALUE 1001
    MAXVALUE 9999
    CYCLE;

CREATE SEQUENCE PrescriptionNumberSequence
    START WITH 1001
    INCREMENT BY 1
    MINVALUE 1001
    MAXVALUE 9999
    CYCLE;
