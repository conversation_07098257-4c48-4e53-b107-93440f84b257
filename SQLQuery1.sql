﻿-- =================================================================
-- MediEase: Smart Pharmacy Management System
-- Database Schema for Microsoft SQL Server (Plain Text Passwords)
-- =================================================================
-- SECURITY WARNING: Storing passwords in plain text is highly insecure and not recommended.
-- This schema is for educational/development purposes only where security is not a concern.
-- =================================================================

-- Drop existing tables in reverse order of creation to avoid foreign key constraints
IF OBJECT_ID('AuditTrail', 'U') IS NOT NULL DROP TABLE AuditTrail;
IF OBJECT_ID('SystemSettings', 'U') IS NOT NULL DROP TABLE SystemSettings;
IF OBJECT_ID('ChatLogs', 'U') IS NOT NULL DROP TABLE ChatLogs;
IF OBJECT_ID('CMSContent', 'U') IS NOT NULL DROP TABLE CMSContent;
IF OBJECT_ID('Feedback', 'U') IS NOT NULL DROP TABLE Feedback;
IF OBJECT_ID('ProductReviews', 'U') IS NOT NULL DROP TABLE ProductReviews;
IF OBJECT_ID('Reminders', 'U') IS NOT NULL DROP TABLE Reminders;
IF OBJECT_ID('AutoRefills', 'U') IS NOT NULL DROP TABLE AutoRefills;
IF OBJECT_ID('LoyaltyPoints', 'U') IS NOT NULL DROP TABLE LoyaltyPoints;
IF OBJECT_ID('Discounts', 'U') IS NOT NULL DROP TABLE Discounts;
IF OBJECT_ID('Invoices', 'U') IS NOT NULL DROP TABLE Invoices;
IF OBJECT_ID('Payments', 'U') IS NOT NULL DROP TABLE Payments;
IF OBJECT_ID('Prescriptions', 'U') IS NOT NULL DROP TABLE Prescriptions;
IF OBJECT_ID('OrderItems', 'U') IS NOT NULL DROP TABLE OrderItems;
IF OBJECT_ID('Orders', 'U') IS NOT NULL DROP TABLE Orders;
IF OBJECT_ID('OrderStatus', 'U') IS NOT NULL DROP TABLE OrderStatus;
IF OBJECT_ID('Pricing', 'U') IS NOT NULL DROP TABLE Pricing;
IF OBJECT_ID('Inventory', 'U') IS NOT NULL DROP TABLE Inventory;
IF OBJECT_ID('Products', 'U') IS NOT NULL DROP TABLE Products;
IF OBJECT_ID('Categories', 'U') IS NOT NULL DROP TABLE Categories;
IF OBJECT_ID('Brands', 'U') IS NOT NULL DROP TABLE Brands;
IF OBJECT_ID('FamilyProfiles', 'U') IS NOT NULL DROP TABLE FamilyProfiles;
IF OBJECT_ID('UserProfiles', 'U') IS NOT NULL DROP TABLE UserProfiles;
IF OBJECT_ID('Users', 'U') IS NOT NULL DROP TABLE Users;
IF OBJECT_ID('Roles', 'U') IS NOT NULL DROP TABLE Roles;

-- =================================================================
-- 1. USER AND ROLE MANAGEMENT TABLES
-- =================================================================

-- Roles Table: Defines the roles within the system (e.g., Customer, Pharmacist, Admin)
CREATE TABLE Roles (
    RoleID INT PRIMARY KEY IDENTITY(1,1),
    RoleName NVARCHAR(50) NOT NULL UNIQUE
);

-- Users Table: Stores core login and identity information for all users
CREATE TABLE Users (
    UserID INT PRIMARY KEY IDENTITY(1,1),
    Email NVARCHAR(255) NOT NULL UNIQUE,
    Password NVARCHAR(255) NOT NULL, -- Storing password in plain text
    RoleID INT NOT NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
    CONSTRAINT FK_Users_Roles FOREIGN KEY (RoleID) REFERENCES Roles(RoleID)
);

-- UserProfiles Table: Stores additional personal information for users, mainly customers
CREATE TABLE UserProfiles (
    ProfileID INT PRIMARY KEY IDENTITY(1,1),
    UserID INT NOT NULL UNIQUE,
    FirstName NVARCHAR(100),
    LastName NVARCHAR(100),
    PhoneNumber NVARCHAR(20),
    Address NVARCHAR(500),
    DateOfBirth DATE,
    CONSTRAINT FK_UserProfiles_Users FOREIGN KEY (UserID) REFERENCES Users(UserID) ON DELETE CASCADE
);

-- FamilyProfiles Table: Allows customers to manage profiles for family members under one account
CREATE TABLE FamilyProfiles (
    FamilyProfileID INT PRIMARY KEY IDENTITY(1,1),
    PrimaryUserID INT NOT NULL, -- The main customer account
    PatientName NVARCHAR(200) NOT NULL,
    Relationship NVARCHAR(50),
    DateOfBirth DATE,
    CONSTRAINT FK_FamilyProfiles_Users FOREIGN KEY (PrimaryUserID) REFERENCES Users(UserID)
);

-- =================================================================
-- 2. PRODUCT AND INVENTORY MANAGEMENT TABLES
-- =================================================================

-- Categories Table: For organizing medicines into categories (e.g., Pain Relief, Vitamins)
CREATE TABLE Categories (
    CategoryID INT PRIMARY KEY IDENTITY(1,1),
    CategoryName NVARCHAR(100) NOT NULL UNIQUE,
    Description NVARCHAR(500)
);

-- Brands Table: For medicine manufacturers or brands (e.g., Pfizer, Cipla)
CREATE TABLE Brands (
    BrandID INT PRIMARY KEY IDENTITY(1,1),
    BrandName NVARCHAR(100) NOT NULL UNIQUE
);

-- Products Table: The central table for all medicines and other products
CREATE TABLE Products (
    ProductID INT PRIMARY KEY IDENTITY(1,1),
    Name NVARCHAR(255) NOT NULL,
    Description NVARCHAR(MAX),
    CategoryID INT,
    BrandID INT,
    Form NVARCHAR(50), -- e.g., 'Tablet', 'Syrup', 'Injection'
    Strength NVARCHAR(100), -- e.g., '500mg'
    IsPrescriptionRequired BIT NOT NULL DEFAULT 1,
    IsActive BIT NOT NULL DEFAULT 1, -- Can be set to 0 if product is discontinued
    ApprovedByAdminID INT, -- Admin who approved the listing
    CONSTRAINT FK_Products_Categories FOREIGN KEY (CategoryID) REFERENCES Categories(CategoryID),
    CONSTRAINT FK_Products_Brands FOREIGN KEY (BrandID) REFERENCES Brands(BrandID),
    CONSTRAINT FK_Products_Admin FOREIGN KEY (ApprovedByAdminID) REFERENCES Users(UserID)
);

-- Inventory Table: Tracks stock levels, batch numbers, and expiry dates for each product
CREATE TABLE Inventory (
    InventoryID INT PRIMARY KEY IDENTITY(1,1),
    ProductID INT NOT NULL,
    BatchNumber NVARCHAR(100) NOT NULL,
    ExpiryDate DATE NOT NULL,
    QuantityInStock INT NOT NULL CHECK (QuantityInStock >= 0),
    ReorderLevel INT NOT NULL DEFAULT 10,
    LastUpdatedByUserID INT, -- Pharmacist or Admin who updated the stock
    LastUpdatedAt DATETIME DEFAULT GETDATE(),
    CONSTRAINT FK_Inventory_Products FOREIGN KEY (ProductID) REFERENCES Products(ProductID),
    CONSTRAINT FK_Inventory_Users FOREIGN KEY (LastUpdatedByUserID) REFERENCES Users(UserID)
);

-- Pricing Table: Manages the price history of products
CREATE TABLE Pricing (
    PriceID INT PRIMARY KEY IDENTITY(1,1),
    ProductID INT NOT NULL,
    Price DECIMAL(10, 2) NOT NULL CHECK (Price > 0),
    EffectiveDate DATETIME NOT NULL DEFAULT GETDATE(),
    SetByUserID INT, -- Pharmacist or Admin who set the price
    CONSTRAINT FK_Pricing_Products FOREIGN KEY (ProductID) REFERENCES Products(ProductID),
    CONSTRAINT FK_Pricing_Users FOREIGN KEY (SetByUserID) REFERENCES Users(UserID)
);


-- =================================================================
-- 3. ORDER AND SALES MANAGEMENT TABLES
-- =================================================================

-- OrderStatus Table: A lookup table for the various states an order can be in
CREATE TABLE OrderStatus (
    StatusID INT PRIMARY KEY IDENTITY(1,1),
    StatusName NVARCHAR(50) NOT NULL UNIQUE -- e.g., 'Pending', 'Under Review', 'Processing', 'Shipped', 'Delivered', 'Cancelled', 'Rejected'
);

-- Orders Table: Header information for each customer order
CREATE TABLE Orders (
    OrderID INT PRIMARY KEY IDENTITY(1,1),
    CustomerID INT NOT NULL,
    OrderDate DATETIME NOT NULL DEFAULT GETDATE(),
    TotalAmount DECIMAL(18, 2) NOT NULL,
    StatusID INT NOT NULL,
    ShippingAddress NVARCHAR(500) NOT NULL,
    CONSTRAINT FK_Orders_Users FOREIGN KEY (CustomerID) REFERENCES Users(UserID),
    CONSTRAINT FK_Orders_OrderStatus FOREIGN KEY (StatusID) REFERENCES OrderStatus(StatusID)
);

-- OrderItems Table: Line items for each order
CREATE TABLE OrderItems (
    OrderItemID INT PRIMARY KEY IDENTITY(1,1),
    OrderID INT NOT NULL,
    ProductID INT NOT NULL,
    Quantity INT NOT NULL CHECK (Quantity > 0),
    PricePerUnit DECIMAL(10, 2) NOT NULL,
    CONSTRAINT FK_OrderItems_Orders FOREIGN KEY (OrderID) REFERENCES Orders(OrderID) ON DELETE CASCADE,
    CONSTRAINT FK_OrderItems_Products FOREIGN KEY (ProductID) REFERENCES Products(ProductID)
);

-- Prescriptions Table: Stores information about uploaded prescriptions
CREATE TABLE Prescriptions (
    PrescriptionID INT PRIMARY KEY IDENTITY(1,1),
    OrderID INT, -- Can be nullable if uploaded before checkout
    CustomerID INT NOT NULL,
    FilePath NVARCHAR(1000) NOT NULL, -- Path to the stored PDF or image file
    UploadDate DATETIME NOT NULL DEFAULT GETDATE(),
    Status NVARCHAR(50) NOT NULL DEFAULT 'Pending Review', -- 'Pending Review', 'Approved', 'Rejected'
    ValidatedByPharmacistID INT,
    CONSTRAINT FK_Prescriptions_Orders FOREIGN KEY (OrderID) REFERENCES Orders(OrderID),
    CONSTRAINT FK_Prescriptions_Customers FOREIGN KEY (CustomerID) REFERENCES Users(UserID),
    CONSTRAINT FK_Prescriptions_Pharmacists FOREIGN KEY (ValidatedByPharmacistID) REFERENCES Users(UserID)
);

-- Payments Table: Records payment transactions for orders
CREATE TABLE Payments (
    PaymentID INT PRIMARY KEY IDENTITY(1,1),
    OrderID INT NOT NULL,
    PaymentDate DATETIME NOT NULL DEFAULT GETDATE(),
    Amount DECIMAL(18, 2) NOT NULL,
    PaymentMethod NVARCHAR(50), -- e.g., 'Credit Card', 'PayPal'
    TransactionID NVARCHAR(255),
    PaymentStatus NVARCHAR(50) NOT NULL, -- e.g., 'Success', 'Failed', 'Pending'
    CONSTRAINT FK_Payments_Orders FOREIGN KEY (OrderID) REFERENCES Orders(OrderID)
);

-- Invoices Table: Stores generated invoice details
CREATE TABLE Invoices (
    InvoiceID INT PRIMARY KEY IDENTITY(1,1),
    OrderID INT NOT NULL,
    InvoiceDate DATETIME NOT NULL DEFAULT GETDATE(),
    QRCodeData NVARCHAR(MAX), -- Data to be encoded into a QR code for labels
    CONSTRAINT FK_Invoices_Orders FOREIGN KEY (OrderID) REFERENCES Orders(OrderID)
);

-- =================================================================
-- 4. CUSTOMER ENGAGEMENT AND SUPPORT TABLES
-- =================================================================

-- Discounts Table: Manages promotional codes and offers
CREATE TABLE Discounts (
    DiscountID INT PRIMARY KEY IDENTITY(1,1),
    DiscountCode NVARCHAR(50) UNIQUE,
    Description NVARCHAR(255),
    DiscountPercentage DECIMAL(5, 2),
    StartDate DATETIME,
    EndDate DATETIME,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedByAdminID INT,
    CONSTRAINT FK_Discounts_Users FOREIGN KEY (CreatedByAdminID) REFERENCES Users(UserID)
);

-- LoyaltyPoints Table: Tracks loyalty points for customers
CREATE TABLE LoyaltyPoints (
    LoyaltyPointsID INT PRIMARY KEY IDENTITY(1,1),
    CustomerID INT NOT NULL,
    Points INT NOT NULL,
    TransactionType NVARCHAR(50) NOT NULL, -- 'Earned', 'Redeemed'
    OrderID INT, -- Link to the order where points were earned/redeemed
    TransactionDate DATETIME NOT NULL DEFAULT GETDATE(),
    CONSTRAINT FK_LoyaltyPoints_Users FOREIGN KEY (CustomerID) REFERENCES Users(UserID),
    CONSTRAINT FK_LoyaltyPoints_Orders FOREIGN KEY (OrderID) REFERENCES Orders(OrderID)
);

-- AutoRefills Table: Manages automatic refill subscriptions
CREATE TABLE AutoRefills (
    AutoRefillID INT PRIMARY KEY IDENTITY(1,1),
    CustomerID INT NOT NULL,
    ProductID INT NOT NULL,
    FrequencyInDays INT NOT NULL CHECK (FrequencyInDays > 0),
    NextRefillDate DATE NOT NULL,
    ShippingAddress NVARCHAR(500),
    IsActive BIT NOT NULL DEFAULT 1,
    CONSTRAINT FK_AutoRefills_Users FOREIGN KEY (CustomerID) REFERENCES Users(UserID),
    CONSTRAINT FK_AutoRefills_Products FOREIGN KEY (ProductID) REFERENCES Products(ProductID)
);

-- Reminders Table: For medication intake reminders
CREATE TABLE Reminders (
    ReminderID INT PRIMARY KEY IDENTITY(1,1),
    CustomerID INT NOT NULL,
    ProductID INT NOT NULL,
    ReminderTime TIME NOT NULL,
    Frequency NVARCHAR(100), -- e.g., 'Daily', 'Twice a day'
    IsActive BIT NOT NULL DEFAULT 1,
    CONSTRAINT FK_Reminders_Users FOREIGN KEY (CustomerID) REFERENCES Users(UserID),
    CONSTRAINT FK_Reminders_Products FOREIGN KEY (ProductID) REFERENCES Products(ProductID)
);

-- ProductReviews Table: Stores customer ratings and reviews for products
CREATE TABLE ProductReviews (
    ReviewID INT PRIMARY KEY IDENTITY(1,1),
    ProductID INT NOT NULL,
    CustomerID INT NOT NULL,
    Rating INT NOT NULL CHECK (Rating BETWEEN 1 AND 5),
    Comment NVARCHAR(MAX),
    ReviewDate DATETIME NOT NULL DEFAULT GETDATE(),
    CONSTRAINT FK_ProductReviews_Products FOREIGN KEY (ProductID) REFERENCES Products(ProductID),
    CONSTRAINT FK_ProductReviews_Users FOREIGN KEY (CustomerID) REFERENCES Users(UserID)
);

-- Feedback Table: For customer complaints and feedback
CREATE TABLE Feedback (
    FeedbackID INT PRIMARY KEY IDENTITY(1,1),
    CustomerID INT, -- Nullable for guest feedback
    Subject NVARCHAR(255) NOT NULL,
    Message NVARCHAR(MAX) NOT NULL,
    SubmissionDate DATETIME NOT NULL DEFAULT GETDATE(),
    Status NVARCHAR(50) DEFAULT 'Open', -- 'Open', 'In Progress', 'Resolved'
    RespondedByPharmacistID INT,
    CONSTRAINT FK_Feedback_Customers FOREIGN KEY (CustomerID) REFERENCES Users(UserID),
    CONSTRAINT FK_Feedback_Pharmacists FOREIGN KEY (RespondedByPharmacistID) REFERENCES Users(UserID)
);


-- =================================================================
-- 5. SYSTEM, CONTENT, AND LOGGING TABLES
-- =================================================================

-- CMSContent Table: For managing content of static pages like FAQ, Blog, Home
CREATE TABLE CMSContent (
    ContentID INT PRIMARY KEY IDENTITY(1,1),
    PageName NVARCHAR(100) NOT NULL, -- e.g., 'HomePage', 'FAQ', 'Blog'
    ContentTitle NVARCHAR(255),
    ContentBody NVARCHAR(MAX),
    LastUpdatedByAdminID INT,
    LastUpdatedDate DATETIME DEFAULT GETDATE(),
    CONSTRAINT FK_CMSContent_Users FOREIGN KEY (LastUpdatedByAdminID) REFERENCES Users(UserID)
);

-- ChatLogs Table: Logs conversations with the AI chatbot for oversight
CREATE TABLE ChatLogs (
    LogID INT PRIMARY KEY IDENTITY(1,1),
    SessionID NVARCHAR(255), -- To group messages in a single conversation
    UserID INT, -- Nullable for guest users
    Message NVARCHAR(MAX),
    Response NVARCHAR(MAX),
    Timestamp DATETIME NOT NULL DEFAULT GETDATE(),
    IsEscalated BIT DEFAULT 0, -- Flag if the chat was escalated to a human
    CONSTRAINT FK_ChatLogs_Users FOREIGN KEY (UserID) REFERENCES Users(UserID)
);

-- SystemSettings Table: A key-value store for global application settings
CREATE TABLE SystemSettings (
    SettingID INT PRIMARY KEY IDENTITY(1,1),
    SettingName NVARCHAR(100) NOT NULL UNIQUE,
    SettingValue NVARCHAR(MAX) NOT NULL
);

-- AuditTrail Table: Tracks significant changes made by users for security and accountability
CREATE TABLE AuditTrail (
    AuditID BIGINT PRIMARY KEY IDENTITY(1,1),
    UserID INT,
    ActionType NVARCHAR(100) NOT NULL, -- e.g., 'UPDATE_PRODUCT', 'DELETE_USER', 'APPROVE_PRESCRIPTION'
    TableName NVARCHAR(128),
    RecordID INT, -- The primary key of the record that was changed
    OldValues NVARCHAR(MAX), -- Could be JSON or XML
    NewValues NVARCHAR(MAX), -- Could be JSON or XML
    Timestamp DATETIME NOT NULL DEFAULT GETDATE(),
    CONSTRAINT FK_AuditTrail_Users FOREIGN KEY (UserID) REFERENCES Users(UserID)
);

-- =================================================================
-- 6. INITIAL DATA INSERTION (SEEDING)
-- =================================================================

-- Insert basic roles
INSERT INTO Roles (RoleName) VALUES ('Admin'), ('Pharmacist'), ('Customer');

-- Insert order statuses
INSERT INTO OrderStatus (StatusName) VALUES ('Pending'), ('Under Review'), ('Processing'), ('Shipped'), ('Delivered'), ('Cancelled'), ('Rejected');

-- Insert a default admin user with a plain text password
INSERT INTO Users (Email, Password, RoleID) VALUES ('<EMAIL>', 'AdminPassword123', (SELECT RoleID FROM Roles WHERE RoleName = 'Admin'));

-- Insert a default system setting
INSERT INTO SystemSettings (SettingName, SettingValue) VALUES ('SessionTimeoutMinutes', '30');


PRINT 'MediEase Database Schema (Plain Text Passwords) and initial data created successfully.';
