<%@ Page Language="C#" AutoEventWireup="true" CodeFile="UserManagement.aspx.cs" Inherits="Admin_UserManagement" %>

<!DOCTYPE html>
<html lang="en">
<head runat="server">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management - MediEase Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <style>
        body { font-family: 'Inter', sans-serif; }
        .sidebar-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            transition: background-color 0.2s, color 0.2s;
        }
        .sidebar-link:hover, .sidebar-link.active {
            background-color: #0d9488; /* teal-700 */
            color: white;
        }
        .sidebar-link i {
            width: 1.25rem;
            margin-right: 0.75rem;
        }
    </style>
</head>
<body class="bg-gray-100">
    <form id="form1" runat="server">
        <div class="flex h-screen bg-gray-100">
            <!-- Sidebar -->
            <aside class="w-64 bg-teal-600 text-white flex flex-col p-4">
                <div class="flex items-center space-x-2 pb-4 border-b border-teal-500">
                    <i class="fas fa-pills text-3xl"></i>
                    <span class="text-2xl font-bold">MediEase</span>
                </div>
                <nav class="mt-6 flex-grow">
                    <a href="Dashboard.aspx" class="sidebar-link">
                        <i class="fas fa-tachometer-alt fa-fw"></i>
                        <span>Dashboard</span>
                    </a>
                    <a href="UserManagement.aspx" class="sidebar-link active">
                        <i class="fas fa-users-cog fa-fw"></i>
                        <span>User Management</span>
                    </a>
                    <a href="ProductManagement.aspx" class="sidebar-link">
                        <i class="fas fa-capsules fa-fw"></i>
                        <span>Products</span>
                    </a>
                     <a href="Reports.aspx" class="sidebar-link">
                        <i class="fas fa-chart-line fa-fw"></i>
                        <span>Reports</span>
                    </a>
                    <a href="SystemSettings.aspx" class="sidebar-link">
                        <i class="fas fa-cogs fa-fw"></i>
                        <span>System Settings</span>
                    </a>
                     <a href="AuditLog.aspx" class="sidebar-link">
                        <i class="fas fa-clipboard-list fa-fw"></i>
                        <span>Audit Log</span>
                    </a>
                </nav>
                <div class="pt-4 border-t border-teal-500">
                    <asp:LinkButton ID="btnLogout" runat="server" OnClick="btnLogout_Click" CssClass="sidebar-link w-full">
                        <i class="fas fa-sign-out-alt fa-fw"></i>
                        <span>Logout</span>
                    </asp:LinkButton>
                </div>
            </aside>

            <!-- Main Content -->
            <main class="flex-1 flex flex-col">
                <header class="bg-white shadow-sm p-4">
                    <h1 class="text-2xl font-bold text-gray-800">User Management</h1>
                </header>

                <div class="flex-1 p-6 overflow-y-auto">
                    <!-- Search and Filter Controls -->
                    <div class="bg-white p-4 rounded-lg shadow mb-6">
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Search by Name/Email</label>
                                <asp:TextBox ID="txtSearch" runat="server" CssClass="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teal-500 focus:border-teal-500"></asp:TextBox>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Filter by Role</label>
                                <asp:DropDownList ID="ddlRoleFilter" runat="server" CssClass="mt-1 block w-full px-3 py-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-teal-500 focus:border-teal-500">
                                    <asp:ListItem Value="" Text="All Roles"></asp:ListItem>
                                    <asp:ListItem Value="Customer" Text="Customer"></asp:ListItem>
                                    <asp:ListItem Value="Pharmacist" Text="Pharmacist"></asp:ListItem>
                                </asp:DropDownList>
                            </div>
                             <div>
                                <label class="block text-sm font-medium text-gray-700">Filter by Status</label>
                                <asp:DropDownList ID="ddlStatusFilter" runat="server" CssClass="mt-1 block w-full px-3 py-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-teal-500 focus:border-teal-500">
                                    <asp:ListItem Value="" Text="All Statuses"></asp:ListItem>
                                    <asp:ListItem Value="1" Text="Active"></asp:ListItem>
                                    <asp:ListItem Value="0" Text="Inactive"></asp:ListItem>
                                </asp:DropDownList>
                            </div>
                            <div class="flex space-x-2">
                                <asp:Button ID="btnSearch" runat="server" Text="Search" OnClick="btnSearch_Click" CssClass="w-full justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500" />
                                <asp:Button ID="btnClear" runat="server" Text="Clear" OnClick="btnClear_Click" CssClass="w-full justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500" />
                            </div>
                        </div>
                    </div>
                    
                    <!-- Server Message -->
                    <asp:Literal ID="litMessage" runat="server"></asp:Literal>

                    <!-- Users Grid -->
                    <div class="bg-white p-6 rounded-lg shadow overflow-x-auto">
                         <asp:GridView ID="gvUsers" runat="server" AutoGenerateColumns="False"
                             OnRowCommand="gvUsers_RowCommand" OnRowDataBound="gvUsers_RowDataBound"
                             DataKeyNames="UserId,IsActive"
                             CssClass="min-w-full divide-y divide-gray-200"
                             HeaderStyle-CssClass="bg-gray-50" PagerStyle-CssClass="bg-white p-4"
                             AllowPaging="True" PageSize="10" OnPageIndexChanging="gvUsers_PageIndexChanging">
                             <Columns>
                                 <asp:BoundField DataField="UserId" HeaderText="ID" HeaderStyle-CssClass="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" ItemStyle-CssClass="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900" />
                                 <asp:BoundField DataField="FirstName" HeaderText="First Name" HeaderStyle-CssClass="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" ItemStyle-CssClass="px-6 py-4 whitespace-nowrap text-sm text-gray-500" />
                                 <asp:BoundField DataField="LastName" HeaderText="Last Name" HeaderStyle-CssClass="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" ItemStyle-CssClass="px-6 py-4 whitespace-nowrap text-sm text-gray-500" />
                                 <asp:BoundField DataField="Email" HeaderText="Email" HeaderStyle-CssClass="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" ItemStyle-CssClass="px-6 py-4 whitespace-nowrap text-sm text-gray-500" />
                                 <asp:BoundField DataField="Role" HeaderText="Role" HeaderStyle-CssClass="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" ItemStyle-CssClass="px-6 py-4 whitespace-nowrap text-sm text-gray-500" />
                                 <asp:BoundField DataField="LastLoginDate" HeaderText="Last Login" DataFormatString="{0:MMM dd, yyyy HH:mm}" HeaderStyle-CssClass="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" ItemStyle-CssClass="px-6 py-4 whitespace-nowrap text-sm text-gray-500" />
                                 
                                 <asp:TemplateField HeaderText="Status" HeaderStyle-CssClass="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" ItemStyle-CssClass="px-6 py-4 whitespace-nowrap">
                                     <ItemTemplate>
                                         <span class='px-2 inline-flex text-xs leading-5 font-semibold rounded-full <%# (bool)Eval("IsActive") ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800" %>'>
                                             <%# (bool)Eval("IsActive") ? "Active" : "Inactive" %>
                                         </span>
                                     </ItemTemplate>
                                 </asp:TemplateField>

                                 <asp:TemplateField HeaderText="Actions" HeaderStyle-CssClass="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider" ItemStyle-CssClass="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                                     <ItemTemplate>
                                         <asp:LinkButton ID="btnToggleStatus" runat="server" CommandName="ToggleStatus" CommandArgument='<%# Eval("UserId") %>' ToolTip='<%# (bool)Eval("IsActive") ? "Deactivate User" : "Activate User" %>' CssClass="text-gray-500 hover:text-gray-700">
                                            <i class='<%# (bool)Eval("IsActive") ? "fas fa-toggle-on text-2xl text-green-500" : "fas fa-toggle-off text-2xl text-gray-400" %>'></i>
                                         </asp:LinkButton>
                                     </ItemTemplate>
                                 </asp:TemplateField>
                             </Columns>
                             <EmptyDataTemplate>
                                 <div class="text-center py-8">
                                     <p class="mt-2 text-gray-500">No users found matching your criteria.</p>
                                 </div>
                             </EmptyDataTemplate>
                         </asp:GridView>
                    </div>
                </div>
            </main>
        </div>
    </form>
</body>
</html>
