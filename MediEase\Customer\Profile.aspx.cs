﻿using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web.UI;

public partial class Customer_Profile : System.Web.UI.Page
{
    protected void Page_Load(object sender, EventArgs e)
    {
        // --- SECURITY CHECK ---
        if (Session["UserId"] == null || Session["UserRole"]?.ToString() != "Customer")
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        if (!IsPostBack)
        {
            LoadProfileData();
        }
    }

    private void LoadProfileData()
    {
        int userId = (int)Session["UserId"];
        string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
        using (SqlConnection con = new SqlConnection(connectionString))
        {
            string query = "SELECT * FROM Users WHERE UserId = @UserId";
            using (SqlCommand cmd = new SqlCommand(query, con))
            {
                cmd.Parameters.AddWithValue("@UserId", userId);
                try
                {
                    con.Open();
                    SqlDataReader reader = cmd.ExecuteReader();
                    if (reader.Read())
                    {
                        txtFirstName.Text = reader["FirstName"]?.ToString();
                        txtLastName.Text = reader["LastName"]?.ToString();
                        txtEmail.Text = reader["Email"]?.ToString();
                        txtPhoneNumber.Text = reader["PhoneNumber"]?.ToString();
                        txtAddress.Text = reader["Address"]?.ToString();
                        txtCity.Text = reader["City"]?.ToString();
                        txtState.Text = reader["State"]?.ToString();
                        txtPostalCode.Text = reader["PostalCode"]?.ToString();

                        if (reader["DateOfBirth"] != DBNull.Value)
                        {
                            txtDateOfBirth.Text = Convert.ToDateTime(reader["DateOfBirth"]).ToString("yyyy-MM-dd");
                        }
                        if (reader["Gender"] != DBNull.Value)
                        {
                            ddlGender.SelectedValue = reader["Gender"].ToString();
                        }
                    }
                }
                catch (Exception ex)
                {
                    ShowMessage("Error loading profile data: " + ex.Message, "error");
                }
            }
        }
    }

    protected void btnSaveChanges_Click(object sender, EventArgs e)
    {
        int userId = (int)Session["UserId"];
        string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;

        using (SqlConnection con = new SqlConnection(connectionString))
        {
            string query = @"UPDATE Users SET 
                                FirstName = @FirstName, LastName = @LastName, PhoneNumber = @PhoneNumber, DateOfBirth = @DateOfBirth,
                                Address = @Address, City = @City, State = @State, PostalCode = @PostalCode, Gender = @Gender,
                                ModifiedDate = @ModifiedDate
                             WHERE UserId = @UserId";
            using (SqlCommand cmd = new SqlCommand(query, con))
            {
                cmd.Parameters.AddWithValue("@UserId", userId);
                cmd.Parameters.AddWithValue("@FirstName", txtFirstName.Text.Trim());
                cmd.Parameters.AddWithValue("@LastName", txtLastName.Text.Trim());
                cmd.Parameters.AddWithValue("@PhoneNumber", string.IsNullOrWhiteSpace(txtPhoneNumber.Text) ? (object)DBNull.Value : txtPhoneNumber.Text.Trim());
                cmd.Parameters.AddWithValue("@DateOfBirth", string.IsNullOrWhiteSpace(txtDateOfBirth.Text) ? (object)DBNull.Value : Convert.ToDateTime(txtDateOfBirth.Text));
                cmd.Parameters.AddWithValue("@Address", string.IsNullOrWhiteSpace(txtAddress.Text) ? (object)DBNull.Value : txtAddress.Text.Trim());
                cmd.Parameters.AddWithValue("@City", string.IsNullOrWhiteSpace(txtCity.Text) ? (object)DBNull.Value : txtCity.Text.Trim());
                cmd.Parameters.AddWithValue("@State", string.IsNullOrWhiteSpace(txtState.Text) ? (object)DBNull.Value : txtState.Text.Trim());
                cmd.Parameters.AddWithValue("@PostalCode", string.IsNullOrWhiteSpace(txtPostalCode.Text) ? (object)DBNull.Value : txtPostalCode.Text.Trim());
                cmd.Parameters.AddWithValue("@Gender", string.IsNullOrEmpty(ddlGender.SelectedValue) ? (object)DBNull.Value : ddlGender.SelectedValue);
                cmd.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);

                try
                {
                    con.Open();
                    cmd.ExecuteNonQuery();
                    ShowMessage("Profile updated successfully!", "success");
                }
                catch (Exception ex)
                {
                    ShowMessage("Error updating profile: " + ex.Message, "error");
                }
            }
        }
    }

    protected void btnChangePassword_Click(object sender, EventArgs e)
    {
        if (!Page.IsValid) return;

        int userId = (int)Session["UserId"];
        string currentPassword = txtCurrentPassword.Text;
        string newPassword = txtNewPassword.Text;

        string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
        using (SqlConnection con = new SqlConnection(connectionString))
        {
            try
            {
                con.Open();
                // 1. Get the current stored password hash
                SqlCommand getHashCmd = new SqlCommand("SELECT PasswordHash FROM Users WHERE UserId = @UserId", con);
                getHashCmd.Parameters.AddWithValue("@UserId", userId);
                string storedHash = getHashCmd.ExecuteScalar()?.ToString();

                if (string.IsNullOrEmpty(storedHash))
                {
                    ShowMessage("Could not verify user. Please log out and back in.", "error");
                    return;
                }

                // 2. Verify the current password is correct
                if (BCrypt.Net.BCrypt.Verify(currentPassword, storedHash))
                {
                    // 3. Hash the new password and update the database
                    string newPasswordHash = BCrypt.Net.BCrypt.HashPassword(newPassword);
                    SqlCommand updateCmd = new SqlCommand("UPDATE Users SET PasswordHash = @PasswordHash, ModifiedDate = @ModifiedDate WHERE UserId = @UserId", con);
                    updateCmd.Parameters.AddWithValue("@PasswordHash", newPasswordHash);
                    updateCmd.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);
                    updateCmd.Parameters.AddWithValue("@UserId", userId);
                    updateCmd.ExecuteNonQuery();

                    ShowMessage("Password changed successfully.", "success");
                }
                else
                {
                    ShowMessage("The current password you entered is incorrect.", "error");
                }
            }
            catch (Exception ex)
            {
                ShowMessage("Error changing password: " + ex.Message, "error");
            }
        }
    }

    protected void btnLogout_Click(object sender, EventArgs e)
    {
        Session.Clear();
        Session.Abandon();
        Response.Redirect("~/Login.aspx");
    }

    private void ShowMessage(string message, string type)
    {
        string cssClass = (type == "success")
            ? "bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg relative mb-4"
            : "bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg relative mb-4";

        litMessage.Text = $"<div class='{cssClass}' role='alert'>{message}</div>";
    }
}
