﻿/*
 =======================================================================
 FILE: Dashboard.aspx.cs (Final Customer Version)
 =======================================================================
*/
using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace MediEase.Customer
{
    public partial class Dashboard : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (Session["UserID"] == null || Session["RoleName"]?.ToString() != "Customer")
            {
                Response.Redirect("~/Login.aspx?returnUrl=" + Server.UrlEncode(Request.Url.AbsolutePath));
                return;
            }

            if (!IsPostBack)
            {
                LoadCustomerName();
                LoadLoyaltyPoints();
                BindOrderHistory();
            }
        }

        private void LoadCustomerName()
        {
            string userId = Session["UserID"].ToString();
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "SELECT FirstName FROM UserProfiles WHERE UserID = @UserID";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@UserID", userId);
                    con.Open();
                    object result = cmd.ExecuteScalar();
                    CustomerName.Text = Server.HtmlEncode(result?.ToString() ?? "Customer");
                }
            }
        }

        private void LoadLoyaltyPoints()
        {
            int customerId = Convert.ToInt32(Session["UserID"]);
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "SELECT ISNULL(SUM(Points), 0) FROM LoyaltyPoints WHERE CustomerID = @CustomerID";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@CustomerID", customerId);
                    con.Open();
                    LoyaltyPointsLabel.Text = cmd.ExecuteScalar().ToString();
                }
            }
        }

        private void BindOrderHistory()
        {
            string userId = Session["UserID"].ToString();
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "SELECT o.OrderID, o.OrderDate, o.TotalAmount, os.StatusName FROM Orders o INNER JOIN OrderStatus os ON o.StatusID = os.StatusID WHERE o.CustomerID = @CustomerID ORDER BY o.OrderDate DESC;";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@CustomerID", userId);
                    con.Open();
                    SqlDataAdapter sda = new SqlDataAdapter(cmd);
                    DataTable dt = new DataTable();
                    sda.Fill(dt);
                    OrderHistoryGrid.DataSource = dt;
                    OrderHistoryGrid.DataBind();
                }
            }
        }

        protected void OrderHistoryGrid_PageIndexChanging(object sender, GridViewPageEventArgs e)
        {
            OrderHistoryGrid.PageIndex = e.NewPageIndex;
            BindOrderHistory();
        }

        public string GetStatusBadgeClass(string status)
        {
            switch (status.ToLower())
            {
                case "pending": case "under review": return "badge-warning";
                case "processing": return "badge-info";
                case "shipped": case "delivered": return "badge-success";
                case "cancelled": case "rejected": return "badge-danger";
                default: return "badge-secondary";
            }
        }
    }
}