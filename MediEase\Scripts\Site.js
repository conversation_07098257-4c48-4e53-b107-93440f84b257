// MediEase JavaScript Functions

// Global variables
let chatbotVisible = false;
let chatHistory = [];

// Document ready function
$(document).ready(function() {
    initializeApp();
});

// Initialize application
function initializeApp() {
    setupEventHandlers();
    initializeTooltips();
    initializePopovers();
    setupFormValidation();
    loadChatHistory();
}

// Setup event handlers
function setupEventHandlers() {
    // Search functionality
    $('#txtSearch').on('keypress', function(e) {
        if (e.which === 13) { // Enter key
            performSearch();
        }
    });

    // Cart functionality
    $('.add-to-cart').on('click', function(e) {
        e.preventDefault();
        const medicineId = $(this).data('medicine-id');
        const quantity = $(this).closest('.medicine-card').find('.quantity-input').val() || 1;
        addToCart(medicineId, quantity);
    });

    // Quantity controls
    $('.quantity-minus').on('click', function() {
        const input = $(this).siblings('.quantity-input');
        const currentValue = parseInt(input.val()) || 1;
        if (currentValue > 1) {
            input.val(currentValue - 1);
        }
    });

    $('.quantity-plus').on('click', function() {
        const input = $(this).siblings('.quantity-input');
        const currentValue = parseInt(input.val()) || 1;
        const maxValue = parseInt(input.attr('max')) || 999;
        if (currentValue < maxValue) {
            input.val(currentValue + 1);
        }
    });

    // File upload drag and drop
    setupFileUpload();
}

// Initialize Bootstrap tooltips
function initializeTooltips() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// Initialize Bootstrap popovers
function initializePopovers() {
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
}

// Setup form validation
function setupFormValidation() {
    // Custom validation for forms
    $('.needs-validation').on('submit', function(e) {
        if (!this.checkValidity()) {
            e.preventDefault();
            e.stopPropagation();
        }
        $(this).addClass('was-validated');
    });

    // Password strength indicator
    $('#password, #txtPassword').on('input', function() {
        checkPasswordStrength($(this).val(), $(this).attr('id') + '-strength');
    });

    // Email validation
    $('.email-input').on('blur', function() {
        validateEmail($(this));
    });

    // Phone number formatting
    $('.phone-input').on('input', function() {
        formatPhoneNumber($(this));
    });
}

// Search functionality
function performSearch() {
    const searchTerm = $('#txtSearch').val().trim();
    if (searchTerm) {
        window.location.href = '/Medicines.aspx?search=' + encodeURIComponent(searchTerm);
    }
}

// Add to cart functionality
function addToCart(medicineId, quantity) {
    showLoading();
    
    $.ajax({
        url: '/api/Cart/Add',
        method: 'POST',
        data: {
            medicineId: medicineId,
            quantity: quantity
        },
        success: function(response) {
            hideLoading();
            if (response.success) {
                updateCartCount(response.cartCount);
                showAlert('Medicine added to cart successfully!', 'success');
            } else {
                showAlert(response.message || 'Failed to add medicine to cart.', 'danger');
            }
        },
        error: function() {
            hideLoading();
            showAlert('An error occurred while adding to cart.', 'danger');
        }
    });
}

// Update cart count in navigation
function updateCartCount(count) {
    const cartBadge = $('#cartCount');
    cartBadge.text(count);
    if (count > 0) {
        cartBadge.show();
    } else {
        cartBadge.hide();
    }
}

// Chatbot functionality
function toggleChatbot() {
    const chatWindow = $('#chatbot-window');
    const chatToggle = $('#chatbot-toggle');
    
    if (chatbotVisible) {
        chatWindow.hide();
        chatToggle.html('<i class="fas fa-comments"></i>');
        chatbotVisible = false;
    } else {
        chatWindow.show();
        chatToggle.html('<i class="fas fa-times"></i>');
        chatbotVisible = true;
        $('#chatbot-input').focus();
    }
}

function handleChatKeyPress(event) {
    if (event.key === 'Enter') {
        sendChatMessage();
    }
}

function sendChatMessage() {
    const input = $('#chatbot-input');
    const message = input.val().trim();
    
    if (!message) return;
    
    // Add user message to chat
    addChatMessage(message, 'user');
    input.val('');
    
    // Show typing indicator
    showTypingIndicator();
    
    // Send message to AI
    $.ajax({
        url: '/Handlers/ChatHandler.ashx',
        method: 'POST',
        data: { message: message },
        success: function(response) {
            hideTypingIndicator();
            if (response.success) {
                addChatMessage(response.message, 'bot');
            } else {
                addChatMessage('Sorry, I encountered an error. Please try again.', 'bot');
            }
        },
        error: function() {
            hideTypingIndicator();
            addChatMessage('Sorry, I\'m having trouble connecting. Please try again later.', 'bot');
        }
    });
}

function addChatMessage(message, sender) {
    const messagesContainer = $('#chatbot-messages');
    const messageClass = sender === 'user' ? 'user-message' : 'bot-message';
    const icon = sender === 'user' ? '<i class="fas fa-user me-2"></i>' : '<i class="fas fa-robot me-2"></i>';
    
    const messageHtml = `
        <div class="${messageClass}">
            ${icon}${escapeHtml(message)}
        </div>
    `;
    
    messagesContainer.append(messageHtml);
    messagesContainer.scrollTop(messagesContainer[0].scrollHeight);
    
    // Save to chat history
    chatHistory.push({ message: message, sender: sender, timestamp: new Date() });
    saveChatHistory();
}

function showTypingIndicator() {
    const messagesContainer = $('#chatbot-messages');
    const typingHtml = `
        <div class="bot-message typing-indicator">
            <i class="fas fa-robot me-2"></i>
            <span class="typing-dots">
                <span>.</span><span>.</span><span>.</span>
            </span>
        </div>
    `;
    messagesContainer.append(typingHtml);
    messagesContainer.scrollTop(messagesContainer[0].scrollHeight);
}

function hideTypingIndicator() {
    $('.typing-indicator').remove();
}

function loadChatHistory() {
    const saved = localStorage.getItem('mediease-chat-history');
    if (saved) {
        chatHistory = JSON.parse(saved);
        // Optionally restore recent messages
    }
}

function saveChatHistory() {
    // Keep only last 50 messages
    if (chatHistory.length > 50) {
        chatHistory = chatHistory.slice(-50);
    }
    localStorage.setItem('mediease-chat-history', JSON.stringify(chatHistory));
}

// File upload functionality
function setupFileUpload() {
    $('.upload-area').on('dragover', function(e) {
        e.preventDefault();
        $(this).addClass('dragover');
    });

    $('.upload-area').on('dragleave', function(e) {
        e.preventDefault();
        $(this).removeClass('dragover');
    });

    $('.upload-area').on('drop', function(e) {
        e.preventDefault();
        $(this).removeClass('dragover');
        
        const files = e.originalEvent.dataTransfer.files;
        if (files.length > 0) {
            handleFileUpload(files[0], $(this));
        }
    });

    $('.file-input').on('change', function() {
        const file = this.files[0];
        if (file) {
            handleFileUpload(file, $(this).closest('.upload-area'));
        }
    });
}

function handleFileUpload(file, uploadArea) {
    // Validate file type and size
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'];
    const maxSize = 5 * 1024 * 1024; // 5MB

    if (!allowedTypes.includes(file.type)) {
        showAlert('Please upload only image files (JPEG, PNG, GIF) or PDF documents.', 'danger');
        return;
    }

    if (file.size > maxSize) {
        showAlert('File size must be less than 5MB.', 'danger');
        return;
    }

    // Show upload progress
    showUploadProgress(uploadArea);

    // Create FormData for upload
    const formData = new FormData();
    formData.append('file', file);

    // Upload file
    $.ajax({
        url: '/api/Upload/Prescription',
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        xhr: function() {
            const xhr = new window.XMLHttpRequest();
            xhr.upload.addEventListener('progress', function(e) {
                if (e.lengthComputable) {
                    const percentComplete = (e.loaded / e.total) * 100;
                    updateUploadProgress(percentComplete);
                }
            });
            return xhr;
        },
        success: function(response) {
            hideUploadProgress();
            if (response.success) {
                showAlert('File uploaded successfully!', 'success');
                displayUploadedFile(response.fileName, uploadArea);
            } else {
                showAlert(response.message || 'Upload failed.', 'danger');
            }
        },
        error: function() {
            hideUploadProgress();
            showAlert('Upload failed. Please try again.', 'danger');
        }
    });
}

// Utility functions
function showAlert(message, type) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${escapeHtml(message)}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // Remove existing alerts
    $('.alert').remove();
    
    // Add new alert at top of main content
    $('.main-content').prepend(alertHtml);
    
    // Auto-dismiss after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
}

function showLoading() {
    const loadingHtml = `
        <div class="spinner-overlay">
            <div class="spinner-border spinner-border-lg text-primary" role="status">
                <span class="sr-only">Loading...</span>
            </div>
        </div>
    `;
    $('body').append(loadingHtml);
}

function hideLoading() {
    $('.spinner-overlay').remove();
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function checkPasswordStrength(password, targetId) {
    const target = $('#' + targetId);
    let strength = 0;
    let feedback = [];

    if (password.length >= 8) strength++;
    else feedback.push('At least 8 characters');

    if (/[a-z]/.test(password)) strength++;
    else feedback.push('Lowercase letter');

    if (/[A-Z]/.test(password)) strength++;
    else feedback.push('Uppercase letter');

    if (/[0-9]/.test(password)) strength++;
    else feedback.push('Number');

    if (/[^A-Za-z0-9]/.test(password)) strength++;
    else feedback.push('Special character');

    const strengthText = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong'][strength];
    const strengthClass = ['danger', 'danger', 'warning', 'info', 'success'][strength];

    target.html(`
        <div class="progress mb-2">
            <div class="progress-bar bg-${strengthClass}" style="width: ${strength * 20}%"></div>
        </div>
        <small class="text-${strengthClass}">Password Strength: ${strengthText}</small>
        ${feedback.length > 0 ? `<br><small class="text-muted">Missing: ${feedback.join(', ')}</small>` : ''}
    `);
}

function validateEmail(input) {
    const email = input.val();
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    
    if (email && !emailRegex.test(email)) {
        input.addClass('is-invalid');
        input.siblings('.invalid-feedback').text('Please enter a valid email address.');
    } else {
        input.removeClass('is-invalid');
    }
}

function formatPhoneNumber(input) {
    let value = input.val().replace(/\D/g, '');
    if (value.length >= 6) {
        value = value.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
    } else if (value.length >= 3) {
        value = value.replace(/(\d{3})(\d{0,3})/, '($1) $2');
    }
    input.val(value);
}

// Accessibility functions
function announceToScreenReader(message) {
    const announcement = $('<div>').attr({
        'aria-live': 'polite',
        'aria-atomic': 'true',
        'class': 'sr-only'
    }).text(message);
    
    $('body').append(announcement);
    setTimeout(() => announcement.remove(), 1000);
}

// Export functions for global access
window.MediEase = {
    toggleChatbot,
    sendChatMessage,
    handleChatKeyPress,
    addToCart,
    showAlert,
    showLoading,
    hideLoading
};
