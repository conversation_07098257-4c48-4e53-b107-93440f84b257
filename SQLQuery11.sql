﻿-- Insert default content for the FAQ page if it doesn't exist
IF NOT EXISTS (SELECT 1 FROM CMSContent WHERE PageName = 'FAQ')
BEGIN
    INSERT INTO CMSContent (PageName, ContentTitle, ContentBody, LastUpdatedByAdminID)
    VALUES ('FAQ', 'Frequently Asked Questions', 
    '<h3>What payment methods do you accept?</h3><p>We currently accept payment via QR code scan. During checkout, you will be shown a QR code to scan with your payment app. You will then need to upload a screenshot of the successful transaction for verification.</p><hr/><h3>How do I track my order?</h3><p>Once you are logged in, you can visit your Dashboard to see a complete history of your orders and their current status (e.g., Under Review, Processing, Shipped).</p>', 1);
    PRINT 'FAQ content added.';
END

-- Insert default content for the Contact page if it doesn't exist
IF NOT EXISTS (SELECT 1 FROM CMSContent WHERE PageName = 'Contact')
BEGIN
    INSERT INTO CMSContent (PageName, ContentTitle, ContentBody, LastUpdatedByAdminID)
    VALUES ('Contact', 'Contact Information', 
    '<p><strong><i class=""fas fa-map-marker-alt mr-2""></i>Address:</strong><br>123 Pharmacy Lane<br>Kathmandu, Nepal</p><p><strong><i class=""fas fa-phone mr-2""></i>Phone:</strong><br>+977 1 1234567</p><p><strong><i class=""fas fa-envelope mr-2""></i>Email:</strong><br><EMAIL></p>', 1);
    PRINT 'Contact page content added.';
END

-- This script updates the content for the 'Contact' page in the database.
UPDATE CMSContent
SET 
    ContentTitle = 'Our Location & Contact Information',
    ContentBody = '
        <h4><i class="fas fa-map-marker-alt mr-2 text-primary"></i>Store Location:</h4>
        <p>Dehimandu, Baitadi<br/>Sudurpashchim Province, Nepal</p>
        <hr/>
        <h4><i class="fas fa-phone mr-2 text-primary"></i>Phone:</h4>
        <p>+977 **********</p>
        <hr/>
        <h4><i class="fas fa-envelope mr-2 text-primary"></i>Email:</h4>
        <p><EMAIL></p>
    '
WHERE 
    PageName = 'Contact';

PRINT 'Contact page content updated successfully.';



IF NOT EXISTS (SELECT 1 FROM BlogPosts WHERE Title = '5 Tips for a Healthier Summer')
BEGIN
    INSERT INTO BlogPosts (Title, Content, AuthorID, IsPublished, PublishDate, LastModifiedDate)
    VALUES ('5 Tips for a Healthier Summer', 'Summer is a great time to focus on your health. Tip 1: Stay hydrated. Tip 2: Protect your skin. Tip 3: Stay active. Tip 4: Eat fresh fruits and vegetables. Tip 5: Get enough rest.', 1, 1, GETDATE(), GETDATE());
END
GO

IF NOT EXISTS (SELECT 1 FROM BlogPosts WHERE Title = 'Understanding Your Prescription')
BEGIN
    INSERT INTO BlogPosts (Title, Content, AuthorID, IsPublished, PublishDate, LastModifiedDate)
    VALUES ('Understanding Your Prescription', 'Navigating medical prescriptions can be confusing. "OD" means once a day, "BID" means twice a day, and "TID" means three times a day. Always ask our staff if you have questions.', 1, 1, GETDATE()-1, GETDATE());
END
GO


