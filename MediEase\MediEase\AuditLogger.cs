﻿using System;
using System.Configuration;
using System.Data.SqlClient;

namespace MediEase
{
    public static class AuditLogger
    {
        public static void LogAction(int? userId, string actionType, string tableName = null, int? recordId = null, string oldValues = null, string newValues = null)
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "INSERT INTO AuditTrail (UserID, ActionType, TableName, RecordID, OldValues, NewValues, Timestamp) VALUES (@UserID, @ActionType, @TableName, @RecordID, @OldValues, @NewValues, GETDATE());";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@UserID", (object)userId ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@ActionType", actionType);
                    cmd.Parameters.AddWithValue("@TableName", (object)tableName ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@RecordID", (object)recordId ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@OldValues", (object)oldValues ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@NewValues", (object)newValues ?? DBNull.Value);
                    try
                    {
                        con.Open();
                        cmd.ExecuteNonQuery();
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine("Audit Log Error: " + ex.Message);
                    }
                }
            }
        }
    }
}