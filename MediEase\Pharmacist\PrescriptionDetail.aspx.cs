﻿using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web.UI;

public partial class Pharmacist_PrescriptionDetail : System.Web.UI.Page
{
    private int PrescriptionId = 0;

    protected void Page_Load(object sender, EventArgs e)
    {
        // --- SECURITY CHECK ---
        if (Session["UserId"] == null || Session["UserRole"]?.ToString() != "Pharmacist")
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        if (!int.TryParse(Request.QueryString["id"], out PrescriptionId))
        {
            Response.Redirect("ValidatePrescriptions.aspx");
            return;
        }

        hdnPrescriptionId.Value = PrescriptionId.ToString();

        if (!IsPostBack)
        {
            LoadPrescriptionDetails();
        }
    }

    private void LoadPrescriptionDetails()
    {
        string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
        using (SqlConnection con = new SqlConnection(connectionString))
        {
            string query = @"
                SELECT p.*, u.FirstName + ' ' + u.LastName AS CustomerName
                FROM Prescriptions p
                INNER JOIN Users u ON p.UserId = u.UserId
                WHERE p.PrescriptionId = @PrescriptionId";

            using (SqlCommand cmd = new SqlCommand(query, con))
            {
                cmd.Parameters.AddWithValue("@PrescriptionId", PrescriptionId);
                try
                {
                    con.Open();
                    SqlDataReader reader = cmd.ExecuteReader();
                    if (reader.Read())
                    {
                        litPrescriptionNumber.Text = reader["PrescriptionNumber"].ToString();
                        litCustomerName.Text = reader["CustomerName"].ToString();
                        litDoctorName.Text = reader["DoctorName"].ToString();
                        litPrescriptionDate.Text = Convert.ToDateTime(reader["PrescriptionDate"]).ToString("MMM dd, yyyy");
                        litCurrentStatus.Text = reader["Status"].ToString();

                        string filePath = reader["FilePath"]?.ToString();
                        if (!string.IsNullOrEmpty(filePath))
                        {
                            imgPrescription.ImageUrl = $"~/{filePath}";
                        }

                        // Hide action panel if prescription is already validated
                        if (reader["Status"].ToString() != "Pending")
                        {
                            pnlValidationActions.Visible = false;
                        }
                    }
                    else
                    {
                        Response.Redirect("ValidatePrescriptions.aspx");
                    }
                }
                catch (Exception ex)
                {
                    ShowMessage("Error loading prescription details: " + ex.Message, "error");
                }
            }
        }
    }

    protected void btnApprove_Click(object sender, EventArgs e)
    {
        UpdatePrescriptionStatus("Verified");
    }

    protected void btnReject_Click(object sender, EventArgs e)
    {
        UpdatePrescriptionStatus("Rejected");
    }

    private void UpdatePrescriptionStatus(string newStatus)
    {
        int pharmacistId = (int)Session["UserId"];
        string notes = txtVerificationNotes.Text.Trim();
        string reason = txtRejectionReason.Text.Trim();

        if (newStatus == "Rejected" && string.IsNullOrWhiteSpace(reason))
        {
            ShowMessage("Rejection reason is required when rejecting a prescription.", "error");
            return;
        }

        string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
        using (SqlConnection con = new SqlConnection(connectionString))
        {
            string query = @"
                UPDATE Prescriptions 
                SET 
                    Status = @Status, 
                    VerifiedBy = @VerifiedBy, 
                    VerificationDate = @VerificationDate,
                    VerificationNotes = @VerificationNotes,
                    RejectionReason = @RejectionReason,
                    ModifiedDate = @VerificationDate
                WHERE PrescriptionId = @PrescriptionId";

            using (SqlCommand cmd = new SqlCommand(query, con))
            {
                cmd.Parameters.AddWithValue("@Status", newStatus);
                cmd.Parameters.AddWithValue("@VerifiedBy", pharmacistId);
                cmd.Parameters.AddWithValue("@VerificationDate", DateTime.Now);
                cmd.Parameters.AddWithValue("@VerificationNotes", notes);
                cmd.Parameters.AddWithValue("@RejectionReason", newStatus == "Rejected" ? (object)reason : DBNull.Value);
                cmd.Parameters.AddWithValue("@PrescriptionId", PrescriptionId);

                try
                {
                    con.Open();
                    cmd.ExecuteNonQuery();
                    LogAudit(pharmacistId, newStatus, notes, reason, con);

                    Session["ToastMessage"] = $"Prescription successfully marked as '{newStatus}'.";
                    Response.Redirect("ValidatePrescriptions.aspx");
                }
                catch (Exception ex)
                {
                    ShowMessage("Error updating prescription: " + ex.Message, "error");
                }
            }
        }
    }

    private void LogAudit(int pharmacistId, string newStatus, string notes, string reason, SqlConnection con)
    {
        string auditQuery = @"
            INSERT INTO AuditLogs (UserId, Action, EntityType, EntityId, NewValues, IPAddress) 
            VALUES (@UserId, @Action, @EntityType, @EntityId, @NewValues, @IPAddress)";

        string details = $"Status: {newStatus}, Notes: {notes}";
        if (newStatus == "Rejected")
        {
            details += $", Reason: {reason}";
        }

        using (SqlCommand auditCmd = new SqlCommand(auditQuery, con))
        {
            auditCmd.Parameters.AddWithValue("@UserId", pharmacistId);
            auditCmd.Parameters.AddWithValue("@Action", "Validate Prescription");
            auditCmd.Parameters.AddWithValue("@EntityType", "Prescription");
            auditCmd.Parameters.AddWithValue("@EntityId", PrescriptionId);
            auditCmd.Parameters.AddWithValue("@NewValues", details);
            auditCmd.Parameters.AddWithValue("@IPAddress", Request.UserHostAddress);
            auditCmd.ExecuteNonQuery();
        }
    }

    protected void btnLogout_Click(object sender, EventArgs e)
    {
        Session.Clear();
        Session.Abandon();
        Response.Redirect("~/Login.aspx");
    }

    private void ShowMessage(string message, string type)
    {
        string cssClass = (type == "success")
            ? "bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg relative mb-4"
            : "bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg relative mb-4";

        litMessage.Text = $"<div class='{cssClass}' role='alert'>{message}</div>";
    }
}
