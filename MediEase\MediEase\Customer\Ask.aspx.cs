﻿/* FILE: Customer/Ask.aspx.cs */
using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace MediEase.Customer
{
    public partial class Ask : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (Session["UserID"] == null || Session["RoleName"]?.ToString() != "Customer") { Response.Redirect("~/Login.aspx?returnUrl=" + Server.UrlEncode(Request.Url.AbsolutePath)); return; }
            if (!IsPostBack) { BindQuestionHistory(); }
        }

        private void BindQuestionHistory()
        {
            int customerId = Convert.ToInt32(Session["UserID"]);
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "SELECT QuestionID, Subject, QuestionDate, IsAnswered FROM PharmacistQuestions WHERE CustomerID = @CustomerID ORDER BY QuestionDate DESC";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@CustomerID", customerId);
                    try
                    {
                        con.Open();
                        SqlDataAdapter sda = new SqlDataAdapter(cmd);
                        DataTable dt = new DataTable();
                        sda.Fill(dt);
                        QuestionHistoryGrid.DataSource = dt;
                        QuestionHistoryGrid.DataBind();
                    }
                    catch (Exception ex) { System.Diagnostics.Debug.WriteLine("Error binding question history: " + ex.Message); }
                }
            }
        }

        protected void SubmitQuestionButton_Click(object sender, EventArgs e)
        {
            if (Page.IsValid)
            {
                int customerId = Convert.ToInt32(Session["UserID"]);
                string subject = SubjectBox.Text.Trim();
                string question = QuestionBox.Text.Trim();
                string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    const string query = "INSERT INTO PharmacistQuestions (CustomerID, Subject, QuestionText) VALUES (@CustomerID, @Subject, @QuestionText)";
                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        cmd.Parameters.AddWithValue("@CustomerID", customerId);
                        cmd.Parameters.AddWithValue("@Subject", subject);
                        cmd.Parameters.AddWithValue("@QuestionText", question);
                        try
                        {
                            con.Open();
                            cmd.ExecuteNonQuery();
                            FormView.Visible = false;
                            SuccessMessage.Visible = true;
                            BindQuestionHistory();
                        }
                        catch (Exception ex) { System.Diagnostics.Debug.WriteLine("Error submitting question: " + ex.Message); }
                    }
                }
            }
        }
    }
}