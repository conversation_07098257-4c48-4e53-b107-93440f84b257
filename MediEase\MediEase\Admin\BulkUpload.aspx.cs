﻿/* FILE: Admin/BulkUpload.aspx.cs */
using System;
using System.Configuration;
using System.Data.SqlClient;
using System.IO;
using System.Text;
using System.Web.UI;
using System.Web.UI.HtmlControls;

namespace MediEase.Admin
{
    public partial class BulkUpload : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (Session["UserID"] == null || Session["RoleName"]?.ToString() != "Admin") { Response.Redirect("~/Login.aspx"); return; }
        }

        protected void UploadButton_Click(object sender, EventArgs e)
        {
            if (!CsvFileUpload.HasFile) { ShowMessage("Please select a file to upload.", false); return; }
            if (Path.GetExtension(CsvFileUpload.FileName).ToLower() != ".csv") { ShowMessage("Invalid file type. Please upload a .csv file.", false); return; }

            int successCount = 0;
            int errorCount = 0;
            StringBuilder errorDetails = new StringBuilder();

            try
            {
                using (StreamReader reader = new StreamReader(CsvFileUpload.FileContent))
                {
                    if (!reader.EndOfStream) { reader.ReadLine(); }
                    int rowNumber = 1;
                    while (!reader.EndOfStream)
                    {
                        rowNumber++;
                        var line = reader.ReadLine();
                        var values = line.Split(',');

                        if (values.Length != 9)
                        {
                            errorCount++;
                            errorDetails.AppendLine($"Row {rowNumber}: Incorrect number of columns. Expected 9, found {values.Length}.<br/>");
                            continue;
                        }
                        if (ProcessRow(values)) { successCount++; }
                        else { errorCount++; errorDetails.AppendLine($"Row {rowNumber}: Failed to insert due to invalid data or database error.<br/>"); }
                    }
                }
                string summary = $"<strong>Upload Complete.</strong><br/>{successCount} products added successfully.<br/>{errorCount} rows failed.";
                if (errorCount > 0) { summary += "<br/><br/><strong>Error Details:</strong><br/>" + errorDetails.ToString(); }
                ShowMessage(summary, errorCount == 0);
            }
            catch (Exception ex)
            {
                ShowMessage("A critical error occurred during file processing: " + ex.Message, false);
                System.Diagnostics.Debug.WriteLine("Bulk Upload Critical Error: " + ex.Message);
            }
        }

        private bool ProcessRow(string[] values)
        {
            try
            {
                string name = values[0].Trim();
                string description = values[1].Trim();
                int categoryId = int.Parse(values[2].Trim());
                int brandId = int.Parse(values[3].Trim());
                string form = values[4].Trim();
                string strength = values[5].Trim();
                decimal price = decimal.Parse(values[6].Trim());
                int stock = int.Parse(values[7].Trim());
                bool isPrescription = bool.Parse(values[8].Trim());
                int adminId = Convert.ToInt32(Session["UserID"]);
                string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    con.Open();
                    SqlTransaction trans = con.BeginTransaction();
                    try
                    {
                        string queryProduct = "INSERT INTO Products (Name, Description, CategoryID, BrandID, Form, Strength, IsPrescriptionRequired, IsApproved, ImageUrl) OUTPUT INSERTED.ProductID VALUES (@Name, @Desc, @CatID, @BrandID, @Form, @Strength, @IsRx, 0, @ImageUrl);";
                        SqlCommand cmdProduct = new SqlCommand(queryProduct, con, trans);
                        cmdProduct.Parameters.AddWithValue("@Name", name); cmdProduct.Parameters.AddWithValue("@Desc", description); cmdProduct.Parameters.AddWithValue("@CatID", categoryId); cmdProduct.Parameters.AddWithValue("@BrandID", brandId); cmdProduct.Parameters.AddWithValue("@Form", form); cmdProduct.Parameters.AddWithValue("@Strength", strength); cmdProduct.Parameters.AddWithValue("@IsRx", isPrescription); cmdProduct.Parameters.AddWithValue("@ImageUrl", "~/Images/Products/default_product.png");
                        int newProductId = (int)cmdProduct.ExecuteScalar();
                        SqlCommand cmdPrice = new SqlCommand("INSERT INTO Pricing (ProductID, Price, SetByUserID) VALUES (@ProductID, @Price, @UserID)", con, trans);
                        cmdPrice.Parameters.AddWithValue("@ProductID", newProductId); cmdPrice.Parameters.AddWithValue("@Price", price); cmdPrice.Parameters.AddWithValue("@UserID", adminId);
                        cmdPrice.ExecuteNonQuery();
                        SqlCommand cmdStock = new SqlCommand("INSERT INTO Inventory (ProductID, BatchNumber, ExpiryDate, QuantityInStock, ReorderLevel, LastUpdatedByUserID) VALUES (@ProductID, @Batch, @Expiry, @Stock, @Reorder, @UserID)", con, trans);
                        cmdStock.Parameters.AddWithValue("@ProductID", newProductId); cmdStock.Parameters.AddWithValue("@Batch", "BULK-UPLOAD"); cmdStock.Parameters.AddWithValue("@Expiry", DateTime.Now.AddYears(2)); cmdStock.Parameters.AddWithValue("@Stock", stock); cmdStock.Parameters.AddWithValue("@Reorder", 10); cmdStock.Parameters.AddWithValue("@UserID", adminId);
                        cmdStock.ExecuteNonQuery();
                        trans.Commit();
                        return true;
                    }
                    catch (Exception) { trans.Rollback(); return false; }
                }
            }
            catch (Exception) { return false; }
        }

        private void ShowMessage(string message, bool isSuccess)
        {
            ResultMessage.Text = message;
            ResultAlert.Attributes["class"] = isSuccess ? "alert alert-success" : "alert alert-danger";
            ResultPlaceholder.Visible = true;
        }
    }
}