﻿/* FILE: Site.Master.cs */
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Web;
using System.Web.UI;
using System.Web.UI.HtmlControls;
using System.Web.UI.WebControls;

namespace MediEase
{
    public partial class SiteMaster : MasterPage
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            ApplyAccessibilitySettings();
            if (!IsPostBack)
            {
                if (Session["UserID"] != null)
                {
                    GuestView.Visible = false;
                    UserView.Visible = true;
                    SetUserNavigation();
                    LoadNotifications();
                }
                else
                {
                    GuestView.Visible = true;
                    UserView.Visible = false;
                }
            }
        }
        private void SetUserNavigation()
        {
            string userId = Session["UserID"]?.ToString();
            string roleName = Session["RoleName"]?.ToString() ?? "";
            switch (roleName)
            {
                case "Admin": DashboardLink.NavigateUrl = "~/Admin/Dashboard.aspx"; ProfileLink.NavigateUrl = "~/Admin/Profile.aspx"; break;
                case "Pharmacist": DashboardLink.NavigateUrl = "~/Pharmacist/Dashboard.aspx"; ProfileLink.NavigateUrl = "~/Pharmacist/Profile.aspx"; break;
                case "Customer": DashboardLink.NavigateUrl = "~/Customer/Dashboard.aspx"; ProfileLink.NavigateUrl = "~/Customer/Profile.aspx"; break;
                default: DashboardLink.Visible = false; ProfileLink.Visible = false; break;
            }
            List<CartItem> cart = Session["Cart"] as List<CartItem>;
            CartCount.InnerText = (cart != null) ? cart.Count.ToString() : "0";
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                string query = "SELECT FirstName FROM UserProfiles WHERE UserID = @UserID";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@UserID", userId);
                    try { con.Open(); object result = cmd.ExecuteScalar(); ProfileNameLabel.Text = result?.ToString() ?? "Profile"; }
                    catch (Exception) { ProfileNameLabel.Text = "Profile"; }
                }
            }
        }
        protected void LogoutButton_Click(object sender, EventArgs e) { Session.Clear(); Session.Abandon(); Response.Redirect("~/Default.aspx"); }
        private void ApplyAccessibilitySettings()
        {
            string bodyClasses = "";
            if (Request.Cookies["darkMode"]?.Value == "enabled") { bodyClasses = "dark-mode"; ((HtmlGenericControl)ToggleDarkMode.FindControl("darkModeIcon")).Attributes["class"] = "fas fa-sun"; } else { ((HtmlGenericControl)ToggleDarkMode.FindControl("darkModeIcon")).Attributes["class"] = "fas fa-moon"; }
            if (Request.Cookies["fontSize"] != null && int.TryParse(Request.Cookies["fontSize"].Value, out int savedSize)) { pageBody.Style["font-size"] = $"{savedSize}px"; }
            if (Request.Cookies["ttsEnabled"]?.Value == "true")
            {
                bodyClasses = string.IsNullOrEmpty(bodyClasses) ? "tts-active" : $"{bodyClasses} tts-active";
                ((HtmlGenericControl)ToggleTTS.FindControl("ttsIcon")).Attributes["class"] = "fas fa-volume-up";
            }
            else
            {
                ((HtmlGenericControl)ToggleTTS.FindControl("ttsIcon")).Attributes["class"] = "fas fa-volume-mute";
            }
            pageBody.Attributes["class"] = bodyClasses.Trim();
        }
        protected void ToggleDarkMode_Click(object sender, EventArgs e) { HttpCookie cookie = new HttpCookie("darkMode") { Value = Request.Cookies["darkMode"]?.Value == "enabled" ? "disabled" : "enabled", Expires = DateTime.Now.AddYears(1) }; Response.Cookies.Add(cookie); Response.Redirect(Request.RawUrl, true); }
        protected void ChangeFontSize_Click(object sender, EventArgs e) { LinkButton btn = (LinkButton)sender; int currentSize = 16; if (Request.Cookies["fontSize"] != null && int.TryParse(Request.Cookies["fontSize"].Value, out int savedSize)) { currentSize = savedSize; } if (btn.CommandArgument == "increase") { currentSize = Math.Min(24, currentSize + 1); } else if (btn.CommandArgument == "decrease") { currentSize = Math.Max(12, currentSize - 1); } HttpCookie cookie = new HttpCookie("fontSize", currentSize.ToString()) { Expires = DateTime.Now.AddYears(1) }; Response.Cookies.Add(cookie); Response.Redirect(Request.RawUrl, true); }
        protected void ToggleTTS_Click(object sender, EventArgs e) { HttpCookie cookie = new HttpCookie("ttsEnabled") { Value = Request.Cookies["ttsEnabled"]?.Value == "true" ? "false" : "true", Expires = DateTime.Now.AddYears(1) }; Response.Cookies.Add(cookie); Response.Redirect(Request.RawUrl, true); }
        private void LoadNotifications()
        {
            if (Session["UserID"] == null) return;
            int userId = Convert.ToInt32(Session["UserID"]);
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "SELECT TOP 5 NotificationID, Message, LinkUrl, CreatedDate FROM Notifications WHERE UserID = @UserID AND IsRead = 0 ORDER BY CreatedDate DESC; SELECT COUNT(*) FROM Notifications WHERE UserID = @UserID AND IsRead = 0;";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@UserID", userId);
                    con.Open(); SqlDataAdapter sda = new SqlDataAdapter(cmd); DataSet ds = new DataSet(); sda.Fill(ds);
                    if (ds.Tables[0].Rows.Count > 0) { NotificationRepeater.DataSource = ds.Tables[0]; NotificationRepeater.DataBind(); NoNotificationsPlaceholder.Visible = false; }
                    else { NoNotificationsPlaceholder.Visible = true; }
                    int unreadCount = (int)ds.Tables[1].Rows[0][0];
                    if (unreadCount > 0) { NotificationBadge.Text = unreadCount.ToString(); NotificationBadge.Visible = true; }
                    else { NotificationBadge.Visible = false; }
                }
            }
        }
        protected void NotificationRepeater_ItemCommand(object source, RepeaterCommandEventArgs e)
        {
            if (e.CommandName == "View")
            {
                string[] args = e.CommandArgument.ToString().Split('|'); int notificationId = Convert.ToInt32(args[0]);
                string linkUrl = args.Length > 1 && !string.IsNullOrEmpty(args[1]) ? args[1] : "~/";
                string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    const string query = "UPDATE Notifications SET IsRead = 1 WHERE NotificationID = @NotificationID";
                    using (SqlCommand cmd = new SqlCommand(query, con)) { cmd.Parameters.AddWithValue("@NotificationID", notificationId); con.Open(); cmd.ExecuteNonQuery(); }
                }
                Response.Redirect(linkUrl);
            }
        }
        protected void MarkAllAsReadButton_Click(object sender, EventArgs e)
        {
            int userId = Convert.ToInt32(Session["UserID"]);
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "UPDATE Notifications SET IsRead = 1 WHERE UserID = @UserID AND IsRead = 0";
                using (SqlCommand cmd = new SqlCommand(query, con)) { cmd.Parameters.AddWithValue("@UserID", userId); con.Open(); cmd.ExecuteNonQuery(); }
            }
            LoadNotifications();
        }
    }
}