﻿<%--
 =======================================================================
 FILE: Dashboard.aspx (Final Interlinked Pharmacist Version)
 =======================================================================
--%>
<%@ Page Title="Pharmacist Dashboard" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="Dashboard.aspx.cs" Inherits="MediEase.Pharmacist.Dashboard" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-5">
        <h1 class="mb-4">Pharmacist Dashboard</h1>
        
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card text-white bg-warning shadow">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="h2 font-weight-bold"><asp:Label ID="PendingOrdersLabel" runat="server" Text="0"></asp:Label></div>
                                <div class="h5">Pending Orders</div>
                            </div>
                            <i class="fas fa-exclamation-circle fa-3x"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-white bg-info shadow">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="h2 font-weight-bold"><asp:Label ID="PendingQuestionsLabel" runat="server" Text="0"></asp:Label></div>
                                <div class="h5">Pending Questions</div>
                            </div>
                            <i class="fas fa-question-circle fa-3x"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-white bg-secondary shadow">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="h2 font-weight-bold"><asp:Label ID="OpenFeedbackLabel" runat="server" Text="0"></asp:Label></div>
                                <div class="h5">Open Feedback</div>
                            </div>
                            <i class="fas fa-comment-dots fa-3x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-3">
                <div class="card shadow-sm">
                    <div class="card-header"><h5 class="mb-0">Management Menu</h5></div>
                    <div class="list-group list-group-flush">
                        <a href="ManageProducts.aspx" class="list-group-item list-group-item-action">Manage Products</a>
                        <a href="ManageStock.aspx" class="list-group-item list-group-item-action">Manage Stock</a>
                        <a href="ManageOrders.aspx" class="list-group-item list-group-item-action">Manage Orders</a>
                        <a href="ManagePricing.aspx" class="list-group-item list-group-item-action">Manage Pricing</a>
                        <a href="ManageQuestions.aspx" class="list-group-item list-group-item-action">Manage Questions</a>
                        <a href="ManageFeedback.aspx" class="list-group-item list-group-item-action">Manage Feedback</a>
                        <a href="ViewDiscounts.aspx" class="list-group-item list-group-item-action">View Discounts</a>
                        <a href="Reports.aspx" class="list-group-item list-group-item-action">View Reports</a>
                    </div>
                </div>
            </div>
            <div class="col-lg-9">
                <div class="card shadow-sm">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Order Processing Queue</h5>
                        <asp:DropDownList ID="StatusFilter" runat="server" AutoPostBack="true" OnSelectedIndexChanged="StatusFilter_SelectedIndexChanged" CssClass="form-control form-control-sm w-auto"></asp:DropDownList>
                    </div>
                    <div class="card-body">
                        <asp:GridView ID="OrdersGrid" runat="server"
                            CssClass="table table-hover"
                            AutoGenerateColumns="False"
                            DataKeyNames="OrderID"
                            GridLines="None"
                            AllowPaging="True"
                            OnPageIndexChanging="OrdersGrid_PageIndexChanging"
                            PageSize="10">
                            <Columns>
                                <asp:BoundField DataField="OrderID" HeaderText="ID" />
                                <asp:BoundField DataField="CustomerName" HeaderText="Customer" />
                                <asp:BoundField DataField="OrderDate" HeaderText="Date" DataFormatString="{0:d}" />
                                <asp:BoundField DataField="TotalAmount" HeaderText="Total" DataFormatString="{0:C}" />
                                <asp:TemplateField HeaderText="Status"><ItemTemplate><span class='badge badge-pill <%# GetStatusBadgeClass(Eval("StatusName").ToString()) %>'><%# Eval("StatusName") %></span></ItemTemplate></asp:TemplateField>
                                <asp:HyperLinkField DataNavigateUrlFields="OrderID" DataNavigateUrlFormatString="~/Pharmacist/OrderDetails.aspx?OrderID={0}" Text="View Details" ControlStyle-CssClass="btn btn-sm btn-primary" />
                            </Columns>
                            <EmptyDataTemplate><div class="alert alert-info text-center">No orders match the selected status.</div></EmptyDataTemplate>
                        </asp:GridView>
                    </div>
                </div>
            </div>
        </div>
    </div>
</asp:Content>