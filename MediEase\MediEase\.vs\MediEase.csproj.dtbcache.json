{"RootPath": "C:\\Users\\<USER>\\Desktop\\Project\\MediEase\\MediEase", "ProjectFileName": "MediEase.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "Admin\\AuditTrail.aspx.cs"}, {"SourceFile": "Admin\\AuditTrail.aspx.designer.cs"}, {"SourceFile": "Admin\\BulkUpload.aspx.cs"}, {"SourceFile": "Admin\\BulkUpload.aspx.designer.cs"}, {"SourceFile": "Admin\\Dashboard.aspx.cs"}, {"SourceFile": "Admin\\Dashboard.aspx.designer.cs"}, {"SourceFile": "Admin\\EditPost.aspx.cs"}, {"SourceFile": "Admin\\EditPost.aspx.designer.cs"}, {"SourceFile": "Admin\\ManageBlog.aspx.cs"}, {"SourceFile": "Admin\\ManageBlog.aspx.designer.cs"}, {"SourceFile": "Admin\\ManageChatLogs.aspx.cs"}, {"SourceFile": "Admin\\ManageChatLogs.aspx.designer.cs"}, {"SourceFile": "Admin\\ManageContent.aspx.cs"}, {"SourceFile": "Admin\\ManageContent.aspx.designer.cs"}, {"SourceFile": "Admin\\ManageDiscounts.aspx.cs"}, {"SourceFile": "Admin\\ManageDiscounts.aspx.designer.cs"}, {"SourceFile": "Admin\\ManageOrders.aspx.cs"}, {"SourceFile": "Admin\\ManageOrders.aspx.designer.cs"}, {"SourceFile": "Admin\\ManageProducts.aspx.cs"}, {"SourceFile": "Admin\\ManageProducts.aspx.designer.cs"}, {"SourceFile": "Admin\\ManageSettings.aspx.cs"}, {"SourceFile": "Admin\\ManageSettings.aspx.designer.cs"}, {"SourceFile": "Admin\\ManageUsers.aspx.cs"}, {"SourceFile": "Admin\\ManageUsers.aspx.designer.cs"}, {"SourceFile": "Admin\\OrderDetails.aspx.cs"}, {"SourceFile": "Admin\\OrderDetails.aspx.designer.cs"}, {"SourceFile": "Admin\\Profile.aspx.cs"}, {"SourceFile": "Admin\\Profile.aspx.designer.cs"}, {"SourceFile": "Admin\\Reports.aspx.cs"}, {"SourceFile": "Admin\\Reports.aspx.designer.cs"}, {"SourceFile": "App_Start\\RouteConfig.cs"}, {"SourceFile": "App_Start\\WebApiConfig.cs"}, {"SourceFile": "AuditLogger.cs"}, {"SourceFile": "Blog.aspx.cs"}, {"SourceFile": "Blog.aspx.designer.cs"}, {"SourceFile": "Cart.aspx.cs"}, {"SourceFile": "Cart.aspx.designer.cs"}, {"SourceFile": "CartItem.cs"}, {"SourceFile": "Chatbot.aspx.cs"}, {"SourceFile": "Chatbot.aspx.designer.cs"}, {"SourceFile": "Checkout.aspx.cs"}, {"SourceFile": "Checkout.aspx.designer.cs"}, {"SourceFile": "Contact.aspx.cs"}, {"SourceFile": "Contact.aspx.designer.cs"}, {"SourceFile": "Customer\\Ask.aspx.cs"}, {"SourceFile": "Customer\\Ask.aspx.designer.cs"}, {"SourceFile": "Customer\\Dashboard.aspx.cs"}, {"SourceFile": "Customer\\Dashboard.aspx.designer.cs"}, {"SourceFile": "Customer\\Feedback.aspx.cs"}, {"SourceFile": "Customer\\Feedback.aspx.designer.cs"}, {"SourceFile": "Customer\\OrderDetails.aspx.cs"}, {"SourceFile": "Customer\\OrderDetails.aspx.designer.cs"}, {"SourceFile": "Customer\\Profile.aspx.cs"}, {"SourceFile": "Customer\\Profile.aspx.designer.cs"}, {"SourceFile": "Customer\\Reminders.aspx.cs"}, {"SourceFile": "Customer\\Reminders.aspx.designer.cs"}, {"SourceFile": "Default.aspx.cs"}, {"SourceFile": "Default.aspx.designer.cs"}, {"SourceFile": "EmailService.cs"}, {"SourceFile": "FAQ.aspx.cs"}, {"SourceFile": "FAQ.aspx.designer.cs"}, {"SourceFile": "ForgotPassword.aspx.cs"}, {"SourceFile": "ForgotPassword.aspx.designer.cs"}, {"SourceFile": "Global.asax.cs"}, {"SourceFile": "Login.aspx.cs"}, {"SourceFile": "Login.aspx.designer.cs"}, {"SourceFile": "OrderSuccess.aspx.cs"}, {"SourceFile": "OrderSuccess.aspx.designer.cs"}, {"SourceFile": "Pharmacist\\AnswerQuestion.aspx.cs"}, {"SourceFile": "Pharmacist\\AnswerQuestion.aspx.designer.cs"}, {"SourceFile": "Pharmacist\\Dashboard.aspx.cs"}, {"SourceFile": "Pharmacist\\Dashboard.aspx.designer.cs"}, {"SourceFile": "Pharmacist\\FeedbackDetails.aspx.cs"}, {"SourceFile": "Pharmacist\\FeedbackDetails.aspx.designer.cs"}, {"SourceFile": "Pharmacist\\Invoice.aspx.cs"}, {"SourceFile": "Pharmacist\\Invoice.aspx.designer.cs"}, {"SourceFile": "Pharmacist\\ManageFeedback.aspx.cs"}, {"SourceFile": "Pharmacist\\ManageFeedback.aspx.designer.cs"}, {"SourceFile": "Pharmacist\\ManageOrders.aspx.cs"}, {"SourceFile": "Pharmacist\\ManageOrders.aspx.designer.cs"}, {"SourceFile": "Pharmacist\\ManagePricing.aspx.cs"}, {"SourceFile": "Pharmacist\\ManagePricing.aspx.designer.cs"}, {"SourceFile": "Pharmacist\\ManageProducts.aspx.cs"}, {"SourceFile": "Pharmacist\\ManageProducts.aspx.designer.cs"}, {"SourceFile": "Pharmacist\\ManageQuestions.aspx.cs"}, {"SourceFile": "Pharmacist\\ManageQuestions.aspx.designer.cs"}, {"SourceFile": "Pharmacist\\ManageStock.aspx.cs"}, {"SourceFile": "Pharmacist\\ManageStock.aspx.designer.cs"}, {"SourceFile": "Pharmacist\\OrderDetails.aspx.cs"}, {"SourceFile": "Pharmacist\\OrderDetails.aspx.designer.cs"}, {"SourceFile": "Pharmacist\\Profile.aspx.cs"}, {"SourceFile": "Pharmacist\\Profile.aspx.designer.cs"}, {"SourceFile": "Pharmacist\\Reports.aspx.cs"}, {"SourceFile": "Pharmacist\\Reports.aspx.designer.cs"}, {"SourceFile": "Pharmacist\\StockDetails.aspx.cs"}, {"SourceFile": "Pharmacist\\StockDetails.aspx.designer.cs"}, {"SourceFile": "Pharmacist\\ViewDiscounts.aspx.cs"}, {"SourceFile": "Pharmacist\\ViewDiscounts.aspx.designer.cs"}, {"SourceFile": "Post.aspx.cs"}, {"SourceFile": "Post.aspx.designer.cs"}, {"SourceFile": "ProductDetails.aspx.cs"}, {"SourceFile": "ProductDetails.aspx.designer.cs"}, {"SourceFile": "Properties\\AssemblyInfo.cs"}, {"SourceFile": "Register.aspx.cs"}, {"SourceFile": "Register.aspx.designer.cs"}, {"SourceFile": "ResetPassword.aspx.cs"}, {"SourceFile": "ResetPassword.aspx.designer.cs"}, {"SourceFile": "SettingsService.cs"}, {"SourceFile": "Shop.aspx.cs"}, {"SourceFile": "Shop.aspx.designer.cs"}, {"SourceFile": "Site.Master.cs"}, {"SourceFile": "Site.Master.designer.cs"}, {"SourceFile": "Site.Mobile.Master.cs"}, {"SourceFile": "Site.Mobile.Master.designer.cs"}, {"SourceFile": "ViewSwitcher.ascx.cs"}, {"SourceFile": "ViewSwitcher.ascx.designer.cs"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.8.1.AssemblyAttributes.cs"}], "References": [{"Reference": "C:\\Users\\<USER>\\Desktop\\Project\\MediEase\\packages\\Microsoft.AspNet.FriendlyUrls.Core.1.0.2\\lib\\net45\\Microsoft.AspNet.FriendlyUrls.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Project\\MediEase\\packages\\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\\lib\\net45\\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8.1\\Microsoft.CSharp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Project\\MediEase\\packages\\Microsoft.Web.Infrastructure.2.0.0\\lib\\net40\\Microsoft.Web.Infrastructure.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8.1\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Project\\MediEase\\packages\\Newtonsoft.Json.13.0.3\\lib\\net45\\Newtonsoft.Json.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8.1\\System.ComponentModel.DataAnnotations.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8.1\\System.Configuration.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8.1\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8.1\\System.Data.DataSetExtensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8.1\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8.1\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8.1\\System.Drawing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8.1\\System.EnterpriseServices.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8.1\\System.Net.Http.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Project\\MediEase\\packages\\Microsoft.AspNet.WebApi.Client.5.2.9\\lib\\net45\\System.Net.Http.Formatting.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8.1\\System.Web.ApplicationServices.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8.1\\System.Web.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8.1\\System.Web.DynamicData.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8.1\\System.Web.Entity.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8.1\\System.Web.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Project\\MediEase\\packages\\Microsoft.AspNet.Webpages.3.2.9\\lib\\net45\\System.Web.Helpers.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Project\\MediEase\\packages\\Microsoft.AspNet.WebApi.Core.5.2.9\\lib\\net45\\System.Web.Http.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Project\\MediEase\\packages\\Microsoft.AspNet.WebApi.WebHost.5.2.9\\lib\\net45\\System.Web.Http.WebHost.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Project\\MediEase\\packages\\Microsoft.AspNet.Mvc.5.2.9\\lib\\net45\\System.Web.Mvc.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Project\\MediEase\\packages\\Microsoft.AspNet.Razor.3.2.9\\lib\\net45\\System.Web.Razor.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8.1\\System.Web.Services.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Project\\MediEase\\packages\\Microsoft.AspNet.Webpages.3.2.9\\lib\\net45\\System.Web.Webpages.Deployment.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Project\\MediEase\\packages\\Microsoft.AspNet.Webpages.3.2.9\\lib\\net45\\System.Web.Webpages.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Project\\MediEase\\packages\\Microsoft.AspNet.Webpages.3.2.9\\lib\\net45\\System.Web.Webpages.Razor.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8.1\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8.1\\System.Xml.Linq.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "C:\\Users\\<USER>\\Desktop\\Project\\MediEase\\MediEase\\bin\\MediEase.dll", "OutputItemRelativePath": "MediEase.dll"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}