﻿/*
 =======================================================================
 MediEase: Database Schema Update for "Forgot Password" Feature
 PURPOSE: Adds columns to the Users table to store a password reset
          token and its expiration date.
 =======================================================================
*/

-- Add ResetToken column if it doesn't exist
IF NOT EXISTS (SELECT * FROM sys.columns 
               WHERE Name = N'ResetToken' AND Object_ID = Object_ID(N'Users'))
BEGIN
    ALTER TABLE Users
    ADD ResetToken NVARCHAR(100) NULL;
    PRINT 'ResetToken column added to Users table successfully.';
END
ELSE
BEGIN
    PRINT 'ResetToken column already exists in Users table.';
END
GO

-- Add ResetTokenExpiry column if it doesn't exist
IF NOT EXISTS (SELECT * FROM sys.columns 
               WHERE Name = N'ResetTokenExpiry' AND Object_ID = Object_ID(N'Users'))
BEGIN
    ALTER TABLE Users
    ADD ResetTokenExpiry DATETIME NULL;
    PRINT 'ResetTokenExpiry column added to Users table successfully.';
END
ELSE
BEGIN
    PRINT 'ResetTokenExpiry column already exists in Users table.';
END
GO

