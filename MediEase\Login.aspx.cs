using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Runtime.Remoting.Messaging;
using System.Web;
using System.Web.UI;

public partial class Login : System.Web.UI.Page
{
    protected void btnLogin_Click(object sender, EventArgs e)
    {
        if (!Page.IsValid)
        {
            return;
        }

        string email = txtEmail.Text.Trim();
        string password = txtPassword.Text;

        string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;

        using (SqlConnection con = new SqlConnection(connectionString))
        {
            // Query to get user details including the password hash and role
            string query = "SELECT UserId, FirstName, PasswordHash, Role, IsActive FROM [Users] WHERE Email = @Email";
            using (SqlCommand cmd = new SqlCommand(query, con))
            {
                cmd.Parameters.AddWithValue("@Email", email);

                try
                {
                    con.Open();
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        if (reader.Read()) // If a user with that email was found
                        {
                            if (!(bool)reader["IsActive"])
                            {
                                ShowMessage("Your account is inactive. Please contact support.", "error");
                                return;
                            }

                            string storedHash = reader["PasswordHash"].ToString();

                            // Verify the password against the stored hash
                            if (BCrypt.Net.BCrypt.Verify(password, storedHash))
                            {
                                // --- AUTHENTICATION SUCCESSFUL ---

                                // Store user info in Session
                                Session["UserId"] = reader["UserId"];
                                Session["UserFirstName"] = reader["FirstName"];
                                Session["UserRole"] = reader["Role"];

                                int userId = (int)reader["UserId"];
                                reader.Close(); // Close the reader before executing another command

                                // Update LastLoginDate
                                UpdateLastLoginDate(userId, con);

                                // Redirect based on role
                                string userRole = Session["UserRole"].ToString();
                                if (userRole == "Admin")
                                {
                                    Response.Redirect("~/Admin/Dashboard.aspx");
                                }
                                else if (userRole == "Pharmacist")
                                {
                                    Response.Redirect("~/Pharmacist/Dashboard.aspx");
                                }
                                else // Customer
                                {
                                    Response.Redirect("~/Customer/Dashboard.aspx");
                                }
                            }
                            else
                            {
                                // Password does not match
                                ShowMessage("Invalid email or password.", "error");
                            }
                        }
                        else
                        {
                            // No user found with that email
                            ShowMessage("Invalid email or password.", "error");
                        }
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine(ex.ToString());
                    ShowMessage("An error occurred. Please try again later.", "error");
                }
            }
        }
    }

    /// <summary>
    /// Updates the LastLoginDate for the specified user.
    /// </summary>
    private void UpdateLastLoginDate(int userId, SqlConnection con)
    {
        string updateQuery = "UPDATE [Users] SET LastLoginDate = @LastLoginDate WHERE UserId = @UserId";
        using (SqlCommand updateCmd = new SqlCommand(updateQuery, con))
        {
            updateCmd.Parameters.AddWithValue("@LastLoginDate", DateTime.Now);
            updateCmd.Parameters.AddWithValue("@UserId", userId);
            updateCmd.ExecuteNonQuery();
        }
    }

    /// <summary>
    /// Displays a formatted message to the user.
    /// </summary>
    private void ShowMessage(string message, string type)
    {
        string cssClass = (type == "success")
            ? "bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4"
            : "bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4";

        litMessage.Text = $"<div class='{cssClass}' role='alert'>{message}</div>";
    }
}
