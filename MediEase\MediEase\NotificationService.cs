﻿using System;
using System.Configuration;
using System.Data.SqlClient;

namespace MediEase
{
    public static class NotificationService
    {
        public static void CreateNotification(int userId, string message, string linkUrl)
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "INSERT INTO Notifications (UserID, Message, LinkUrl, IsRead, CreatedDate) VALUES (@UserID, @Message, @LinkUrl, 0, GETDATE());";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@UserID", userId);
                    cmd.Parameters.AddWithValue("@Message", message);
                    cmd.Parameters.AddWithValue("@LinkUrl", linkUrl);
                    try
                    {
                        con.Open();
                        cmd.ExecuteNonQuery();
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine("Create Notification Error: " + ex.Message);
                    }
                }
            }
        }
    }
}