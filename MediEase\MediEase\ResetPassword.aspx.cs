﻿using System;
using System.Configuration;
using System.Data.SqlClient;
using System.Web.UI;

namespace MediEase
{
    public partial class ResetPassword : Page
    {
        private string resetToken;

        protected void Page_Load(object sender, EventArgs e)
        {
            resetToken = Request.QueryString["token"];
            if (string.IsNullOrEmpty(resetToken))
            {
                ShowErrorView();
                return;
            }

            if (!IsPostBack)
            {
                if (!IsTokenValid(resetToken))
                {
                    ShowErrorView();
                }
            }
        }

        private bool IsTokenValid(string token)
        {
            bool isValid = false;
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "SELECT COUNT(*) FROM Users WHERE ResetToken = @ResetToken AND ResetTokenExpiry > GETDATE()";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@ResetToken", token);
                    try
                    {
                        con.Open();
                        int userCount = (int)cmd.ExecuteScalar();
                        if (userCount > 0) { isValid = true; }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine("Token Validation Error: " + ex.Message);
                    }
                }
            }
            return isValid;
        }

        protected void ResetPasswordButton_Click(object sender, EventArgs e)
        {
            if (Page.IsValid && IsTokenValid(resetToken))
            {
                string newPassword = PasswordBox.Text;
                string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    const string query = "UPDATE Users SET Password = @Password, ResetToken = NULL, ResetTokenExpiry = NULL WHERE ResetToken = @ResetToken";
                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        cmd.Parameters.AddWithValue("@Password", newPassword);
                        cmd.Parameters.AddWithValue("@ResetToken", resetToken);
                        try
                        {
                            con.Open();
                            int rowsAffected = cmd.ExecuteNonQuery();
                            if (rowsAffected > 0)
                            {
                                FormView.Visible = false;
                                MessageView.Visible = true;
                            }
                            else
                            {
                                ShowErrorView();
                            }
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine("Password Reset DB Error: " + ex.Message);
                            ShowErrorView();
                        }
                    }
                }
            }
            else
            {
                ShowErrorView();
            }
        }

        private void ShowErrorView()
        {
            FormView.Visible = false;
            MessageView.Visible = false;
            ErrorView.Visible = true;
        }
    }
}