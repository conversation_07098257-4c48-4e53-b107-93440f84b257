﻿<%--
 =======================================================================
 FILE: ForgotPassword.aspx (Final Version)
 =======================================================================
--%>
<%@ Page Title="Forgot Your Password?" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="ForgotPassword.aspx.cs" Inherits="MediEase.ForgotPassword" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card mt-5 shadow-sm">
                <div class="card-body p-4">
                    <div class="text-center">
                        <h2 class="card-title mb-3"><%: Title %></h2>
                        <p class="text-muted">Enter your email address and we will send you a link to reset your password.</p>
                    </div>
                    <hr />
                    <asp:PlaceHolder ID="FormView" runat="server">
                        <div class="form-group">
                            <label for="Email">Email Address</label>
                            <asp:TextBox ID="EmailBox" runat="server" CssClass="form-control" TextMode="Email" placeholder="Enter your registered email"></asp:TextBox>
                            <asp:RequiredFieldValidator runat="server" ControlToValidate="EmailBox" ErrorMessage="Email address is required." CssClass="text-danger" Display="Dynamic" />
                        </div>
                        <asp:Button ID="SendLinkButton" runat="server" Text="Send Reset Link" CssClass="btn btn-primary btn-block" OnClick="SendLinkButton_Click" />
                    </asp:PlaceHolder>
                    <asp:PlaceHolder ID="MessageView" runat="server" Visible="false">
                        <div class="alert alert-success">
                           <asp:Literal ID="SuccessMessage" runat="server"></asp:Literal>
                        </div>
                    </asp:PlaceHolder>
                </div>
            </div>
        </div>
    </div>
</asp:Content>