﻿<%--
 =======================================================================
 FILE: ManageDiscounts.aspx (Final Version)
 =======================================================================
--%>
<%@ Page Title="Manage Discounts" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="ManageDiscounts.aspx.cs" Inherits="MediEase.Admin.ManageDiscounts" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>Manage Discounts</h1>
            <a href="Dashboard.aspx" class="btn btn-outline-secondary">&larr; Back to Admin Dashboard</a>
        </div>
        <div class="card shadow-sm mb-4">
            <div class="card-header"><h5 class="mb-0">Create New Discount Code</h5></div>
            <div class="card-body">
                <asp:PlaceHolder ID="MessagePlaceholder" runat="server" Visible="false">
                    <div class="alert" id="MessageAlert" runat="server"><asp:Literal ID="MessageText" runat="server"></asp:Literal></div>
                </asp:PlaceHolder>
                <div class="form-row">
                    <div class="form-group col-md-4"><label>Discount Code</label><asp:TextBox ID="DiscountCodeBox" runat="server" CssClass="form-control" placeholder="e.g., SAVE10"></asp:TextBox></div>
                    <div class="form-group col-md-4"><label>Discount Percentage (%)</label><asp:TextBox ID="PercentageBox" runat="server" CssClass="form-control" TextMode="Number" placeholder="e.g., 10"></asp:TextBox></div>
                     <div class="form-group col-md-4 align-self-end"><asp:Button ID="CreateDiscountButton" runat="server" Text="Create Discount" CssClass="btn btn-primary btn-block" OnClick="CreateDiscountButton_Click" /></div>
                </div>
                <div class="form-row">
                    <div class="form-group col-md-4"><label>Start Date (Optional)</label><asp:TextBox ID="StartDateBox" runat="server" CssClass="form-control" TextMode="Date"></asp:TextBox></div>
                    <div class="form-group col-md-4"><label>End Date (Optional)</label><asp:TextBox ID="EndDateBox" runat="server" CssClass="form-control" TextMode="Date"></asp:TextBox></div>
                </div>
            </div>
        </div>
        <div class="card shadow-sm">
            <div class="card-header"><h5 class="mb-0">Existing Discount Codes</h5></div>
            <div class="card-body">
                 <asp:GridView ID="DiscountsGrid" runat="server" CssClass="table table-hover table-striped" AutoGenerateColumns="False" DataKeyNames="DiscountID" GridLines="None" OnRowCommand="DiscountsGrid_RowCommand" PageSize="10" AllowPaging="True">
                    <Columns>
                        <asp:BoundField DataField="DiscountCode" HeaderText="Code" /><asp:BoundField DataField="Description" HeaderText="Description" /><asp:BoundField DataField="DiscountPercentage" HeaderText="Percentage" DataFormatString="{0:F2}%" /><asp:BoundField DataField="StartDate" HeaderText="Start Date" DataFormatString="{0:d}" /><asp:BoundField DataField="EndDate" HeaderText="End Date" DataFormatString="{0:d}" />
                        <asp:TemplateField HeaderText="Status"><ItemTemplate><span class='badge <%# Convert.ToBoolean(Eval("IsActive")) ? "badge-success" : "badge-danger" %>'><%# Convert.ToBoolean(Eval("IsActive")) ? "Active" : "Inactive" %></span></ItemTemplate></asp:TemplateField>
                        <asp:TemplateField HeaderText="Actions"><ItemTemplate><asp:LinkButton ID="ToggleButton" runat="server" Text='<%# Convert.ToBoolean(Eval("IsActive")) ? "Deactivate" : "Activate" %>' CssClass='<%# Convert.ToBoolean(Eval("IsActive")) ? "btn btn-sm btn-warning" : "btn btn-sm btn-success" %>' CommandName="ToggleStatus" CommandArgument='<%# Eval("DiscountID") %>' /></ItemTemplate></asp:TemplateField>
                    </Columns>
                 </asp:GridView>
            </div>
        </div>
    </div>
</asp:Content>