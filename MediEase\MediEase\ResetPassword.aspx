﻿<%--
 =======================================================================
 FILE: ResetPassword.aspx (Final Version)
 =======================================================================
--%>
<%@ Page Title="Reset Your Password" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="ResetPassword.aspx.cs" Inherits="MediEase.ResetPassword" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card mt-5 shadow-sm">
                <div class="card-body p-4">
                     <h2 class="card-title text-center mb-4"><%: Title %></h2>
                    <hr />

                    <asp:PlaceHolder ID="FormView" runat="server">
                         <p class="text-center text-muted">Please enter your new password below.</p>
                        <div class="form-group">
                            <label>New Password</label>
                            <asp:TextBox ID="PasswordBox" runat="server" CssClass="form-control" TextMode="Password"></asp:TextBox>
                            <asp:RequiredFieldValidator runat="server" ControlToValidate="PasswordBox" ErrorMessage="New password is required." CssClass="text-danger" Display="Dynamic" />
                        </div>
                        <div class="form-group">
                            <label>Confirm New Password</label>
                            <asp:TextBox ID="ConfirmPasswordBox" runat="server" CssClass="form-control" TextMode="Password"></asp:TextBox>
                            <asp:CompareValidator runat="server" ControlToValidate="ConfirmPasswordBox" ControlToCompare="PasswordBox" ErrorMessage="The passwords do not match." CssClass="text-danger" Display="Dynamic" />
                        </div>
                        <asp:Button ID="ResetPasswordButton" runat="server" Text="Reset Password" CssClass="btn btn-primary btn-block" OnClick="ResetPasswordButton_Click" />
                    </asp:PlaceHolder>

                    <asp:PlaceHolder ID="MessageView" runat="server" Visible="false">
                        <div class="alert alert-success">
                           <h4>Password Reset Successfully!</h4>
                           <p>Your password has been changed. You can now log in with your new password.</p>
                           <a href="Login.aspx" class="btn btn-success btn-block">Go to Login</a>
                        </div>
                    </asp:PlaceHolder>

                    <asp:PlaceHolder ID="ErrorView" runat="server" Visible="false">
                        <div class="alert alert-danger">
                            <h4>Invalid Link</h4>
                            <p>The password reset link is invalid or has expired. Please request a new one.</p>
                             <a href="ForgotPassword.aspx" class="btn btn-danger btn-block">Request New Link</a>
                        </div>
                    </asp:PlaceHolder>

                </div>
            </div>
        </div>
    </div>
</asp:Content>