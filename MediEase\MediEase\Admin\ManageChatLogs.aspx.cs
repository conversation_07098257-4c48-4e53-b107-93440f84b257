﻿/*
 =======================================================================
 FILE: ManageChatLogs.aspx.cs (Final Version)
 =======================================================================
*/
using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace MediEase.Admin
{
    public partial class ManageChatLogs : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (Session["UserID"] == null || Session["RoleName"]?.ToString() != "Admin")
            {
                Response.Redirect("~/Login.aspx");
                return;
            }

            if (!IsPostBack)
            {
                BindChatLogsGrid();
            }
        }

        private void BindChatLogsGrid()
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = @"
                    SELECT 
                        cl.LogID,
                        cl.Timestamp,
                        ISNULL(up.FirstName + ' ' + up.LastName, 'Guest') AS UserName,
                        cl.Message,
                        cl.Response
                    FROM 
                        ChatLogs cl
                    LEFT JOIN 
                        UserProfiles up ON cl.UserID = up.UserID
                    ORDER BY 
                        cl.Timestamp DESC;
                ";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    try
                    {
                        con.Open();
                        SqlDataAdapter sda = new SqlDataAdapter(cmd);
                        DataTable dt = new DataTable();
                        sda.Fill(dt);
                        ChatLogsGrid.DataSource = dt;
                        ChatLogsGrid.DataBind();
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine("Error binding chat logs: " + ex.Message);
                    }
                }
            }
        }

        protected void ChatLogsGrid_PageIndexChanging(object sender, GridViewPageEventArgs e)
        {
            ChatLogsGrid.PageIndex = e.NewPageIndex;
            BindChatLogsGrid();
        }
    }
}