﻿<%--
 =======================================================================
 FILE: OrderDetails.aspx (Final Version)
 =======================================================================
--%>
<%@ Page Title="Admin - Order Details" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="OrderDetails.aspx.cs" Inherits="MediEase.Admin.OrderDetails" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-5">
        <asp:PlaceHolder ID="OrderDetailsView" runat="server">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2>Order Details</h2>
                            <p class="mb-0">Order #<asp:Label ID="OrderIDLabel" runat="server"></asp:Label></p>
                        </div>
                        <a href="ManageOrders.aspx" class="btn btn-outline-secondary">&larr; Back to All Orders</a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h4>Customer &amp; Shipping Details</h4>
                            <p class="mb-1"><strong>Customer:</strong> <asp:Label ID="CustomerNameLabel" runat="server"></asp:Label></p>
                            <p class="mb-1"><strong>Contact:</strong> <asp:Label ID="CustomerPhoneLabel" runat="server"></asp:Label></p>
                            <p class="mb-1"><strong>Shipping Address:</strong><br /><asp:Label ID="ShippingAddressLabel" runat="server"></asp:Label></p>
                        </div>
                        <div class="col-md-6">
                            <h4>Order Summary</h4>
                            <p class="mb-1"><strong>Order Date:</strong> <asp:Label ID="OrderDateLabel" runat="server"></asp:Label></p>
                            <p class="mb-1"><strong>Order Total:</strong> <asp:Label ID="OrderTotalLabel" runat="server"></asp:Label></p>
                            <p class="mb-1"><strong>Current Status:</strong> <asp:Label ID="OrderStatusLabel" runat="server" CssClass="badge badge-pill"></asp:Label></p>
                        </div>
                    </div>
                    <hr />
                    <div class="row">
                         <asp:PlaceHolder ID="PrescriptionView" runat="server" Visible="false">
                             <div class="col-md-6">
                                 <h4>Prescription Details</h4>
                                 <a href="#" target="_blank" id="PrescriptionLink" runat="server">
                                    <asp:Image ID="PrescriptionImage" runat="server" CssClass="img-fluid rounded border shadow-sm" style="max-height: 300px;" />
                                 </a>
                                 <p class="text-muted mt-2">Click image to view full size.</p>
                             </div>
                         </asp:PlaceHolder>
                        <div class="col-md-6">
                            <h4>Payment Screenshot</h4>
                            <asp:Image ID="PaymentScreenshotImage" runat="server" CssClass="img-fluid rounded border shadow-sm" style="max-height: 300px;" />
                        </div>
                    </div>
                     <hr />
                     <h4>Items in this Order</h4>
                    <asp:GridView ID="OrderItemsGrid" runat="server"
                        CssClass="table table-striped"
                        AutoGenerateColumns="False"
                        GridLines="None">
                        <Columns>
                            <asp:BoundField DataField="Name" HeaderText="Product Name" />
                            <asp:BoundField DataField="Quantity" HeaderText="Quantity" />
                            <asp:BoundField DataField="PricePerUnit" HeaderText="Price per Item" DataFormatString="{0:C}" />
                        </Columns>
                    </asp:GridView>
                </div>
            </div>
        </asp:PlaceHolder>
        <asp:PlaceHolder ID="ErrorView" runat="server" Visible="false">
             <div class="alert alert-danger text-center">
                <h2>Order Not Found</h2>
                <p>The order you are looking for does not exist.</p>
                <a href="ManageOrders.aspx" class="btn btn-primary">Return to All Orders</a>
            </div>
        </asp:PlaceHolder>
    </div>
</asp:Content>