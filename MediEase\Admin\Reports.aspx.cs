﻿using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web.UI;
using System.Text;

public partial class Admin_Reports : System.Web.UI.Page
{
    protected void Page_Load(object sender, EventArgs e)
    {
        // --- SECURITY CHECK ---
        if (Session["UserId"] == null || Session["UserRole"]?.ToString() != "Admin")
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        if (!IsPostBack)
        {
            // Set default date range to the current month
            txtStartDate.Text = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1).ToString("yyyy-MM-dd");
            txtEndDate.Text = DateTime.Now.ToString("yyyy-MM-dd");
            GenerateReports();
        }
    }

    protected void btnGenerateReport_Click(object sender, EventArgs e)
    {
        GenerateReports();
    }

    private void GenerateReports()
    {
        DateTime startDate;
        DateTime endDate;

        if (!DateTime.TryParse(txtStartDate.Text, out startDate) || !DateTime.TryParse(txtEndDate.Text, out endDate))
        {
            // Handle invalid date format, though TextMode=Date helps prevent this.
            return;
        }
        // Ensure end date includes the entire day
        endDate = endDate.AddDays(1).AddTicks(-1);

        string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
        using (SqlConnection con = new SqlConnection(connectionString))
        {
            try
            {
                con.Open();
                BindSalesSummary(con, startDate, endDate);
                BindBestSellingProducts(con, startDate, endDate);
                BindStockLevelReport(con); // Stock level is point-in-time, not date-range dependent
            }
            catch (Exception ex)
            {
                // Handle and log exception
                System.Diagnostics.Debug.WriteLine($"Report Generation Error: {ex.Message}");
            }
        }
    }

    private void BindSalesSummary(SqlConnection con, DateTime startDate, DateTime endDate)
    {
        string query = @"
            SELECT 
                ISNULL(SUM(TotalAmount), 0) AS TotalRevenue,
                COUNT(OrderId) AS TotalOrders
            FROM Orders 
            WHERE Status = 'Delivered' AND OrderDate BETWEEN @StartDate AND @EndDate";

        using (SqlCommand cmd = new SqlCommand(query, con))
        {
            cmd.Parameters.AddWithValue("@StartDate", startDate);
            cmd.Parameters.AddWithValue("@EndDate", endDate);

            using (SqlDataReader reader = cmd.ExecuteReader())
            {
                if (reader.Read())
                {
                    decimal totalRevenue = Convert.ToDecimal(reader["TotalRevenue"]);
                    int totalOrders = Convert.ToInt32(reader["TotalOrders"]);

                    litTotalRevenue.Text = totalRevenue.ToString("C");
                    litTotalOrders.Text = totalOrders.ToString();
                    litAvgOrderValue.Text = (totalOrders > 0) ? (totalRevenue / totalOrders).ToString("C") : "$0.00";
                }
            }
        }
    }

    private void BindBestSellingProducts(SqlConnection con, DateTime startDate, DateTime endDate)
    {
        string query = @"
            SELECT TOP 10
                m.Name,
                SUM(oi.Quantity) AS TotalQuantity,
                SUM(oi.TotalPrice) AS TotalSales
            FROM OrderItems oi
            JOIN Medicines m ON oi.MedicineId = m.MedicineId
            JOIN Orders o ON oi.OrderId = o.OrderId
            WHERE o.Status = 'Delivered' AND o.OrderDate BETWEEN @StartDate AND @EndDate
            GROUP BY m.Name
            ORDER BY TotalSales DESC";

        using (SqlDataAdapter sda = new SqlDataAdapter(query, con))
        {
            sda.SelectCommand.Parameters.AddWithValue("@StartDate", startDate);
            sda.SelectCommand.Parameters.AddWithValue("@EndDate", endDate);
            DataTable dt = new DataTable();
            sda.Fill(dt);
            gvBestSellers.DataSource = dt;
            gvBestSellers.DataBind();
        }
    }

    private void BindStockLevelReport(SqlConnection con)
    {
        // This report shows items that are currently low in stock, regardless of date range.
        string query = @"
            SELECT TOP 10
                Name,
                StockQuantity,
                ReorderLevel
            FROM Medicines
            WHERE StockQuantity <= ReorderLevel AND IsActive = 1
            ORDER BY StockQuantity ASC";

        using (SqlDataAdapter sda = new SqlDataAdapter(query, con))
        {
            DataTable dt = new DataTable();
            sda.Fill(dt);
            gvLowStock.DataSource = dt;
            gvLowStock.DataBind();
        }
    }

    protected void btnLogout_Click(object sender, EventArgs e)
    {
        Session.Clear();
        Session.Abandon();
        Response.Redirect("~/Login.aspx");
    }
}
