﻿/*
 =======================================================================
 FILE: FAQ.aspx.cs (Final Corrected Version)
 PURPOSE: Backend logic for the FAQ page. Fetches the page's content
          dynamically from the database with enhanced logging.
 PLACE IN: The root directory of your project.
 =======================================================================
*/
using System;
using System.Configuration;
using System.Data.SqlClient;
using System.Web.UI;

namespace MediEase
{
    public partial class FAQ : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                LoadFaqContent();
            }
        }

        private void LoadFaqContent()
        {
            string pageName = "FAQ";
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "SELECT ContentBody FROM CMSContent WHERE PageName = @PageName";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@PageName", pageName);
                    try
                    {
                        con.Open();
                        System.Diagnostics.Debug.WriteLine("Database connection opened for FAQ page.");
                        object result = cmd.ExecuteScalar();

                        if (result != null && result != DBNull.Value)
                        {
                            string content = result.ToString();
                            FaqContent.Text = content; // Set the text of the literal control
                            System.Diagnostics.Debug.WriteLine($"Successfully loaded FAQ content ({content.Length} characters).");
                        }
                        else
                        {
                            // This message will show if the 'FAQ' row is missing from the CMSContent table
                            FaqContent.Text = "<p class='text-center'>The FAQ content has not been set up yet. Please ask an administrator to add it.</p>";
                            System.Diagnostics.Debug.WriteLine("No content found for PageName = 'FAQ' in the database.");
                        }
                    }
                    catch (Exception ex)
                    {
                        // This message will show if there's a database connection error
                        FaqContent.Text = "<p class='text-danger text-center'>An error occurred while loading the page content. Please check the system logs.</p>";
                        System.Diagnostics.Debug.WriteLine("--- FAQ PAGE DATABASE ERROR ---");
                        System.Diagnostics.Debug.WriteLine(ex.ToString());
                    }
                }
            }
        }
    }
}