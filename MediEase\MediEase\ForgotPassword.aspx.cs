﻿/* FILE: ForgotPassword.aspx.cs */
using System;
using System.Configuration;
using System.Data.SqlClient;
using System.Net;
using System.Net.Mail;
using System.Web.UI;

namespace MediEase
{
    public partial class ForgotPassword : Page
    {
        protected void Page_Load(object sender, EventArgs e) { }

        protected void SendLinkButton_Click(object sender, EventArgs e)
        {
            if (Page.IsValid)
            {
                string email = EmailBox.Text.Trim();
                string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
                string resetToken = Guid.NewGuid().ToString();
                DateTime tokenExpiry = DateTime.Now.AddHours(1);

                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    const string query = "UPDATE Users SET ResetToken = @ResetToken, ResetTokenExpiry = @ResetTokenExpiry WHERE Email = @Email";
                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        cmd.Parameters.AddWithValue("@ResetToken", resetToken);
                        cmd.Parameters.AddWithValue("@ResetTokenExpiry", tokenExpiry);
                        cmd.Parameters.AddWithValue("@Email", email);
                        try
                        {
                            con.Open();
                            int rowsAffected = cmd.ExecuteNonQuery();
                            if (rowsAffected > 0)
                            {
                                SendPasswordResetEmail(email, resetToken);
                            }
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine("Forgot Password DB Error: " + ex.Message);
                        }
                    }
                }
                FormView.Visible = false;
                MessageView.Visible = true;
                SuccessMessage.Text = "If a matching account was found, a password reset link has been sent to your email address.";
            }
        }

        private void SendPasswordResetEmail(string toEmail, string resetToken)
        {
            try
            {
                string fromEmail = "<EMAIL>";
                string fromPassword = "ojtl edhu fqxy tevc";
                string resetUrl = Request.Url.GetLeftPart(UriPartial.Authority) + ResolveUrl("~/ResetPassword.aspx?token=" + resetToken);
                MailMessage mailMessage = new MailMessage { From = new MailAddress(fromEmail), Subject = "MediEase - Password Reset Request", IsBodyHtml = true };
                mailMessage.To.Add(toEmail);
                mailMessage.Body = $"<h2>MediEase Password Reset</h2><p>Please click the link below to set a new password:</p><p><a href='{resetUrl}'>Reset My Password</a></p><p>This link will expire in one hour.</p>";
                SmtpClient smtpClient = new SmtpClient("smtp.gmail.com", 587) { EnableSsl = true, Credentials = new NetworkCredential(fromEmail, fromPassword) };
                smtpClient.Send(mailMessage);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("Password Reset Email Send Error: " + ex.ToString());
            }
        }
    }
}