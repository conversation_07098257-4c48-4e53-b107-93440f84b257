﻿<%--
 =======================================================================
 FILE: Dashboard.aspx (Final Admin Version)
 =======================================================================
--%>
<%@ Page Title="Admin Dashboard" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="Dashboard.aspx.cs" Inherits="MediEase.Admin.Dashboard" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-5">
        <h1 class="mb-4">Admin Dashboard</h1>
        <div class="row">
            <div class="col-lg-3 col-md-6 mb-4"><div class="card text-white bg-primary shadow"><div class="card-body"><div class="d-flex justify-content-between align-items-center"><div><div class="h2 font-weight-bold"><asp:Label ID="TotalUsersLabel" runat="server" Text="0"></asp:Label></div><div class="h5">Total Users</div></div><i class="fas fa-users fa-3x"></i></div></div></div></div>
            <div class="col-lg-3 col-md-6 mb-4"><div class="card text-white bg-success shadow"><div class="card-body"><div class="d-flex justify-content-between align-items-center"><div><div class="h2 font-weight-bold"><asp:Label ID="TotalOrdersLabel" runat="server" Text="0"></asp:Label></div><div class="h5">Total Orders</div></div><i class="fas fa-shopping-cart fa-3x"></i></div></div></div></div>
            <div class="col-lg-3 col-md-6 mb-4"><div class="card text-white bg-info shadow"><div class="card-body"><div class="d-flex justify-content-between align-items-center"><div><div class="h2 font-weight-bold"><asp:Label ID="TotalProductsLabel" runat="server" Text="0"></asp:Label></div><div class="h5">Total Products</div></div><i class="fas fa-pills fa-3x"></i></div></div></div></div>
             <div class="col-lg-3 col-md-6 mb-4"><div class="card text-white bg-warning shadow"><div class="card-body"><div class="d-flex justify-content-between align-items-center"><div><div class="h2 font-weight-bold"><asp:Label ID="PendingOrdersLabel" runat="server" Text="0"></asp:Label></div><div class="h5">Pending Orders</div></div><i class="fas fa-exclamation-circle fa-3x"></i></div></div></div></div>
        </div>
        <div class="card shadow-sm">
            <div class="card-header"><h4 class="mb-0">System Management Menu</h4></div>
            <div class="list-group list-group-flush">
                <a href="ManageUsers.aspx" class="list-group-item list-group-item-action"><i class="fas fa-users-cog fa-fw mr-2"></i>Manage User Accounts</a>
                <a href="ManageProducts.aspx" class="list-group-item list-group-item-action"><i class="fas fa-edit fa-fw mr-2"></i>Manage Products, Categories & Brands</a>
                <a href="ManageOrders.aspx" class="list-group-item list-group-item-action"><i class="fas fa-tasks fa-fw mr-2"></i>View & Manage All Orders</a>
                <a href="ManageDiscounts.aspx" class="list-group-item list-group-item-action"><i class="fas fa-tags fa-fw mr-2"></i>Manage Discount Codes</a>
                <a href="ManageSettings.aspx" class="list-group-item list-group-item-action"><i class="fas fa-cogs fa-fw mr-2"></i>Configure Loyalty Program & Site Settings</a>
                <a href="ManageBlog.aspx" class="list-group-item list-group-item-action"><i class="fas fa-blog fa-fw mr-2"></i>Manage Blog Posts</a>
                <a href="ManageContent.aspx" class="list-group-item list-group-item-action"><i class="fas fa-file-alt fa-fw mr-2"></i>Manage Site Content (FAQ, etc.)</a>
                <a href="ManageChatLogs.aspx" class="list-group-item list-group-item-action"><i class="fas fa-robot fa-fw mr-2"></i>View Chatbot Logs</a>
                <a href="AuditTrail.aspx" class="list-group-item list-group-item-action"><i class="fas fa-history fa-fw mr-2"></i>View System Audit Trail</a>
                <a href="BulkUpload.aspx" class="list-group-item list-group-item-action"><i class="fas fa-file-csv fa-fw mr-2"></i>Bulk Upload Products</a>
            </div>
        </div>
    </div>
</asp:Content>