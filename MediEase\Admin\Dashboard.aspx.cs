using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web.UI;
using System.Collections.Generic;
using System.Web.Script.Serialization;

public partial class Admin_Dashboard : System.Web.UI.Page
{
    protected void Page_Load(object sender, EventArgs e)
    {
        // --- SECURITY CHECK ---
        if (Session["UserId"] == null || Session["UserRole"]?.ToString() != "Admin")
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        if (!IsPostBack)
        {
            LoadDashboardData();
        }
    }

    private void LoadDashboardData()
    {
        string firstName = Session["UserFirstName"]?.ToString() ?? "Admin";
        litWelcomeMessage.Text = $"Welcome, {firstName}!";
        litUserInitial.Text = $"<div class='w-10 h-10 rounded-full bg-teal-500 text-white flex items-center justify-center font-bold text-lg'>{firstName[0]}</div>";

        string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
        using (SqlConnection con = new SqlConnection(connectionString))
        {
            try
            {
                con.Open();
                BindDashboardStats(con);
                BindRecentActivity(con);
                LoadChartData(con);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Admin Dashboard Load Error: {ex.Message}");
                litWelcomeMessage.Text = "Error loading dashboard data.";
            }
        }
    }

    private void BindDashboardStats(SqlConnection con)
    {
        string query = @"
            SELECT 
                (SELECT ISNULL(SUM(TotalAmount), 0) FROM Orders WHERE Status = 'Delivered') AS TotalRevenue,
                (SELECT COUNT(OrderId) FROM Orders) AS TotalSales,
                (SELECT COUNT(UserId) FROM Users WHERE Role = 'Customer' AND CreatedDate >= DATEADD(day, -30, GETDATE())) AS NewCustomers,
                (SELECT COUNT(MedicineId) FROM Medicines WHERE IsActive = 1) AS TotalProducts;";

        using (SqlCommand cmd = new SqlCommand(query, con))
        {
            using (SqlDataReader reader = cmd.ExecuteReader())
            {
                if (reader.Read())
                {
                    litTotalRevenue.Text = $"{reader["TotalRevenue"]:C}";
                    litTotalSales.Text = reader["TotalSales"].ToString();
                    litNewCustomers.Text = reader["NewCustomers"].ToString();
                    litTotalProducts.Text = reader["TotalProducts"].ToString();
                }
            }
        }
    }

    private void BindRecentActivity(SqlConnection con)
    {
        string query = @"
            SELECT TOP 5 
                al.LogId, 
                al.Action, 
                al.Timestamp,
                ISNULL(u.FirstName + ' ' + u.LastName, 'System') AS UserName,
                al.EntityType,
                al.EntityId
            FROM AuditLogs al
            LEFT JOIN Users u ON al.UserId = u.UserId
            ORDER BY al.Timestamp DESC";

        using (SqlDataAdapter sda = new SqlDataAdapter(query, con))
        {
            DataTable dt = new DataTable();
            sda.Fill(dt);
            // Add a new column to hold the formatted action details
            dt.Columns.Add("ActionDetails", typeof(string));
            foreach (DataRow row in dt.Rows)
            {
                row["ActionDetails"] = $"{row["UserName"]} performed action: <strong>{row["Action"]}</strong> on {row["EntityType"]} #{row["EntityId"]}";
            }
            rptRecentActivity.DataSource = dt;
            rptRecentActivity.DataBind();
        }
    }

    private void LoadChartData(SqlConnection con)
    {
        var salesData = new Dictionary<string, decimal>();
        // Initialize last 6 months
        for (int i = 5; i >= 0; i--)
        {
            salesData.Add(DateTime.Now.AddMonths(-i).ToString("MMM yyyy"), 0);
        }

        string query = @"
            SELECT
                FORMAT(OrderDate, 'MMM yyyy') AS MonthYear,
                SUM(TotalAmount) AS MonthlySales
            FROM Orders
            WHERE Status = 'Delivered' AND OrderDate >= DATEADD(month, -6, GETDATE())
            GROUP BY FORMAT(OrderDate, 'MMM yyyy')
            ORDER BY MIN(OrderDate);";

        using (SqlCommand cmd = new SqlCommand(query, con))
        {
            using (SqlDataReader reader = cmd.ExecuteReader())
            {
                while (reader.Read())
                {
                    salesData[reader["MonthYear"].ToString()] = Convert.ToDecimal(reader["MonthlySales"]);
                }
            }
        }

        var serializer = new JavaScriptSerializer();
        var chartScript = $@"
            mySalesChart.data.labels = {serializer.Serialize(salesData.Keys)};
            mySalesChart.data.datasets[0].data = {serializer.Serialize(salesData.Values)};
            mySalesChart.update();
        ";
        ScriptManager.RegisterStartupScript(this, this.GetType(), "SalesChartScript", chartScript, true);
    }

    protected void btnLogout_Click(object sender, EventArgs e)
    {
        Session.Clear();
        Session.Abandon();
        Response.Redirect("~/Login.aspx");
    }

    protected string GetIconForAction(string action)
    {
        if (action.ToLower().Contains("create")) return "fas fa-plus-circle";
        if (action.ToLower().Contains("update")) return "fas fa-edit";
        if (action.ToLower().Contains("delete")) return "fas fa-trash-alt";
        if (action.ToLower().Contains("login")) return "fas fa-sign-in-alt";
        return "fas fa-info-circle";
    }
}
