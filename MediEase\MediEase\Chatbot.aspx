﻿<%--
 =======================================================================
 FILE: Chatbot.aspx
 PURPOSE: A standalone page for the AI medical assistant. This page
          calls the OpenRouter API directly from the browser.
 PLACE IN: The root directory of your project.
 =======================================================================
--%>
<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="Chatbot.aspx.cs" Inherits="MediEase.Chatbot" %>

<!DOCTYPE html>
<html lang="en">
<head runat="server">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MediEase - Your Friendly Medical Assistant</title>
    <style>
        body { font-family: 'Arial', sans-serif; max-width: 800px; margin: 0 auto; padding: 5px; background-color: #f0f7fb; }
        .header { text-align: center; color: #2b6cb0; margin-bottom: 5px; }
        .chat-container { background-color: white; border-radius: 12px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08); height: 260px; overflow-y: auto; padding: 5px; margin-bottom: 5px; border: 1px solid #bee3f8; }
        .message { margin-bottom: 12px; padding: 12px 16px; border-radius: 16px; max-width: 80%; word-wrap: break-word; line-height: 1.4; font-size: 15px; }
        .user-message { background-color: #ebf5ff; margin-left: auto; border-bottom-right-radius: 4px; color: #2b6cb0; }
        .bot-message { background-color: #f8fafc; margin-right: auto; border-bottom-left-radius: 4px; color: #4a5568; border-left: 2px solid #90cdf4; }
        .input-area { display: flex; gap: 4px; }
        #user-input { flex-grow: 1; padding: 12px 16px; border: 1px solid #bee3f8; border-radius: 20px; outline: none; font-size: 15px; }
        #send-button { padding: 12px 20px; background-color: #4299e1; color: white; border: none; border-radius: 20px; cursor: pointer; font-size: 15px; transition: background-color 0.2s; }
        #send-button:hover { background-color: #3182ce; }
        .typing-indicator { display: none; color: #718096; font-style: italic; margin-bottom: 12px; font-size: 14px; }
        .disclaimer { font-size: 12px; color: #718096; text-align: center; margin-top: 15px; padding: 8px; }
        .apology-message { background-color: #fff5f5; color: #c53030; border-left: 2px solid #fc8181; }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <div class="header">
            <h2 style="margin-bottom: 5px;">MediEase</h2>
        </div>
        
        <div class="chat-container" id="chat-container">
            <div class="bot-message message">
                Hi there! I'm MediEase, How can I help you?
            </div>
            <div class="typing-indicator" id="typing-indicator">MediEase is thinking...</div>
        </div>
        
        <div class="input-area">
            <input type="text" id="user-input" placeholder="Ask me anything about health..." autocomplete="off">
            <button id="send-button" type="button">Send</button>
        </div>
        
        <div class="disclaimer">
            Remember: I provide general health information, not medical advice.
        </div>
    </form>
    <script>
        const chatContainer = document.getElementById('chat-container');
        const userInput = document.getElementById('user-input');
        const sendButton = document.getElementById('send-button');
        const typingIndicator = document.getElementById('typing-indicator');

        const systemPrompt = `You are MediEase, a friendly medical assistant. Follow these rules:
        1. TONE:
        - Warm and conversational (like a helpful nurse)
        - Use simple, everyday language
        - Occasionally use friendly phrases like "That's a good question!" or "I understand your concern"
        2. RESPONSES:
        - Keep answers under 3 sentences
        - Use natural phrasing, not robotic lists
        - For medical questions, provide clear info with empathy
        3. NON-MEDICAL QUESTIONS:
        - Politely decline: "I'm sorry, I can only help with health-related questions."
        - Never mention technical details or creators
        4. SAFETY:
        - Always remind: "For personal advice, please check with your doctor."
        - Never diagnose - only provide general information`;

        function addMessage(text, isUser, isApology = false) {
            const messageDiv = document.createElement('div');
            messageDiv.classList.add('message');
            messageDiv.classList.add(isUser ? 'user-message' : 'bot-message');
            if (isApology) messageDiv.classList.add('apology-message');
            messageDiv.textContent = text;
            chatContainer.insertBefore(messageDiv, typingIndicator);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        async function sendMessage() {
            const message = userInput.value.trim();
            if (!message) return;

            addMessage(message, true);
            userInput.value = '';

            typingIndicator.style.display = 'block';
            chatContainer.scrollTop = chatContainer.scrollHeight;

            try {
                const messages = [
                    { role: "system", content: systemPrompt },
                    { role: "user", content: message }
                ];

                const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer 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',
                        'HTTP-Referer': window.location.origin, // Use origin for privacy
                        'X-Title': 'MediEase Medical Chatbot'
                    },
                    body: JSON.stringify({
                        model: "meta-llama/llama-4-maverick:free",
                        messages: messages,
                        temperature: 0.7,
                        max_tokens: 120
                    })
                });

                if (!response.ok) throw new Error(`API error: ${response.status}`);

                const data = await response.json();
                let botResponse = data.choices[0].message.content;

                typingIndicator.style.display = 'none';
                addMessage(botResponse, false);

            } catch (error) {
                typingIndicator.style.display = 'none';
                addMessage("I'm having trouble connecting right now. Please try again soon!", false, true);
                console.error("Chatbot API Error:", error);
            }
        }

        sendButton.addEventListener('click', sendMessage);
        userInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') sendMessage();
        });
        window.addEventListener('DOMContentLoaded', () => {
            userInput.focus();
        });
    </script>
</body>
</html>