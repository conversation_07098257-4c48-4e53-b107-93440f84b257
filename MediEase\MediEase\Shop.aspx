﻿<%--
 =======================================================================
 FILE: Shop.aspx (Final Corrected Version)
 PURPOSE: Fixes the scrolling issue where the filter menu overlaps the
          main navigation bar.
 =======================================================================
--%>
<%@ Page Title="Shop" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="Shop.aspx.cs" Inherits="MediEase.Shop" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <%-- ADDED: Page-specific style to fix the z-index issue --%>
    <style>
        .sticky-top {
            z-index: 1030; /* Ensures navbar is above other sticky elements */
        }
    </style>

    <div class="container-fluid">
        <div class="row">
            <!-- Filter Sidebar -->
            <div class="col-lg-3">
                <div class="card sticky-top" style="top: 80px;">
                    <div class="card-body">
                        <h4 class="card-title">Filters</h4>
                        <hr />
                        <div class="form-group">
                            <label>Search by Name</label>
                            <div class="input-group">
                                <asp:TextBox ID="SearchBox" runat="server" CssClass="form-control" placeholder="e.g., Paracetamol"></asp:TextBox>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>Brand</label>
                            <asp:DropDownList ID="BrandFilter" runat="server" CssClass="form-control"></asp:DropDownList>
                        </div>
                        <div class="form-group">
                            <label>Price Range</label>
                            <div class="form-row">
                                <div class="col">
                                    <asp:TextBox ID="MinPriceBox" runat="server" CssClass="form-control" placeholder="Min" TextMode="Number"></asp:TextBox>
                                </div>
                                <div class="col">
                                    <asp:TextBox ID="MaxPriceBox" runat="server" CssClass="form-control" placeholder="Max" TextMode="Number"></asp:TextBox>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>Form</label>
                             <asp:DropDownList ID="FormFilter" runat="server" CssClass="form-control"></asp:DropDownList>
                        </div>
                        <asp:Button ID="ApplyFiltersButton" runat="server" Text="Apply Filters" CssClass="btn btn-primary btn-block" OnClick="ApplyFiltersButton_Click" />
                        <a href="Shop.aspx" class="btn btn-secondary btn-block mt-2">Clear All Filters</a>
                        <hr />
                        <h5>Categories</h5>
                        <div class="list-group list-group-flush">
                             <asp:HyperLink NavigateUrl="~/Shop.aspx" Text="All Categories" CssClass="list-group-item list-group-item-action" runat="server" ID="AllCategoriesLink" />
                            <asp:Repeater ID="CategoryRepeater" runat="server">
                                <ItemTemplate>
                                    <asp:HyperLink NavigateUrl='<%# "~/Shop.aspx?category=" + Eval("CategoryID") %>' 
                                        Text='<%# Eval("CategoryName") %>'
                                        CssClass="list-group-item list-group-item-action" 
                                        runat="server" />
                                </ItemTemplate>
                            </asp:Repeater>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Products Grid -->
            <div class="col-lg-9">
                <h2 id="PageTitle" runat="server">All Products</h2>
                <hr />
                <div class="row">
                    <asp:Repeater ID="ProductRepeater" runat="server" OnItemDataBound="ProductRepeater_ItemDataBound">
                        <ItemTemplate>
                            <div class="col-12 col-md-6 col-lg-4 mb-4">
                                <div class="card h-100 shadow-sm">
                                    <asp:Image ID="ProductImage" runat="server" ImageUrl='<%# Eval("ImageUrl") %>' CssClass="card-img-top" style="height: 200px; object-fit: cover;" />
                                    <div class="card-body d-flex flex-column">
                                        <h5 class="card-title"><%# Eval("Name") %></h5>
                                        <p class="card-text text-muted"><%# Eval("BrandName") %></p>
                                        <div class="mt-auto">
                                             <p class="card-text font-weight-bold h5">$<%# Eval("Price", "{0:N2}") %></p>
                                             <a href='ProductDetails.aspx?ProductID=<%# Eval("ProductID") %>' class="btn btn-primary btn-block">View Details</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </ItemTemplate>
                    </asp:Repeater>
                </div>
                 <asp:PlaceHolder ID="NoProductsMessage" runat="server" Visible="false">
                    <div class="alert alert-info text-center mt-5">
                        <p>No products match your search or filter criteria. Please try again.</p>
                    </div>
                </asp:PlaceHolder>
            </div>
        </div>
    </div>
</asp:Content>