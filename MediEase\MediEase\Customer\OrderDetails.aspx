﻿<%--
 =======================================================================
 FILE: OrderDetails.aspx (Final Version)
 =======================================================================
--%>
<%@ Page Title="Order Details" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="OrderDetails.aspx.cs" Inherits="MediEase.Customer.OrderDetails" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-5">
        <asp:PlaceHolder ID="OrderDetailsView" runat="server">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2>Order Details</h2>
                            <p class="mb-0">Order #<asp:Label ID="OrderIDLabel" runat="server"></asp:Label></p>
                        </div>
                        <a href="Dashboard.aspx" class="btn btn-outline-secondary">&larr; Back to Order History</a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <strong>Order Date:</strong>
                            <p><asp:Label ID="OrderDateLabel" runat="server"></asp:Label></p>
                        </div>
                        <div class="col-md-4">
                            <strong>Order Total:</strong>
                            <p><asp:Label ID="OrderTotalLabel" runat="server"></asp:Label></p>
                        </div>
                        <div class="col-md-4">
                            <strong>Order Status:</strong>
                            <p>
                                <asp:Label ID="OrderStatusLabel" runat="server" CssClass="badge badge-pill"></asp:Label>
                            </p>
                        </div>
                    </div>
                    <hr />
                    <div class="row">
                        <div class="col-md-12">
                            <strong>Shipping Address:</strong>
                            <p><asp:Label ID="ShippingAddressLabel" runat="server"></asp:Label></p>
                        </div>
                    </div>
                     <hr />
                     <h4>Items in this Order</h4>
                    <asp:GridView ID="OrderItemsGrid" runat="server"
                        CssClass="table table-striped"
                        AutoGenerateColumns="False"
                        GridLines="None">
                        <Columns>
                            <asp:BoundField DataField="Name" HeaderText="Product Name" />
                            <asp:BoundField DataField="Quantity" HeaderText="Quantity" />
                            <asp:BoundField DataField="PricePerUnit" HeaderText="Price per Item" DataFormatString="{0:C}" />
                        </Columns>
                    </asp:GridView>
                </div>
            </div>
        </asp:PlaceHolder>

        <asp:PlaceHolder ID="ErrorView" runat="server" Visible="false">
             <div class="alert alert-danger text-center">
                <h2>Order Not Found</h2>
                <p>The order you are looking for does not exist or you do not have permission to view it.</p>
                <a href="Dashboard.aspx" class="btn btn-primary">Return to Order History</a>
            </div>
        </asp:PlaceHolder>
    </div>
</asp:Content>