﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="CustomerOrderDetail.aspx.cs" Inherits="Customer_CustomerOrderDetail" %>

<!DOCTYPE html>
<html lang="en">
<head runat="server">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Detail - MediEase</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <style>
        body { font-family: 'Inter', sans-serif; }
        .sidebar-link { display: flex; align-items: center; padding: 0.75rem 1rem; border-radius: 0.5rem; transition: background-color 0.2s, color 0.2s; }
        .sidebar-link:hover, .sidebar-link.active { background-color: #0d9488; color: white; }
        .sidebar-link i { width: 1.25rem; margin-right: 0.75rem; }
    </style>
</head>
<body class="bg-gray-100">
    <form id="form1" runat="server">
        <div class="flex h-screen bg-gray-100">
            <!-- Sidebar -->
            <aside class="w-64 bg-teal-600 text-white flex-col p-4 hidden md:flex">
                <div class="flex items-center space-x-2 pb-4 border-b border-teal-500">
                    <i class="fas fa-pills text-3xl"></i><span class="text-2xl font-bold">MediEase</span>
                </div>
                <nav class="mt-6 flex-grow">
                    <a href="Dashboard.aspx" class="sidebar-link"><i class="fas fa-tachometer-alt fa-fw"></i><span>Dashboard</span></a>
                    <a href="Profile.aspx" class="sidebar-link"><i class="fas fa-user-edit fa-fw"></i><span>My Profile</span></a>
                    <a href="OrderHistory.aspx" class="sidebar-link active"><i class="fas fa-history fa-fw"></i><span>Order History</span></a>
                    <a href="Prescriptions.aspx" class="sidebar-link"><i class="fas fa-file-prescription fa-fw"></i><span>Prescriptions</span></a>
                    <a href="Reminders.aspx" class="sidebar-link"><i class="fas fa-bell fa-fw"></i><span>Reminders</span></a>
                </nav>
                <div class="pt-4 border-t border-teal-500">
                    <asp:LinkButton ID="btnLogout" runat="server" OnClick="btnLogout_Click" CssClass="sidebar-link w-full"><i class="fas fa-sign-out-alt fa-fw"></i><span>Logout</span></asp:LinkButton>
                </div>
            </aside>

            <!-- Main Content -->
            <main class="flex-1 flex flex-col">
                <header class="bg-white shadow-sm p-4">
                    <div class="flex items-center">
                        <a href="OrderHistory.aspx" class="text-gray-500 hover:text-gray-700 mr-4"><i class="fas fa-arrow-left"></i></a>
                        <h1 class="text-2xl font-bold text-gray-800">
                            Order Detail: <asp:Literal ID="litOrderNumber" runat="server"></asp:Literal>
                        </h1>
                    </div>
                </header>

                <div class="flex-1 p-6 overflow-y-auto">
                    <!-- Server Message -->
                    <asp:Literal ID="litMessage" runat="server"></asp:Literal>

                    <div class="bg-white p-8 rounded-lg shadow">
                        <!-- Order Header -->
                        <div class="flex flex-col md:flex-row justify-between items-start md:items-center border-b pb-4 mb-6">
                            <div>
                                <p class="text-sm text-gray-500">Order placed on <asp:Literal ID="litOrderDate" runat="server"></asp:Literal></p>
                                <p class="text-sm text-gray-500">Total: <asp:Literal ID="litTotalAmount" runat="server"></asp:Literal></p>
                            </div>
                            <div>
                                <span class="text-sm font-semibold">Status:</span>
                                <asp:Literal ID="litStatus" runat="server"></asp:Literal>
                            </div>
                        </div>

                        <!-- Order Items -->
                        <div class="mb-6">
                            <h2 class="text-lg font-semibold text-gray-800 mb-4">Items in this Order</h2>
                            <asp:Repeater ID="rptOrderItems" runat="server">
                                <ItemTemplate>
                                    <div class="flex items-center py-4 border-b last:border-0">
                                        <asp:Image ID="imgProduct" runat="server" ImageUrl='<%# Eval("ImageUrl", "~/Uploads/Products/{0}") %>' CssClass="h-16 w-16 rounded-md object-cover mr-4" />
                                        <div class="flex-grow">
                                            <p class="font-semibold text-gray-900"><%# Eval("Name") %></p>
                                            <p class="text-sm text-gray-500">Qty: <%# Eval("Quantity") %></p>
                                        </div>
                                        <div class="text-right">
                                            <p class="font-semibold"><%# Eval("TotalPrice", "{0:C}") %></p>
                                        </div>
                                    </div>
                                </ItemTemplate>
                            </asp:Repeater>
                        </div>
                        
                        <!-- Shipping and Summary -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h3 class="font-semibold text-gray-800 mb-2">Shipping Address</h3>
                                <div class="text-sm text-gray-600">
                                    <asp:Literal ID="litShippingAddress" runat="server"></asp:Literal>
                                </div>
                            </div>
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <h3 class="font-semibold text-gray-800 mb-2">Order Summary</h3>
                                <div class="space-y-1 text-sm">
                                    <div class="flex justify-between"><span class="text-gray-600">Subtotal:</span><span><asp:Literal ID="litSubtotal" runat="server"></asp:Literal></span></div>
                                    <div class="flex justify-between"><span class="text-gray-600">Shipping:</span><span><asp:Literal ID="litShippingCost" runat="server"></asp:Literal></span></div>
                                    <div class="flex justify-between"><span class="text-gray-600">Tax:</span><span><asp:Literal ID="litTax" runat="server"></asp:Literal></span></div>
                                    <div class="flex justify-between font-bold border-t pt-2 mt-2"><span class="text-gray-800">Total:</span><span><asp:Literal ID="litGrandTotal" runat="server"></asp:Literal></span></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </form>
</body>
</html>
