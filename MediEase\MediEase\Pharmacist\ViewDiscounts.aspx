﻿<%--
 =======================================================================
 FILE: ViewDiscounts.aspx
 PURPOSE: Allows a pharmacist to view all discount codes in the system.
 PLACE IN: The 'Pharmacist' folder in your project root.
 =======================================================================
--%>
<%@ Page Title="View Discount Codes" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="ViewDiscounts.aspx.cs" Inherits="MediEase.Pharmacist.ViewDiscounts" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>All Discount Codes</h1>
            <a href="Dashboard.aspx" class="btn btn-outline-secondary">&larr; Back to Dashboard</a>
        </div>

        <div class="card shadow-sm">
            <div class="card-header">
                <h4 class="mb-0">System Discount List</h4>
            </div>
            <div class="card-body">
                 <asp:GridView ID="DiscountsGrid" runat="server"
                    CssClass="table table-hover table-striped"
                    AutoGenerateColumns="False"
                    DataKeyNames="DiscountID"
                    GridLines="None"
                    AllowPaging="True"
                    OnPageIndexChanging="DiscountsGrid_PageIndexChanging"
                    PageSize="15">
                    <Columns>
                        <asp:BoundField DataField="DiscountCode" HeaderText="Code" />
                        <asp:BoundField DataField="Description" HeaderText="Description" />
                        <asp:BoundField DataField="DiscountPercentage" HeaderText="Percentage" DataFormatString="{0:F2}%" />
                        <asp:BoundField DataField="StartDate" HeaderText="Start Date" DataFormatString="{0:d}" />
                        <asp:BoundField DataField="EndDate" HeaderText="End Date" DataFormatString="{0:d}" />
                        <asp:TemplateField HeaderText="Status">
                             <ItemTemplate>
                                <span class='badge <%# Convert.ToBoolean(Eval("IsActive")) ? "badge-success" : "badge-danger" %>'>
                                    <%# Convert.ToBoolean(Eval("IsActive")) ? "Active" : "Inactive" %>
                                </span>
                            </ItemTemplate>
                        </asp:TemplateField>
                    </Columns>
                     <EmptyDataTemplate>
                        <div class="alert alert-info text-center">
                            No discount codes have been created by an administrator yet.
                        </div>
                    </EmptyDataTemplate>
                 </asp:GridView>
            </div>
        </div>
    </div>
</asp:Content>