﻿using System;
using System.Web.UI;

namespace MediEase
{
    public partial class OrderSuccess : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                if (Request.QueryString["OrderID"] != null)
                {
                    string orderId = Request.QueryString["OrderID"];
                    OrderIDLabel.Text = Server.HtmlEncode(orderId);
                    SuccessView.Visible = true;
                    ErrorView.Visible = false;
                }
                else
                {
                    ShowErrorView();
                }
            }
        }

        private void ShowErrorView()
        {
            SuccessView.Visible = false;
            ErrorView.Visible = true;
        }
    }
}