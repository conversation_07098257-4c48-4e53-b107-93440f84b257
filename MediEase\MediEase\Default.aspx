﻿<%--
 =======================================================================
 FILE: Default.aspx (Final Version with Images)
 =======================================================================
--%>
<%@ Page Title="Welcome" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="Default.aspx.cs" Inherits="MediEase.Default" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div id="promoCarousel" class="carousel slide" data-ride="carousel">
        <ol class="carousel-indicators">
            <li data-target="#promoCarousel" data-slide-to="0" class="active"></li>
            <li data-target="#promoCarousel" data-slide-to="1"></li>
            <li data-target="#promoCarousel" data-slide-to="2"></li>
        </ol>
        <div class="carousel-inner rounded">
            <div class="carousel-item active">
                <img src="https://placehold.co/1200x400/007BFF/FFFFFF?text=Welcome+to+MediEase" class="d-block w-100" alt="Promotion 1">
                <div class="carousel-caption d-none d-md-block"><h5>Your Trusted Online Pharmacy</h5><p>Quality medicines delivered to your doorstep.</p></div>
            </div>
            <div class="carousel-item">
                <img src="https://placehold.co/1200x400/28A745/FFFFFF?text=Seasonal+Discounts" class="d-block w-100" alt="Promotion 2">
                 <div class="carousel-caption d-none d-md-block"><h5>Up to 20% Off</h5><p>Check out our special offers on healthcare products.</p></div>
            </div>
            <div class="carousel-item">
                <img src="https://placehold.co/1200x400/6C757D/FFFFFF?text=Fast+Delivery" class="d-block w-100" alt="Promotion 3">
                 <div class="carousel-caption d-none d-md-block"><h5>Nationwide Shipping</h5><p>Quick and reliable delivery services.</p></div>
            </div>
        </div>
        <a class="carousel-control-prev" href="#promoCarousel" role="button" data-slide="prev"><span class="carousel-control-prev-icon" aria-hidden="true"></span><span class="sr-only">Previous</span></a>
        <a class="carousel-control-next" href="#promoCarousel" role="button" data-slide="next"><span class="carousel-control-next-icon" aria-hidden="true"></span><span class="sr-only">Next</span></a>
    </div>

    <div class="mt-5">
        <h2 class="text-center">Featured Products</h2>
        <hr class="w-50 mx-auto" />
        <div class="row">
            <asp:Repeater ID="FeaturedProductsRepeater" runat="server" OnItemDataBound="FeaturedProductsRepeater_ItemDataBound">
                <ItemTemplate>
                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-4">
                        <div class="card h-100 shadow-sm">
                            <asp:Image ID="ProductImage" runat="server" ImageUrl='<%# Eval("ImageUrl") %>' CssClass="card-img-top" style="height: 200px; object-fit: cover;" />
                            <div class="card-body d-flex flex-column">
                                <h5 class="card-title"><%# Eval("Name") %></h5>
                                <p class="card-text text-muted"><%# Eval("BrandName") %></p>
                                <div class="mt-auto">
                                     <p class="card-text font-weight-bold h5">$<%# Eval("Price", "{0:N2}") %></p>
                                     <a href='ProductDetails.aspx?ProductID=<%# Eval("ProductID") %>' class="btn btn-primary btn-block">View Details</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </ItemTemplate>
            </asp:Repeater>
        </div>
    </div>
    <asp:PlaceHolder ID="NoProductsMessage" runat="server" Visible="false">
        <div class="alert alert-info text-center mt-5"><p>No featured products are available at the moment. Please check back later.</p></div>
    </asp:PlaceHolder>
</asp:Content>