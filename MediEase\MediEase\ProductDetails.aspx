﻿<%--
 =======================================================================
 FILE: ProductDetails.aspx (Definitive Final Version)
 =======================================================================
--%>
<%@ Page Title="Product Details" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="ProductDetails.aspx.cs" Inherits="MediEase.ProductDetails" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-5">
        <asp:PlaceHolder ID="ProductView" runat="server">
            <div class="row">
                <div class="col-md-6"><asp:Image ID="ProductImage" runat="server" CssClass="img-fluid rounded shadow-sm" /></div>
                <div class="col-md-6">
                    <h1 runat="server" id="ProductName">Product Name</h1>
                    <h4 class="text-muted" runat="server" id="ProductBrand">Brand Name</h4>
                    <div class="d-flex align-items-center my-2"><span class="text-warning mr-2"><asp:Literal ID="StarRatingSummary" runat="server"></asp:Literal></span><span class="text-muted">(<asp:Literal ID="ReviewCount" runat="server">0</asp:Literal> reviews)</span></div>
                    <p class="mt-3" runat="server" id="ProductDescription">Product description goes here.</p>
                    <hr />
                    <div class="bg-light p-3 rounded"><p class="mb-2"><strong>Strength:</strong> <asp:Label ID="ProductStrength" runat="server"></asp:Label></p><p class="mb-2"><strong>Form:</strong> <asp:Label ID="ProductForm" runat="server"></asp:Label></p><asp:Panel ID="PrescriptionPanel" runat="server" Visible="false" CssClass="alert alert-warning mt-3"><i class="fas fa-prescription"></i> <strong>A prescription is required for this item.</strong></asp:Panel></div>
                    <hr />
                    <h2 class="text-primary" runat="server" id="ProductPrice">$0.00</h2>
                    <div class="form-row align-items-center mt-4"><div class="col-auto"><label for="QuantityBox" class="font-weight-bold">Quantity:</label></div><div class="col-auto"><asp:TextBox ID="QuantityBox" runat="server" Text="1" CssClass="form-control" TextMode="Number" style="width: 80px;"></asp:TextBox></div></div>
                    <div class="mt-4"><asp:Button ID="AddToCartButton" runat="server" Text="Add to Cart" CssClass="btn btn-primary btn-lg" OnClick="AddToCartButton_Click" /></div>
                </div>
            </div>

            <asp:PlaceHolder ID="AIRecommendationsView" runat="server" Visible="false">
                 <div class="row mt-5"><div class="col-12"><div class="card shadow-sm"><div class="card-header bg-light"><h4 class="mb-0"><i class="fas fa-robot mr-2"></i>AI Suggestions</h4></div><div class="card-body"><p>Based on this product, our AI assistant suggests:</p><asp:Literal ID="AIRecommendationsLiteral" runat="server"></asp:Literal></div></div></div></div>
            </asp:PlaceHolder>

            <asp:PlaceHolder ID="ComparisonView" runat="server" Visible="false">
                <div class="row mt-5">
                    <div class="col-12">
                        <h3 class="mb-3">Compare Similar Products</h3>
                        <div class="card shadow-sm">
                            <div class="card-body table-responsive">
                                <asp:GridView ID="ComparisonGrid" runat="server" CssClass="table table-bordered table-hover text-center" AutoGenerateColumns="False" GridLines="None" OnRowDataBound="ComparisonGrid_RowDataBound">
                                    <Columns>
                                        <asp:TemplateField HeaderText="Product"><ItemTemplate><asp:Image ID="CompareProductImage" runat="server" ImageUrl='<%# Eval("ImageUrl") %>' Width="80" Height="80" CssClass="rounded mb-2" /><br /><strong><%# Eval("Name") %></strong></ItemTemplate></asp:TemplateField>
                                        <asp:BoundField DataField="BrandName" HeaderText="Brand" /><asp:BoundField DataField="Form" HeaderText="Form" /><asp:BoundField DataField="Strength" HeaderText="Strength" /><asp:TemplateField HeaderText="Price"><ItemTemplate><span class="h5 font-weight-bold"><%# Eval("Price", "{0:C}") %></span></ItemTemplate></asp:TemplateField>
                                        <asp:TemplateField><ItemTemplate><a href='ProductDetails.aspx?ProductID=<%# Eval("ProductID") %>' class="btn btn-outline-primary">View</a></ItemTemplate></asp:TemplateField>
                                    </Columns>
                                </asp:GridView>
                            </div>
                        </div>
                    </div>
                </div>
            </asp:PlaceHolder>

            <div class="row mt-5">
                <div class="col-12">
                    <div class="card shadow-sm"><div class="card-header"><h4 class="mb-0">Ratings & Reviews</h4></div>
                        <div class="card-body">
                            <asp:PlaceHolder ID="ReviewFormView" runat="server" Visible="false">
                                <h5>Write a Review</h5>
                                <div class="form-group"><label>Your Rating</label><asp:DropDownList ID="RatingDropDown" runat="server" CssClass="form-control" style="max-width: 200px;"><asp:ListItem Value="5">5 Stars (Excellent)</asp:ListItem><asp:ListItem Value="4">4 Stars (Good)</asp:ListItem><asp:ListItem Value="3">3 Stars (Average)</asp:ListItem><asp:ListItem Value="2">2 Stars (Fair)</asp:ListItem><asp:ListItem Value="1">1 Star (Poor)</asp:ListItem></asp:DropDownList></div>
                                <div class="form-group"><label>Your Review</label><asp:TextBox ID="ReviewCommentBox" runat="server" TextMode="MultiLine" Rows="4" CssClass="form-control"></asp:TextBox><asp:RequiredFieldValidator runat="server" ControlToValidate="ReviewCommentBox" ErrorMessage="A comment is required." CssClass="text-danger" Display="Dynamic" /></div>
                                <asp:Button ID="SubmitReviewButton" runat="server" Text="Submit Review" CssClass="btn btn-primary" OnClick="SubmitReviewButton_Click" /><hr />
                            </asp:PlaceHolder>
                            <asp:PlaceHolder ID="AlreadyReviewedMessage" runat="server" Visible="false"><div class="alert alert-info">You have already reviewed this product.</div></asp:PlaceHolder>
                            <asp:Repeater ID="ReviewsRepeater" runat="server"><HeaderTemplate><h5 class="mb-3">Customer Reviews</h5></HeaderTemplate><ItemTemplate><div class="media mb-3"><div class="mr-3"><i class="fas fa-user-circle fa-2x text-muted"></i></div><div class="media-body"><h6 class="mt-0"><%# Eval("CustomerName") %></h6><p class="text-warning mb-0"><%# GetStarRating(Convert.ToInt32(Eval("Rating"))) %></p><p><%# Eval("Comment") %></p><small class="text-muted">Reviewed on <%# Convert.ToDateTime(Eval("ReviewDate")).ToString("MMMM dd,<x_bin_534>") %></small></div></div><hr /></ItemTemplate></asp:Repeater>
                            <asp:PlaceHolder ID="NoReviewsMessage" runat="server" Visible="false"><div class="text-center text-muted"><p>There are no reviews for this product yet.</p></div></asp:PlaceHolder>
                        </div>
                    </div>
                </div>
            </div>
        </asp:PlaceHolder>
        <asp:PlaceHolder ID="ErrorView" runat="server" Visible="false"><div class="alert alert-danger text-center"><h2>Product Not Found</h2><p>The product you are looking for does not exist or is no longer available.</p><a href="Shop.aspx" class="btn btn-primary">Return to Shop</a></div></asp:PlaceHolder>
    </div>
</asp:Content>