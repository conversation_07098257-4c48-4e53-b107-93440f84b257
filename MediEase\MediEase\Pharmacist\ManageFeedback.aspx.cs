﻿/*
 =======================================================================
 FILE: ManageFeedback.aspx.cs
 PURPOSE: Backend logic for the pharmacist's feedback management page.
          Handles filtering feedback by Open/Resolved status.
 PLACE IN: The 'Pharmacist' folder in your project root.
 =======================================================================
*/
using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace MediEase.Pharmacist
{
    public partial class ManageFeedback : Page
    {
        // ViewState variable to keep track of the current filter (Open/Resolved)
        private string CurrentView
        {
            get { return ViewState["CurrentView"] as string ?? "Open"; }
            set { ViewState["CurrentView"] = value; }
        }

        protected void Page_Load(object sender, EventArgs e)
        {
            if (Session["UserID"] == null || Session["RoleName"]?.ToString() != "Pharmacist")
            {
                Response.Redirect("~/Login.aspx");
                return;
            }

            if (!IsPostBack)
            {
                BindFeedbackGrid();
                UpdateActiveTab();
            }
        }

        private void BindFeedbackGrid()
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                var queryBuilder = new StringBuilder(@"
                    SELECT 
                        f.FeedbackID, 
                        f.Subject, 
                        f.SubmissionDate,
                        up.FirstName + ' ' + up.LastName AS CustomerName
                    FROM 
                        Feedback f
                    INNER JOIN 
                        UserProfiles up ON f.CustomerID = up.UserID
                    WHERE 
                        f.Status = @Status
                    ORDER BY 
                        f.SubmissionDate DESC;
                ");

                var cmd = new SqlCommand(queryBuilder.ToString(), con);
                cmd.Parameters.AddWithValue("@Status", CurrentView);

                try
                {
                    con.Open();
                    SqlDataAdapter sda = new SqlDataAdapter(cmd);
                    DataTable dt = new DataTable();
                    sda.Fill(dt);
                    FeedbackGrid.DataSource = dt;
                    FeedbackGrid.DataBind();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine("Error binding feedback: " + ex.Message);
                }
            }
        }

        protected void Tab_Click(object sender, EventArgs e)
        {
            LinkButton clickedTab = (LinkButton)sender;
            CurrentView = clickedTab.CommandArgument;
            FeedbackGrid.PageIndex = 0; // Reset to first page on tab change
            BindFeedbackGrid();
            UpdateActiveTab();
        }

        private void UpdateActiveTab()
        {
            // Update the CSS classes to show which tab is active
            if (CurrentView == "Open")
            {
                OpenTab.CssClass = "nav-link active";
                ResolvedTab.CssClass = "nav-link";
            }
            else
            {
                OpenTab.CssClass = "nav-link";
                ResolvedTab.CssClass = "nav-link active";
            }
        }

        protected void FeedbackGrid_PageIndexChanging(object sender, GridViewPageEventArgs e)
        {
            FeedbackGrid.PageIndex = e.NewPageIndex;
            BindFeedbackGrid();
        }
    }
}