﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="Profile.aspx.cs" Inherits="Customer_Profile" %>

<!DOCTYPE html>
<html lang="en">
<head runat="server">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Profile - MediEase</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <style>
        body { font-family: 'Inter', sans-serif; }
        .sidebar-link { display: flex; align-items: center; padding: 0.75rem 1rem; border-radius: 0.5rem; transition: background-color 0.2s, color 0.2s; }
        .sidebar-link:hover, .sidebar-link.active { background-color: #0d9488; color: white; }
        .sidebar-link i { width: 1.25rem; margin-right: 0.75rem; }
        .validation-error { color: #D32F2F; font-size: 0.875rem; margin-top: 0.25rem; }
    </style>
</head>
<body class="bg-gray-100">
    <form id="form1" runat="server">
        <div class="flex h-screen bg-gray-100">
            <!-- Sidebar -->
            <aside class="w-64 bg-teal-600 text-white flex-col p-4 hidden md:flex">
                <div class="flex items-center space-x-2 pb-4 border-b border-teal-500">
                    <i class="fas fa-pills text-3xl"></i><span class="text-2xl font-bold">MediEase</span>
                </div>
                <nav class="mt-6 flex-grow">
                    <a href="Dashboard.aspx" class="sidebar-link"><i class="fas fa-tachometer-alt fa-fw"></i><span>Dashboard</span></a>
                    <a href="Profile.aspx" class="sidebar-link active"><i class="fas fa-user-edit fa-fw"></i><span>My Profile</span></a>
                    <a href="OrderHistory.aspx" class="sidebar-link"><i class="fas fa-history fa-fw"></i><span>Order History</span></a>
                    <a href="Prescriptions.aspx" class="sidebar-link"><i class="fas fa-file-prescription fa-fw"></i><span>Prescriptions</span></a>
                    <a href="Reminders.aspx" class="sidebar-link"><i class="fas fa-bell fa-fw"></i><span>Reminders</span></a>
                </nav>
                <div class="pt-4 border-t border-teal-500">
                    <asp:LinkButton ID="btnLogout" runat="server" OnClick="btnLogout_Click" CssClass="sidebar-link w-full"><i class="fas fa-sign-out-alt fa-fw"></i><span>Logout</span></asp:LinkButton>
                </div>
            </aside>

            <!-- Main Content -->
            <main class="flex-1 flex flex-col">
                <header class="bg-white shadow-sm p-4">
                    <h1 class="text-2xl font-bold text-gray-800">My Profile</h1>
                </header>

                <div class="flex-1 p-6 overflow-y-auto">
                    <!-- Server Message -->
                    <asp:Literal ID="litMessage" runat="server"></asp:Literal>

                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <!-- Left Column: Profile Details -->
                        <div class="lg:col-span-2 bg-white p-8 rounded-lg shadow">
                            <h2 class="text-xl font-bold text-gray-800 mb-6">Personal Information</h2>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">First Name</label>
                                    <asp:TextBox ID="txtFirstName" runat="server" CssClass="mt-1 block w-full input-style"></asp:TextBox>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Last Name</label>
                                    <asp:TextBox ID="txtLastName" runat="server" CssClass="mt-1 block w-full input-style"></asp:TextBox>
                                </div>
                                <div class="md:col-span-2">
                                    <label class="block text-sm font-medium text-gray-700">Email Address</label>
                                    <asp:TextBox ID="txtEmail" runat="server" ReadOnly="true" CssClass="mt-1 block w-full input-style bg-gray-100 cursor-not-allowed"></asp:TextBox>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Phone Number</label>
                                    <asp:TextBox ID="txtPhoneNumber" runat="server" TextMode="Phone" CssClass="mt-1 block w-full input-style"></asp:TextBox>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Date of Birth</label>
                                    <asp:TextBox ID="txtDateOfBirth" runat="server" TextMode="Date" CssClass="mt-1 block w-full input-style"></asp:TextBox>
                                </div>
                                 <div class="md:col-span-2">
                                    <label class="block text-sm font-medium text-gray-700">Address</label>
                                    <asp:TextBox ID="txtAddress" runat="server" CssClass="mt-1 block w-full input-style"></asp:TextBox>
                                </div>
                                 <div>
                                    <label class="block text-sm font-medium text-gray-700">City</label>
                                    <asp:TextBox ID="txtCity" runat="server" CssClass="mt-1 block w-full input-style"></asp:TextBox>
                                </div>
                                 <div>
                                    <label class="block text-sm font-medium text-gray-700">State</label>
                                    <asp:TextBox ID="txtState" runat="server" CssClass="mt-1 block w-full input-style"></asp:TextBox>
                                </div>
                                 <div>
                                    <label class="block text-sm font-medium text-gray-700">Postal Code</label>
                                    <asp:TextBox ID="txtPostalCode" runat="server" CssClass="mt-1 block w-full input-style"></asp:TextBox>
                                </div>
                                 <div>
                                    <label class="block text-sm font-medium text-gray-700">Gender</label>
                                    <asp:DropDownList ID="ddlGender" runat="server" CssClass="mt-1 block w-full input-style">
                                        <asp:ListItem Text="-- Select --" Value=""></asp:ListItem>
                                        <asp:ListItem Text="Male" Value="Male"></asp:ListItem>
                                        <asp:ListItem Text="Female" Value="Female"></asp:ListItem>
                                        <asp:ListItem Text="Other" Value="Other"></asp:ListItem>
                                    </asp:DropDownList>
                                </div>
                            </div>
                            <div class="mt-6 text-right">
                                <asp:Button ID="btnSaveChanges" runat="server" Text="Save Changes" OnClick="btnSaveChanges_Click" CssClass="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-teal-600 hover:bg-teal-700"/>
                            </div>
                        </div>

                        <!-- Right Column: Change Password -->
                        <div class="lg:col-span-1 bg-white p-8 rounded-lg shadow">
                             <h2 class="text-xl font-bold text-gray-800 mb-6">Change Password</h2>
                             <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Current Password</label>
                                    <asp:TextBox ID="txtCurrentPassword" runat="server" TextMode="Password" CssClass="mt-1 block w-full input-style"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvCurrentPassword" runat="server" ControlToValidate="txtCurrentPassword" ErrorMessage="Current password is required." CssClass="validation-error" Display="Dynamic" ValidationGroup="PasswordGroup"></asp:RequiredFieldValidator>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">New Password</label>
                                    <asp:TextBox ID="txtNewPassword" runat="server" TextMode="Password" CssClass="mt-1 block w-full input-style"></asp:TextBox>
                                     <asp:RequiredFieldValidator ID="rfvNewPassword" runat="server" ControlToValidate="txtNewPassword" ErrorMessage="New password is required." CssClass="validation-error" Display="Dynamic" ValidationGroup="PasswordGroup"></asp:RequiredFieldValidator>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Confirm New Password</label>
                                    <asp:TextBox ID="txtConfirmPassword" runat="server" TextMode="Password" CssClass="mt-1 block w-full input-style"></asp:TextBox>
                                     <asp:CompareValidator ID="cvConfirmPassword" runat="server" ControlToValidate="txtConfirmPassword" ControlToCompare="txtNewPassword" Operator="Equal" ErrorMessage="Passwords do not match." CssClass="validation-error" Display="Dynamic" ValidationGroup="PasswordGroup"></asp:CompareValidator>
                                </div>
                             </div>
                             <div class="mt-6 text-right">
                                <asp:Button ID="btnChangePassword" runat="server" Text="Change Password" OnClick="btnChangePassword_Click" ValidationGroup="PasswordGroup" CssClass="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-gray-700 hover:bg-gray-800"/>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </form>
</body>
</html>
