﻿<%--
 =======================================================================
 FILE: BulkUpload.aspx (Final Version)
 =======================================================================
--%>
<%@ Page Title="Bulk Upload Products" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="BulkUpload.aspx.cs" Inherits="MediEase.Admin.BulkUpload" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>Bulk Upload Products</h1>
            <a href="Dashboard.aspx" class="btn btn-outline-secondary">&larr; Back to Admin Dashboard</a>
        </div>

        <div class="card shadow-sm">
            <div class="card-header">
                <h4 class="mb-0">Upload Product CSV</h4>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h5>Instructions:</h5>
                    <p>Please prepare a CSV file with the following columns in this exact order. The first row must be the header row and will be skipped.</p>
                    <p><code>Name,Description,CategoryID,BrandID,Form,Strength,Price,Stock,IsPrescriptionRequired</code></p>
                    <ul>
                        <li><strong>Name, Description, Form, Strength:</strong> Text values.</li>
                        <li><strong>CategoryID, BrandID:</strong> The numerical ID for an existing Category and Brand.</li>
                        <li><strong>Price, Stock:</strong> Numerical values. Do not include currency symbols.</li>
                        <li><strong>IsPrescriptionRequired:</strong> Use `TRUE` or `FALSE`.</li>
                    </ul>
                </div>

                <div class="form-group mt-4">
                    <label><strong>Select CSV File to Upload:</strong></label>
                    <asp:FileUpload ID="CsvFileUpload" runat="server" CssClass="form-control-file" />
                    <asp:RequiredFieldValidator runat="server" ControlToValidate="CsvFileUpload" ErrorMessage="Please select a file." CssClass="text-danger" />
                </div>

                <asp:Button ID="UploadButton" runat="server" Text="Upload & Process File" CssClass="btn btn-primary" OnClick="UploadButton_Click" />
            
                <asp:PlaceHolder ID="ResultPlaceholder" runat="server" Visible="false">
                    <hr />
                    <h4>Upload Results</h4>
                    <div class="alert" id="ResultAlert" runat="server">
                        <asp:Literal ID="ResultMessage" runat="server"></asp:Literal>
                    </div>
                </asp:PlaceHolder>

            </div>
        </div>
    </div>
</asp:Content>