﻿/* FILE: Pharmacist/Reports.aspx.cs */
using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace MediEase.Pharmacist
{
    public partial class Reports : Page
    {
        private string CurrentView { get { return ViewState["CurrentView"] as string ?? "Sales"; } set { ViewState["CurrentView"] = value; } }

        protected void Page_Load(object sender, EventArgs e)
        {
            if (Session["UserID"] == null || Session["RoleName"]?.ToString() != "Pharmacist") { Response.Redirect("~/Login.aspx"); return; }
            if (!IsPostBack)
            {
                ViewState["CurrentView"] = "Sales";
                UpdateActiveView();
                StartDateBox.Text = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1).ToString("yyyy-MM-dd");
                EndDateBox.Text = DateTime.Now.ToString("yyyy-MM-dd");
                GenerateReportButton_Click(sender, e);
            }
        }

        protected void Tab_Click(object sender, EventArgs e)
        {
            LinkButton clickedTab = (LinkButton)sender;
            CurrentView = clickedTab.CommandArgument;
            UpdateActiveView();
        }

        private void UpdateActiveView()
        {
            SalesReportView.Visible = (CurrentView == "Sales");
            StockReportView.Visible = (CurrentView == "Stock");
            ExpiringReportView.Visible = (CurrentView == "Expiring");
            SalesTab.CssClass = (CurrentView == "Sales") ? "nav-link active" : "nav-link";
            StockTab.CssClass = (CurrentView == "Stock") ? "nav-link active" : "nav-link";
            ExpiringTab.CssClass = (CurrentView == "Expiring") ? "nav-link active" : "nav-link";
            if (CurrentView == "Stock") { BindStockReportGrid(); }
            else if (CurrentView == "Expiring") { BindExpiringReportGrid(); }
        }

        protected void GenerateReportButton_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(StartDateBox.Text) || string.IsNullOrEmpty(EndDateBox.Text)) return;
            DateTime startDate = DateTime.Parse(StartDateBox.Text);
            DateTime endDate = DateTime.Parse(EndDateBox.Text).AddDays(1);
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string gridQuery = "SELECT p.ProductID, p.Name, SUM(oi.Quantity) AS TotalQuantity, SUM(oi.Quantity * oi.PricePerUnit) AS TotalRevenue FROM OrderItems oi INNER JOIN Products p ON oi.ProductID = p.ProductID INNER JOIN Orders o ON oi.OrderID = o.OrderID WHERE o.OrderDate >= @StartDate AND o.OrderDate < @EndDate AND o.StatusID IN (3, 4, 5) GROUP BY p.ProductID, p.Name ORDER BY TotalRevenue DESC;";
                const string summaryQuery = "SELECT ISNULL(SUM(oi.Quantity * oi.PricePerUnit), 0) AS GrandTotalSales, ISNULL(SUM(oi.Quantity), 0) AS GrandTotalItems FROM OrderItems oi INNER JOIN Orders o ON oi.OrderID = o.OrderID WHERE o.OrderDate >= @StartDate AND o.OrderDate < @EndDate AND o.StatusID IN (3, 4, 5);";
                try
                {
                    con.Open();
                    SqlDataAdapter sda = new SqlDataAdapter(gridQuery, con);
                    sda.SelectCommand.Parameters.AddWithValue("@StartDate", startDate);
                    sda.SelectCommand.Parameters.AddWithValue("@EndDate", endDate);
                    DataTable dt = new DataTable(); sda.Fill(dt);
                    SalesReportGrid.DataSource = dt; SalesReportGrid.DataBind();
                    using (SqlCommand summaryCmd = new SqlCommand(summaryQuery, con))
                    {
                        summaryCmd.Parameters.AddWithValue("@StartDate", startDate);
                        summaryCmd.Parameters.AddWithValue("@EndDate", endDate);
                        using (SqlDataReader reader = summaryCmd.ExecuteReader())
                        {
                            if (reader.Read()) { TotalSalesLabel.Text = Convert.ToDecimal(reader["GrandTotalSales"]).ToString("C"); TotalItemsLabel.Text = reader["GrandTotalItems"].ToString(); }
                        }
                    }
                    DateRangeLabel.Text = $"{startDate:MMM dd, yyyy} to {endDate.AddDays(-1):MMM dd, yyyy}";
                    ReportView.Visible = true;
                }
                catch (Exception ex) { System.Diagnostics.Debug.WriteLine("Error generating sales report: " + ex.Message); }
            }
        }

        private void BindStockReportGrid()
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "SELECT p.ProductID, p.Name, p.ReorderLevel, ISNULL(SUM(i.QuantityInStock), 0) AS TotalStock, ISNULL(SUM(CASE WHEN i.ExpiryDate < GETDATE() THEN 1 ELSE 0 END), 0) AS ExpiredBatchesCount FROM Products p LEFT JOIN Inventory i ON p.ProductID = i.ProductID WHERE p.IsActive = 1 GROUP BY p.ProductID, p.Name, p.ReorderLevel ORDER BY p.Name;";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    try
                    {
                        con.Open();
                        SqlDataAdapter sda = new SqlDataAdapter(cmd);
                        DataTable dt = new DataTable();
                        sda.Fill(dt);
                        StockReportGrid.DataSource = dt;
                        StockReportGrid.DataBind();
                    }
                    catch (Exception ex) { System.Diagnostics.Debug.WriteLine("Error binding stock report grid: " + ex.Message); }
                }
            }
        }

        protected void StockReportGrid_RowDataBound(object sender, GridViewRowEventArgs e)
        {
            if (e.Row.RowType == DataControlRowType.DataRow)
            {
                Label stockStatusLabel = (Label)e.Row.FindControl("StockStatusLabel");
                Label expiryStatusLabel = (Label)e.Row.FindControl("ExpiryStatusLabel");
                int totalStock = Convert.ToInt32(DataBinder.Eval(e.Row.DataItem, "TotalStock"));
                int reorderLevel = Convert.ToInt32(DataBinder.Eval(e.Row.DataItem, "ReorderLevel"));
                int expiredCount = Convert.ToInt32(DataBinder.Eval(e.Row.DataItem, "ExpiredBatchesCount"));
                if (totalStock == 0) { stockStatusLabel.Text = "Out of Stock"; stockStatusLabel.CssClass = "badge badge-pill badge-danger"; }
                else if (totalStock <= reorderLevel) { stockStatusLabel.Text = "Low Stock"; stockStatusLabel.CssClass = "badge badge-pill badge-warning"; }
                else { stockStatusLabel.Text = "OK"; stockStatusLabel.CssClass = "badge badge-pill badge-success"; }
                if (expiredCount > 0) { expiryStatusLabel.Text = "Expired"; expiryStatusLabel.CssClass = "badge badge-pill badge-danger"; }
                else { expiryStatusLabel.Text = "OK"; expiryStatusLabel.CssClass = "badge badge-pill badge-success"; }
            }
        }

        private void BindExpiringReportGrid()
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "SELECT p.ProductID, p.Name, i.BatchNumber, i.QuantityInStock, i.ExpiryDate FROM Inventory i INNER JOIN Products p ON i.ProductID = p.ProductID WHERE i.ExpiryDate BETWEEN GETDATE() AND DATEADD(day, 90, GETDATE()) ORDER BY i.ExpiryDate ASC;";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    try
                    {
                        con.Open();
                        SqlDataAdapter sda = new SqlDataAdapter(cmd);
                        DataTable dt = new DataTable();
                        sda.Fill(dt);
                        ExpiringReportGrid.DataSource = dt;
                        ExpiringReportGrid.DataBind();
                    }
                    catch (Exception ex) { System.Diagnostics.Debug.WriteLine("Error binding expiring report: " + ex.Message); }
                }
            }
        }
    }
}