﻿-- Check if the column already exists before trying to add it
IF NOT EXISTS (SELECT * FROM sys.columns 
               WHERE Name = N'IsApproved' 
               AND Object_ID = Object_ID(N'Products'))
BEGIN
    -- Add the new column. We'll default it to 0 (Not Approved).
    ALTER TABLE Products
    ADD IsApproved BIT NOT NULL DEFAULT 0;
    PRINT 'IsApproved column added to Products table successfully.';
END
ELSE
BEGIN
    PRINT 'IsApproved column already exists in Products table.';
END
GO
