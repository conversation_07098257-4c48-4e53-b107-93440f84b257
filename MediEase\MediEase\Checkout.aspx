﻿<%-- FILE: Checkout.aspx (Final Version) --%>
<%@ Page Title="Checkout" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="Checkout.aspx.cs" Inherits="MediEase.Checkout" %>
<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-5">
        <div class="text-center"><h1>Checkout</h1><p class="lead">Please confirm your order details and complete the payment.</p></div>
        <asp:PlaceHolder ID="CheckoutView" runat="server">
            <div class="row">
                <div class="col-lg-7">
                    <div class="card shadow-sm mb-4"><div class="card-body"><h4 class="card-title">1. Shipping Address</h4><hr /><div class="form-group"><asp:Label runat="server" AssociatedControlID="ShippingAddressBox" CssClass="font-weight-bold">Full Address</asp:Label><asp:TextBox ID="ShippingAddressBox" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="3"></asp:TextBox></div><div class="form-group"><asp:Label runat="server" AssociatedControlID="PhoneNumberBox" CssClass="font-weight-bold">Contact Number</asp:Label><asp:TextBox ID="PhoneNumberBox" runat="server" CssClass="form-control"></asp:TextBox></div></div></div>
                    <div class="card shadow-sm mb-4"><div class="card-body"><h4 class="card-title">2. Upload Prescription (If Required)</h4><hr /><asp:FileUpload ID="PrescriptionUpload" runat="server" CssClass="form-control-file" /></div></div>
                    <div class="card shadow-sm"><div class="card-body"><h4 class="card-title">3. Scan & Pay</h4><hr /><div class="text-center"><img src="Images/1000088742.jpg" alt="Payment QR Code" class="img-fluid rounded" style="max-width: 250px;" /></div><hr /><h4 class="card-title">4. Upload Payment Screenshot</h4><asp:FileUpload ID="ScreenshotUpload" runat="server" CssClass="form-control-file" /><asp:RequiredFieldValidator runat="server" ControlToValidate="ScreenshotUpload" ErrorMessage="A payment screenshot is required." CssClass="text-danger" Display="Dynamic" /></div></div>
                </div>
                <div class="col-lg-5">
                    <div class="card shadow-sm sticky-top" style="top: 80px;">
                        <div class="card-body">
                            <h4 class="card-title">Order Summary</h4><hr />
                            <asp:Repeater ID="OrderSummaryRepeater" runat="server"><ItemTemplate><div class="d-flex justify-content-between"><span><%# Eval("Quantity") %> x <%# Eval("ProductName") %></span><span>$<%# Eval("Total", "{0:N2}") %></span></div></ItemTemplate></asp:Repeater><hr />
                             <div class="d-flex justify-content-between"><span>Subtotal</span><strong><asp:Label ID="SubtotalLabel" runat="server" Text="$0.00"></asp:Label></strong></div>
                             <asp:PlaceHolder ID="DiscountView" runat="server" Visible="false"><div class="d-flex justify-content-between text-success"><span>Discount (<asp:Literal ID="DiscountCodeLabel" runat="server"></asp:Literal>)</span><strong>-<asp:Label ID="DiscountAmountLabel" runat="server"></asp:Label></strong></div></asp:PlaceHolder>
                             <asp:PlaceHolder ID="PointsRedeemedView" runat="server" Visible="false"><div class="d-flex justify-content-between text-success"><span>Loyalty Points Redeemed</span><strong>-<asp:Label ID="PointsDiscountLabel" runat="server"></asp:Label></strong></div></asp:PlaceHolder><hr />
                             <div class="d-flex justify-content-between font-weight-bold h5"><span>Total</span><asp:Label ID="TotalLabel" runat="server" Text="$0.00"></asp:Label></div><hr />
                            <asp:PlaceHolder ID="PointsRedeemFormView" runat="server"><div class="mb-3"><p class="mb-1">You have <strong><asp:Label ID="AvailablePointsLabel" runat="server" Text="0"></asp:Label></strong> points available.</p><div class="input-group"><asp:TextBox ID="PointsToRedeemBox" runat="server" CssClass="form-control" placeholder="Points to use" TextMode="Number"></asp:TextBox><div class="input-group-append"><asp:Button ID="ApplyPointsButton" runat="server" Text="Apply" CssClass="btn btn-secondary" OnClick="ApplyPointsButton_Click" /></div></div></div><hr /></asp:PlaceHolder>
                            <asp:PlaceHolder ID="DiscountFormView" runat="server"><div class="input-group"><asp:TextBox ID="DiscountCodeBox" runat="server" CssClass="form-control" placeholder="Discount code"></asp:TextBox><div class="input-group-append"><asp:Button ID="ApplyDiscountButton" runat="server" Text="Apply" CssClass="btn btn-secondary" OnClick="ApplyDiscountButton_Click" /></div></div></asp:PlaceHolder>
                            <asp:PlaceHolder ID="MessagePlaceholder" runat="server" Visible="false"><div class="alert mt-3" id="MessageAlert" runat="server"><asp:Literal ID="MessageText" runat="server"></asp:Literal></div></asp:PlaceHolder>
                            <asp:Button ID="ConfirmOrderButton" runat="server" OnClick="ConfirmOrderButton_Click" Text="Confirm & Place Order" CssClass="btn btn-success btn-lg btn-block mt-3" />
                             <asp:PlaceHolder ID="ErrorMessage" runat="server" Visible="false"><div class="alert alert-danger mt-3"><asp:Literal ID="FailureText" runat="server"></asp:Literal></div></asp:PlaceHolder>
                        </div>
                    </div>
                </div>
            </div>
        </asp:PlaceHolder>
        <asp:PlaceHolder ID="EmptyCartErrorView" runat="server" Visible="false"><div class="alert alert-warning text-center"><h2>Your Cart is Empty</h2><a href="Shop.aspx" class="btn btn-primary">Return to Shop</a></div></asp:PlaceHolder>
    </div>
</asp:Content>