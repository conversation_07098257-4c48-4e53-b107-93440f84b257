﻿using System;

namespace MediEase
{
    [Serializable]
    public class CartItem
    {
        public int ProductID { get; set; }
        public string ProductName { get; set; }
        public decimal Price { get; set; }
        public int Quantity { get; set; }
        public string ImageUrl { get; set; }

        public decimal Total
        {
            get { return Price * Quantity; }
        }

        public CartItem() { }

        public CartItem(int productId, string productName, decimal price, int quantity)
        {
            this.ProductID = productId;
            this.ProductName = productName ?? throw new ArgumentNullException(nameof(productName));
            this.Price = price;
            this.Quantity = quantity;
            this.ImageUrl = "~/Images/Products/default_product.png";
        }
    }
}