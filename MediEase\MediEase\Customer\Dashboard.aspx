﻿<%--
 =======================================================================
 FILE: Dashboard.aspx (Final Version with All Links)
 =======================================================================
--%>
<%@ Page Title="My Dashboard" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="Dashboard.aspx.cs" Inherits="MediEase.Customer.Dashboard" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-5">
        <h1 class="mb-4">Welcome, <asp:Literal ID="CustomerName" runat="server">Customer</asp:Literal>!</h1>
        <div class="row">
            <div class="col-lg-3">
                <div class="card shadow-sm mb-4">
                    <div class="card-header"><h5 class="mb-0">My Account Menu</h5></div>
                    <div class="list-group list-group-flush">
                        <a href="Profile.aspx" class="list-group-item list-group-item-action">Profile & Family</a>
                        <a href="Reminders.aspx" class="list-group-item list-group-item-action">Medication Reminders</a>
                        <a href="AutoRefills.aspx" class="list-group-item list-group-item-action">Automatic Refills</a>
                        <a href="Feedback.aspx" class="list-group-item list-group-item-action">Submit Feedback</a>
                        <a href="Ask.aspx" class="list-group-item list-group-item-action">Ask a Pharmacist</a>
                    </div>
                </div>
                 <div class="card bg-warning text-white shadow-sm">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="h2 font-weight-bold"><asp:Label ID="LoyaltyPointsLabel" runat="server" Text="0"></asp:Label></div>
                                <div class="h5">Loyalty Points</div>
                            </div>
                            <i class="fas fa-star fa-3x"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-9">
                <div class="card shadow-sm">
                    <div class="card-header"><h4 class="mb-0">My Order History</h4></div>
                    <div class="card-body">
                        <asp:GridView ID="OrderHistoryGrid" runat="server" 
                            CssClass="table table-hover"
                            AutoGenerateColumns="False" 
                            DataKeyNames="OrderID"
                            GridLines="None"
                            AllowPaging="True"
                            OnPageIndexChanging="OrderHistoryGrid_PageIndexChanging"
                            PageSize="10">
                            <Columns>
                                <asp:BoundField DataField="OrderID" HeaderText="Order ID" />
                                <asp:BoundField DataField="OrderDate" HeaderText="Date Placed" DataFormatString="{0:d}" />
                                <asp:BoundField DataField="TotalAmount" HeaderText="Total" DataFormatString="{0:C}" />
                                <asp:TemplateField HeaderText="Status">
                                    <ItemTemplate>
                                        <span class='badge badge-pill <%# GetStatusBadgeClass(Eval("StatusName").ToString()) %>'><%# Eval("StatusName") %></span>
                                    </ItemTemplate>
                                </asp:TemplateField>
                                <asp:HyperLinkField DataNavigateUrlFields="OrderID" 
                                    DataNavigateUrlFormatString="~/Customer/OrderDetails.aspx?OrderID={0}"
                                    Text="View Details" 
                                    ControlStyle-CssClass="btn btn-sm btn-info" />
                            </Columns>
                            <EmptyDataTemplate><div class="alert alert-info">You have not placed any orders yet. <a href="../Shop.aspx" class="alert-link">Start Shopping!</a></div></EmptyDataTemplate>
                        </asp:GridView>
                    </div>
                </div>
            </div>
        </div>
    </div>
</asp:Content>