﻿<%--
 =======================================================================
 FILE: ManageUsers.aspx (Final Version)
 =======================================================================
--%>
<%@ Page Title="Manage Users" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="ManageUsers.aspx.cs" Inherits="MediEase.Admin.ManageUsers" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>Manage User Accounts</h1>
            <a href="Dashboard.aspx" class="btn btn-outline-secondary">&larr; Back to Admin Dashboard</a>
        </div>

        <div class="card shadow-sm">
            <div class="card-header">
                <h4 class="mb-0">All System Users</h4>
            </div>
            <div class="card-body">
                <asp:GridView ID="UsersGrid" runat="server"
                    CssClass="table table-hover table-striped"
                    AutoGenerateColumns="False"
                    DataKeyNames="UserID"
                    GridLines="None"
                    AllowPaging="True"
                    OnPageIndexChanging="UsersGrid_PageIndexChanging"
                    OnRowCommand="UsersGrid_RowCommand"
                    PageSize="15">
                    <Columns>
                        <asp:BoundField DataField="UserID" HeaderText="User ID" />
                        <asp:BoundField DataField="FullName" HeaderText="Full Name" />
                        <asp:BoundField DataField="Email" HeaderText="Email" />
                        <asp:BoundField DataField="RoleName" HeaderText="Role" />
                        <asp:TemplateField HeaderText="Status">
                            <ItemTemplate>
                                <span class='badge badge-pill <%# Convert.ToBoolean(Eval("IsActive")) ? "badge-success" : "badge-danger" %>'>
                                    <%# Convert.ToBoolean(Eval("IsActive")) ? "Active" : "Inactive" %>
                                </span>
                            </ItemTemplate>
                        </asp:TemplateField>
                        <asp:TemplateField HeaderText="Actions">
                            <ItemTemplate>
                                <asp:Button ID="ActivateButton" runat="server"
                                    Text="Activate"
                                    CssClass="btn btn-sm btn-success"
                                    CommandName="ActivateUser"
                                    CommandArgument='<%# Eval("UserID") %>'
                                    Visible='<%# !Convert.ToBoolean(Eval("IsActive")) %>' />
                                <asp:Button ID="DeactivateButton" runat="server"
                                    Text="Deactivate"
                                    CssClass="btn btn-sm btn-warning"
                                    CommandName="DeactivateUser"
                                    CommandArgument='<%# Eval("UserID") %>'
                                    Visible='<%# Convert.ToBoolean(Eval("IsActive")) && Convert.ToInt32(Eval("UserID")) != 1 %>'
                                    OnClientClick="return confirm('Are you sure you want to deactivate this user?');" />
                            </ItemTemplate>
                        </asp:TemplateField>
                    </Columns>
                    <EmptyDataTemplate>
                        <div class="alert alert-info text-center">
                            No user data found.
                        </div>
                    </EmptyDataTemplate>
                </asp:GridView>
            </div>
        </div>
    </div>
</asp:Content>