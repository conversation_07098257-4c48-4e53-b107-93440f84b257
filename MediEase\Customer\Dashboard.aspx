<%@ Page Language="C#" AutoEventWireup="true" CodeFile="Dashboard.aspx.cs" Inherits="Customer_Dashboard" %>

<!DOCTYPE html>
<html lang="en">
<head runat="server">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Customer Dashboard - MediEase</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <style>
        body { font-family: 'Inter', sans-serif; }
        .sidebar-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            transition: background-color 0.2s, color 0.2s;
        }
        .sidebar-link:hover, .sidebar-link.active {
            background-color: #0d9488; /* teal-700 */
            color: white;
        }
        .sidebar-link i {
            width: 1.25rem;
            margin-right: 0.75rem;
        }
    </style>
</head>
<body class="bg-gray-100">
    <form id="form1" runat="server">
        <div class="flex h-screen bg-gray-100">
            <!-- Sidebar -->
            <aside class="w-64 bg-teal-600 text-white flex flex-col p-4">
                <div class="flex items-center space-x-2 pb-4 border-b border-teal-500">
                    <i class="fas fa-pills text-3xl"></i>
                    <span class="text-2xl font-bold">MediEase</span>
                </div>
                <nav class="mt-6 flex-grow">
                    <a href="Dashboard.aspx" class="sidebar-link active">
                        <i class="fas fa-tachometer-alt fa-fw"></i>
                        <span>Dashboard</span>
                    </a>
                    <a href="Profile.aspx" class="sidebar-link">
                        <i class="fas fa-user-edit fa-fw"></i>
                        <span>My Profile</span>
                    </a>
                    <a href="OrderHistory.aspx" class="sidebar-link">
                        <i class="fas fa-history fa-fw"></i>
                        <span>Order History</span>
                    </a>
                    <a href="Prescriptions.aspx" class="sidebar-link">
                        <i class="fas fa-file-prescription fa-fw"></i>
                        <span>Prescriptions</span>
                    </a>
                     <a href="Reminders.aspx" class="sidebar-link">
                        <i class="fas fa-bell fa-fw"></i>
                        <span>Reminders</span>
                    </a>
                </nav>
                <div class="pt-4 border-t border-teal-500">
                    <asp:LinkButton ID="btnLogout" runat="server" OnClick="btnLogout_Click" CssClass="sidebar-link w-full">
                        <i class="fas fa-sign-out-alt fa-fw"></i>
                        <span>Logout</span>
                    </asp:LinkButton>
                </div>
            </aside>

            <!-- Main Content -->
            <main class="flex-1 flex flex-col">
                <header class="bg-white shadow-sm p-4">
                    <div class="flex justify-between items-center">
                         <h1 class="text-2xl font-bold text-gray-800">
                            <asp:Literal ID="litWelcomeMessage" runat="server" Text="Dashboard"></asp:Literal>
                        </h1>
                         <div class="flex items-center space-x-4">
                             <a href="/ShoppingCart.aspx" class="text-gray-600 hover:text-teal-600 relative">
                                 <i class="fas fa-shopping-cart text-xl"></i>
                                 <span class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">0</span>
                             </a>
                             <asp:Literal ID="litUserInitial" runat="server"></asp:Literal>
                         </div>
                    </div>
                </header>

                <div class="flex-1 p-6 overflow-y-auto">
                    <!-- Stats Cards -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                        <div class="bg-white p-6 rounded-lg shadow flex items-center">
                            <i class="fas fa-box-open text-4xl text-blue-500"></i>
                            <div class="ml-4">
                                <p class="text-gray-500">Total Orders</p>
                                <asp:Literal ID="litTotalOrders" runat="server" Text="0" CssClass="text-2xl font-bold"></asp:Literal>
                            </div>
                        </div>
                         <div class="bg-white p-6 rounded-lg shadow flex items-center">
                            <i class="fas fa-star text-4xl text-yellow-500"></i>
                            <div class="ml-4">
                                <p class="text-gray-500">Loyalty Points</p>
                                <asp:Literal ID="litLoyaltyPoints" runat="server" Text="0" CssClass="text-2xl font-bold"></asp:Literal>
                            </div>
                        </div>
                        <div class="bg-white p-6 rounded-lg shadow flex items-center">
                            <i class="fas fa-heartbeat text-4xl text-red-500"></i>
                             <div class="ml-4">
                                <p class="text-gray-500">Active Reminders</p>
                                <asp:Literal ID="litActiveReminders" runat="server" Text="0" CssClass="text-2xl font-bold"></asp:Literal>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Orders -->
                    <div class="bg-white p-6 rounded-lg shadow">
                         <h2 class="text-xl font-bold text-gray-800 mb-4">Recent Orders</h2>
                         <asp:GridView ID="gvRecentOrders" runat="server" AutoGenerateColumns="False"
                             CssClass="min-w-full divide-y divide-gray-200"
                             HeaderStyle-CssClass="bg-gray-50"
                             RowStyle-CssClass="bg-white"
                             AlternatingRowStyle-CssClass="bg-gray-50">
                             <Columns>
                                 <asp:BoundField DataField="OrderNumber" HeaderText="Order #" HeaderStyle-CssClass="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" ItemStyle-CssClass="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900" />
                                 <asp:BoundField DataField="OrderDate" HeaderText="Date" DataFormatString="{0:MMM dd, yyyy}" HeaderStyle-CssClass="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" ItemStyle-CssClass="px-6 py-4 whitespace-nowrap text-sm text-gray-500" />
                                 <asp:BoundField DataField="TotalAmount" HeaderText="Total" DataFormatString="{0:C}" HeaderStyle-CssClass="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" ItemStyle-CssClass="px-6 py-4 whitespace-nowrap text-sm text-gray-500" />
                                 <asp:TemplateField HeaderText="Status" HeaderStyle-CssClass="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" ItemStyle-CssClass="px-6 py-4 whitespace-nowrap text-sm">
                                     <ItemTemplate>
                                         <span class='px-2 inline-flex text-xs leading-5 font-semibold rounded-full <%# GetStatusClass(Eval("Status").ToString()) %>'>
                                             <%# Eval("Status") %>
                                         </span>
                                     </ItemTemplate>
                                 </asp:TemplateField>
                             </Columns>
                             <EmptyDataTemplate>
                                 <div class="text-center py-8">
                                     <i class="fas fa-receipt text-4xl text-gray-300"></i>
                                     <p class="mt-2 text-gray-500">You haven't placed any orders yet.</p>
                                 </div>
                             </EmptyDataTemplate>
                         </asp:GridView>
                    </div>
                </div>
            </main>
        </div>
    </form>
</body>
</html>
