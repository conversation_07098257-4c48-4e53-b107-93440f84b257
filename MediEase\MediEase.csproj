<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\EntityFramework.6.4.4\build\EntityFramework.props" Condition="Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{8B5C2B4D-6F1C-4B5A-8E3C-9D2E1F4A5B6C}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>MediEase</RootNamespace>
    <AssemblyName>MediEase</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <UseIISExpress>true</UseIISExpress>
    <Use64BitIISExpress />
    <IISExpressSSLPort>44300</IISExpressSSLPort>
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="BCrypt.Net-Next, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\BCrypt.Net-Next.4.0.3\lib\net48\BCrypt.Net-Next.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.4.4\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.4.4\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.ScriptManager.MSAjax, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.ScriptManager.MSAjax.5.0.0\lib\net45\Microsoft.ScriptManager.MSAjax.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Web.Infrastructure, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Web.Infrastructure.*******\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=********, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Xml.Linq" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Default.aspx" />
    <Content Include="Global.asax" />
    <Content Include="Login.aspx" />
    <Content Include="Register.aspx" />
    <Content Include="Web.config" />
    <Content Include="Admin\Dashboard.aspx" />
    <Content Include="Admin\EditProduct.aspx" />
    <Content Include="Admin\ProductManagement.aspx" />
    <Content Include="Admin\Reports.aspx" />
    <Content Include="Admin\SystemSettings.aspx" />
    <Content Include="Admin\UserManagement.aspx" />
    <Content Include="Customer\CustomerOrderDetail.aspx" />
    <Content Include="Customer\Dashboard.aspx" />
    <Content Include="Customer\OrderHistory.aspx" />
    <Content Include="Customer\Prescriptions.aspx" />
    <Content Include="Customer\Profile.aspx" />
    <Content Include="Customer\Reminders.aspx" />
    <Content Include="Pharmacist\Dashboard.aspx" />
    <Content Include="Pharmacist\ManageOrders.aspx" />
    <Content Include="Pharmacist\ManageStock.aspx" />
    <Content Include="Pharmacist\PrescriptionDetail.aspx" />
    <Content Include="Pharmacist\ValidatePrescriptions.aspx" />
    <Content Include="Scripts\Site.js" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Default.aspx.designer.cs">
      <DependentUpon>Default.aspx</DependentUpon>
    </Compile>
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="Login.aspx.cs">
      <DependentUpon>Login.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Login.aspx.designer.cs">
      <DependentUpon>Login.aspx</DependentUpon>
    </Compile>
    <Compile Include="Register.aspx.cs">
      <DependentUpon>Register.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Register.aspx.designer.cs">
      <DependentUpon>Register.aspx</DependentUpon>
    </Compile>
    <Compile Include="Admin\Dashboard.aspx.cs">
      <DependentUpon>Dashboard.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Admin\Dashboard.aspx.designer.cs">
      <DependentUpon>Dashboard.aspx</DependentUpon>
    </Compile>
    <Compile Include="Admin\EditProduct.aspx.cs">
      <DependentUpon>EditProduct.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Admin\EditProduct.aspx.designer.cs">
      <DependentUpon>EditProduct.aspx</DependentUpon>
    </Compile>
    <Compile Include="Admin\ProductManagement.aspx.cs">
      <DependentUpon>ProductManagement.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Admin\ProductManagement.aspx.designer.cs">
      <DependentUpon>ProductManagement.aspx</DependentUpon>
    </Compile>
    <Compile Include="Admin\Reports.aspx.cs">
      <DependentUpon>Reports.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Admin\Reports.aspx.designer.cs">
      <DependentUpon>Reports.aspx</DependentUpon>
    </Compile>
    <Compile Include="Admin\SystemSettings.aspx.cs">
      <DependentUpon>SystemSettings.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Admin\SystemSettings.aspx.designer.cs">
      <DependentUpon>SystemSettings.aspx</DependentUpon>
    </Compile>
    <Compile Include="Admin\UserManagement.aspx.cs">
      <DependentUpon>UserManagement.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Admin\UserManagement.aspx.designer.cs">
      <DependentUpon>UserManagement.aspx</DependentUpon>
    </Compile>
    <Compile Include="Customer\CustomerOrderDetail.aspx.cs">
      <DependentUpon>CustomerOrderDetail.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Customer\CustomerOrderDetail.aspx.designer.cs">
      <DependentUpon>CustomerOrderDetail.aspx</DependentUpon>
    </Compile>
    <Compile Include="Customer\Dashboard.aspx.cs">
      <DependentUpon>Dashboard.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Customer\Dashboard.aspx.designer.cs">
      <DependentUpon>Dashboard.aspx</DependentUpon>
    </Compile>
    <Compile Include="Customer\OrderHistory.aspx.cs">
      <DependentUpon>OrderHistory.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Customer\OrderHistory.aspx.designer.cs">
      <DependentUpon>OrderHistory.aspx</DependentUpon>
    </Compile>
    <Compile Include="Customer\Prescriptions.aspx.cs">
      <DependentUpon>Prescriptions.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Customer\Prescriptions.aspx.designer.cs">
      <DependentUpon>Prescriptions.aspx</DependentUpon>
    </Compile>
    <Compile Include="Customer\Profile.aspx.cs">
      <DependentUpon>Profile.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Customer\Profile.aspx.designer.cs">
      <DependentUpon>Profile.aspx</DependentUpon>
    </Compile>
    <Compile Include="Customer\Reminders.aspx.cs">
      <DependentUpon>Reminders.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Customer\Reminders.aspx.designer.cs">
      <DependentUpon>Reminders.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pharmacist\Dashboard.aspx.cs">
      <DependentUpon>Dashboard.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pharmacist\Dashboard.aspx.designer.cs">
      <DependentUpon>Dashboard.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pharmacist\ManageOrders.aspx.cs">
      <DependentUpon>ManageOrders.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pharmacist\ManageOrders.aspx.designer.cs">
      <DependentUpon>ManageOrders.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pharmacist\ManageStock.aspx.cs">
      <DependentUpon>ManageStock.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pharmacist\ManageStock.aspx.designer.cs">
      <DependentUpon>ManageStock.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pharmacist\PrescriptionDetail.aspx.cs">
      <DependentUpon>PrescriptionDetail.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pharmacist\PrescriptionDetail.aspx.designer.cs">
      <DependentUpon>PrescriptionDetail.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pharmacist\ValidatePrescriptions.aspx.cs">
      <DependentUpon>ValidatePrescriptions.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pharmacist\ValidatePrescriptions.aspx.designer.cs">
      <DependentUpon>ValidatePrescriptions.aspx</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="App_Data\" />
    <Folder Include="Images\" />
    <Folder Include="api\" />
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>0</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>https://localhost:44300/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.4.4\build\EntityFramework.props'))" />
    <Error Condition="!Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.4.4\build\EntityFramework.targets'))" />
  </Target>
  <Import Project="..\packages\EntityFramework.6.4.4\build\EntityFramework.targets" Condition="Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.targets')" />
</Project>
