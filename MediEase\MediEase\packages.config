﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Microsoft.AspNet.FriendlyUrls" version="1.0.2" targetFramework="net481" />
  <package id="Microsoft.AspNet.FriendlyUrls.Core" version="1.0.2" targetFramework="net481" />
  <package id="Microsoft.AspNet.Mvc" version="5.2.9" targetFramework="net481" />
  <package id="Microsoft.AspNet.Razor" version="3.2.9" targetFramework="net481" />
  <package id="Microsoft.AspNet.WebApi" version="5.2.9" targetFramework="net481" />
  <package id="Microsoft.AspNet.WebApi.Client" version="5.2.9" targetFramework="net481" />
  <package id="Microsoft.AspNet.WebApi.Core" version="5.2.9" targetFramework="net481" />
  <package id="Microsoft.AspNet.WebApi.WebHost" version="5.2.9" targetFramework="net481" />
  <package id="Microsoft.AspNet.WebPages" version="3.2.9" targetFramework="net481" />
  <package id="Microsoft.CodeDom.Providers.DotNetCompilerPlatform" version="2.0.1" targetFramework="net481" />
  <package id="Microsoft.Web.Infrastructure" version="2.0.0" targetFramework="net481" />
  <package id="Newtonsoft.Json" version="13.0.3" targetFramework="net481" />
</packages>