﻿/*
 =======================================================================
 FILE: Post.aspx.cs
 PURPOSE: Backend logic for the single blog post page.
 =======================================================================
*/
using System;
using System.Configuration;
using System.Data.SqlClient;
using System.Web.UI;

namespace MediEase
{
    public partial class Post : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                if (int.TryParse(Request.QueryString["PostID"], out int postId))
                {
                    LoadPost(postId);
                }
                else
                {
                    ShowErrorView();
                }
            }
        }

        private void LoadPost(int postId)
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = @"
                    SELECT 
                        bp.Title, bp.Content, bp.ImageUrl, 
                        ISNULL(up.FirstName + ' ' + up.LastName, 'Admin') AS AuthorName, 
                        bp.PublishDate
                    FROM BlogPosts bp
                    LEFT JOIN UserProfiles up ON bp.AuthorID = up.UserID
                    WHERE bp.PostID = @PostID AND bp.IsPublished = 1;";

                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@PostID", postId);
                    try
                    {
                        con.Open();
                        SqlDataReader reader = cmd.ExecuteReader();
                        if (reader.Read())
                        {
                            PostTitle.Text = Server.HtmlEncode(reader["Title"].ToString());
                            Page.Title = reader["Title"].ToString();
                            PostAuthor.Text = Server.HtmlEncode(reader["AuthorName"].ToString());
                            PostDate.Text = Convert.ToDateTime(reader["PublishDate"]).ToString("MMMM dd, yyyy");
                            PostContent.Text = reader["Content"].ToString();
                            string imageUrl = reader["ImageUrl"] as string;
                            if (!string.IsNullOrEmpty(imageUrl))
                            {
                                PostImage.ImageUrl = ResolveUrl(imageUrl);
                                PostImage.Visible = true;
                            }
                            else
                            {
                                PostImage.Visible = false;
                            }
                        }
                        else
                        {
                            ShowErrorView();
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine("Error loading post: " + ex.Message);
                        ShowErrorView();
                    }
                }
            }
        }

        private void ShowErrorView()
        {
            PostView.Visible = false;
            ErrorView.Visible = true;
        }
    }
}