﻿/* FILE: Customer/QuestionDetails.aspx.cs */
using System;
using System.Configuration;
using System.Data.SqlClient;
using System.Web.UI;

namespace MediEase.Customer
{
    public partial class QuestionDetails : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (Session["UserID"] == null || Session["RoleName"]?.ToString() != "Customer") { Response.Redirect("~/Login.aspx"); return; }
            if (!IsPostBack)
            {
                if (int.TryParse(Request.QueryString["QuestionID"], out int questionId)) { LoadQuestionDetails(questionId); }
                else { ShowErrorView(); }
            }
        }

        private void LoadQuestionDetails(int questionId)
        {
            int customerId = Convert.ToInt32(Session["UserID"]);
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "SELECT Subject, QuestionText, QuestionDate, IsAnswered, AnswerText, AnswerDate FROM PharmacistQuestions WHERE QuestionID = @QuestionID AND CustomerID = @CustomerID;";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@QuestionID", questionId);
                    cmd.Parameters.AddWithValue("@CustomerID", customerId);
                    try
                    {
                        con.Open();
                        SqlDataReader reader = cmd.ExecuteReader();
                        if (reader.Read())
                        {
                            SubjectLabel.Text = Server.HtmlEncode(reader["Subject"].ToString());
                            QuestionDateLabel.Text = Convert.ToDateTime(reader["QuestionDate"]).ToString("MMMM dd, yyyy");
                            QuestionTextLiteral.Text = Server.HtmlEncode(reader["QuestionText"].ToString());
                            if (Convert.ToBoolean(reader["IsAnswered"]) && reader["AnswerText"] != DBNull.Value)
                            {
                                AnswerView.Visible = true;
                                AnswerTextLiteral.Text = Server.HtmlEncode(reader["AnswerText"].ToString());
                                AnswerDateLabel.Text = Convert.ToDateTime(reader["AnswerDate"]).ToString("MMMM dd, yyyy");
                            }
                        }
                        else { ShowErrorView(); }
                    }
                    catch (Exception ex) { System.Diagnostics.Debug.WriteLine("Error loading question details: " + ex.Message); ShowErrorView(); }
                }
            }
        }

        private void ShowErrorView()
        {
            QuestionView.Visible = false;
            ErrorView.Visible = true;
        }
    }
}