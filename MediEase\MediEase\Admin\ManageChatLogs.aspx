﻿<%--
 =======================================================================
 FILE: ManageChatLogs.aspx (Final Version)
 =======================================================================
--%>
<%@ Page Title="Chatbot Conversation Logs" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="ManageChatLogs.aspx.cs" Inherits="MediEase.Admin.ManageChatLogs" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>Chatbot Conversation Logs</h1>
            <a href="Dashboard.aspx" class="btn btn-outline-secondary">&larr; Back to Admin Dashboard</a>
        </div>

        <div class="card shadow-sm">
            <div class="card-header">
                <h4 class="mb-0">All Conversations</h4>
            </div>
            <div class="card-body">
                <asp:GridView ID="ChatLogsGrid" runat="server"
                    CssClass="table table-striped"
                    AutoGenerateColumns="False"
                    DataKeyNames="LogID"
                    GridLines="None"
                    AllowPaging="True"
                    OnPageIndexChanging="ChatLogsGrid_PageIndexChanging"
                    PageSize="20">
                    <Columns>
                        <asp:BoundField DataField="LogID" HeaderText="Log ID" />
                        <asp:BoundField DataField="Timestamp" HeaderText="Date & Time" DataFormatString="{0:g}" />
                        <asp:BoundField DataField="UserName" HeaderText="User" />
                        <asp:TemplateField HeaderText="User's Message">
                            <ItemTemplate>
                                <div style="max-height: 100px; overflow-y: auto;">
                                    <%# Eval("Message") %>
                                </div>
                            </ItemTemplate>
                        </asp:TemplateField>
                        <asp:TemplateField HeaderText="Bot's Response">
                             <ItemTemplate>
                                <div style="max-height: 100px; overflow-y: auto;">
                                    <%# Eval("Response") %>
                                </div>
                            </ItemTemplate>
                        </asp:TemplateField>
                    </Columns>
                    <EmptyDataTemplate>
                        <div class="alert alert-info text-center">
                            No chatbot conversations have been logged yet.
                        </div>
                    </EmptyDataTemplate>
                </asp:GridView>
            </div>
        </div>
    </div>
</asp:Content>