﻿/* FILE: Checkout.aspx.cs */
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Web.UI;
using System.Web.UI.HtmlControls;
namespace MediEase
{
    public partial class Checkout : Page
    {
        private int availablePoints = 0;
        protected void Page_Load(object sender, EventArgs e) { if (Session["UserID"] == null) { Response.Redirect("~/Login.aspx?returnUrl=" + Server.UrlEncode(Request.Url.AbsolutePath)); return; } if (!IsPostBack) { LoadCheckoutDetails(); } }
        private void LoadCheckoutDetails() { List<CartItem> cart = Session["Cart"] as List<CartItem>; if (cart == null || cart.Count == 0) { CheckoutView.Visible = false; EmptyCartErrorView.Visible = true; return; } OrderSummaryRepeater.DataSource = cart; OrderSummaryRepeater.DataBind(); LoadAvailablePoints(); CalculateTotals(); LoadUserProfile(); }
        private void LoadUserProfile() { string userId = Session["UserID"].ToString(); string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString; using (SqlConnection con = new SqlConnection(connectionString)) { const string query = "SELECT Address, PhoneNumber FROM UserProfiles WHERE UserID = @UserID"; using (SqlCommand cmd = new SqlCommand(query, con)) { cmd.Parameters.AddWithValue("@UserID", userId); try { con.Open(); SqlDataReader reader = cmd.ExecuteReader(); if (reader.Read()) { ShippingAddressBox.Text = reader["Address"]?.ToString(); PhoneNumberBox.Text = reader["PhoneNumber"]?.ToString(); } } catch (Exception ex) { System.Diagnostics.Debug.WriteLine("Error loading user profile: " + ex.Message); } } } }
        private void LoadAvailablePoints() { int customerId = Convert.ToInt32(Session["UserID"]); string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString; using (SqlConnection con = new SqlConnection(connectionString)) { const string query = "SELECT ISNULL(SUM(Points), 0) FROM LoyaltyPoints WHERE CustomerID = @CustomerID"; using (SqlCommand cmd = new SqlCommand(query, con)) { cmd.Parameters.AddWithValue("@CustomerID", customerId); try { con.Open(); availablePoints = (int)cmd.ExecuteScalar(); AvailablePointsLabel.Text = availablePoints.ToString(); } catch (Exception ex) { AvailablePointsLabel.Text = "N/A"; System.Diagnostics.Debug.WriteLine("Error loading loyalty points: " + ex.Message); } } } }
        private void CalculateTotals() { List<CartItem> cart = Session["Cart"] as List<CartItem>; if (cart == null) return; decimal subtotal = cart.Sum(item => item.Total); SubtotalLabel.Text = subtotal.ToString("C"); decimal promoDiscountAmount = 0; if (Session["AppliedDiscount"] != null) { var discount = (dynamic)Session["AppliedDiscount"]; promoDiscountAmount = subtotal * (discount.Percentage / 100); DiscountCodeLabel.Text = discount.Code; DiscountAmountLabel.Text = promoDiscountAmount.ToString("C"); DiscountView.Visible = true; DiscountFormView.Visible = false; } else { DiscountView.Visible = false; DiscountFormView.Visible = true; } decimal pointsDiscountAmount = 0; if (Session["RedeemedPoints"] != null) { int redeemedPoints = (int)Session["RedeemedPoints"]; decimal pointsToDollarRatio = SettingsService.GetDecimalSetting("PointsValueInDollars", 100.0m); if (pointsToDollarRatio > 0) { pointsDiscountAmount = redeemedPoints / pointsToDollarRatio; } PointsDiscountLabel.Text = pointsDiscountAmount.ToString("C"); PointsRedeemedView.Visible = true; PointsRedeemFormView.Visible = false; } else { PointsRedeemedView.Visible = false; PointsRedeemFormView.Visible = true; } decimal finalTotal = subtotal - promoDiscountAmount - pointsDiscountAmount; if (finalTotal < 0) finalTotal = 0; TotalLabel.Text = finalTotal.ToString("C"); }
        protected void ApplyDiscountButton_Click(object sender, EventArgs e) { string code = DiscountCodeBox.Text.Trim().ToUpper(); if (string.IsNullOrEmpty(code)) { ShowMessage("Please enter a discount code.", false); return; } string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString; using (SqlConnection con = new SqlConnection(connectionString)) { const string query = "SELECT DiscountID, DiscountPercentage, StartDate, EndDate FROM Discounts WHERE DiscountCode = @Code AND IsActive = 1"; using (SqlCommand cmd = new SqlCommand(query, con)) { cmd.Parameters.AddWithValue("@Code", code); try { con.Open(); SqlDataReader reader = cmd.ExecuteReader(); if (reader.Read()) { DateTime? startDate = reader["StartDate"] as DateTime?; DateTime? endDate = reader["EndDate"] as DateTime?; if (startDate.HasValue && startDate.Value > DateTime.Now) { ShowMessage("This discount code is not yet active.", false); Session["AppliedDiscount"] = null; } else if (endDate.HasValue && endDate.Value.AddDays(1) < DateTime.Now) { ShowMessage("This discount code has expired.", false); Session["AppliedDiscount"] = null; } else { var discount = new { ID = Convert.ToInt32(reader["DiscountID"]), Code = code, Percentage = Convert.ToDecimal(reader["DiscountPercentage"]) }; Session["AppliedDiscount"] = discount; ShowMessage("Discount applied successfully!", true); } } else { Session["AppliedDiscount"] = null; ShowMessage("Invalid or inactive discount code.", false); } } catch (Exception ex) { ShowMessage("An error occurred.", false); System.Diagnostics.Debug.WriteLine("Discount Error: " + ex.Message); } } } CalculateTotals(); }
        protected void ApplyPointsButton_Click(object sender, EventArgs e) { if (!int.TryParse(PointsToRedeemBox.Text, out int pointsToRedeem) || pointsToRedeem <= 0) { ShowMessage("Please enter a valid number of points.", false); return; } LoadAvailablePoints(); if (pointsToRedeem > availablePoints) { ShowMessage("You do not have enough points to redeem.", false); return; } List<CartItem> cart = Session["Cart"] as List<CartItem>; decimal subtotal = cart.Sum(item => item.Total); decimal pointsToDollarRatio = SettingsService.GetDecimalSetting("PointsValueInDollars", 100.0m); decimal pointsValue = (pointsToDollarRatio > 0) ? (pointsToRedeem / pointsToDollarRatio) : 0; if (pointsValue > subtotal) { ShowMessage($"You can only redeem up to the subtotal amount (${subtotal:F2}).", false); return; } Session["RedeemedPoints"] = pointsToRedeem; ShowMessage($"{pointsToRedeem} points applied successfully!", true); CalculateTotals(); }
        protected void ConfirmOrderButton_Click(object sender, EventArgs e)
        {
            if (!Page.IsValid) return; List<CartItem> cart = Session["Cart"] as List<CartItem>; if (cart == null || cart.Count == 0) return; string prescriptionPath = null; if (CartRequiresPrescription(cart)) { if (!PrescriptionUpload.HasFile) { FailureText.Text = "Your order contains prescription items. Please upload a valid prescription."; ErrorMessage.Visible = true; return; } }
            if (PrescriptionUpload.HasFile) { try { string fileName = Guid.NewGuid().ToString() + Path.GetExtension(PrescriptionUpload.FileName); string folderPath = Server.MapPath("~/Uploads/Prescriptions/"); if (!Directory.Exists(folderPath)) Directory.CreateDirectory(folderPath); string savedFilePath = Path.Combine(folderPath, fileName); PrescriptionUpload.SaveAs(savedFilePath); prescriptionPath = "~/Uploads/Prescriptions/" + fileName; } catch (Exception ex) { FailureText.Text = "Error uploading prescription."; ErrorMessage.Visible = true; System.Diagnostics.Debug.WriteLine("Prescription Upload Error: " + ex.Message); return; } }
            string screenshotPath = null; if (ScreenshotUpload.HasFile) { try { string fileName = Guid.NewGuid().ToString() + Path.GetExtension(ScreenshotUpload.FileName); string folderPath = Server.MapPath("~/Uploads/Payments/"); if (!Directory.Exists(folderPath)) Directory.CreateDirectory(folderPath); screenshotPath = Path.Combine(folderPath, fileName); ScreenshotUpload.SaveAs(screenshotPath); screenshotPath = "~/Uploads/Payments/" + fileName; } catch (Exception ex) { FailureText.Text = "Error uploading payment screenshot."; ErrorMessage.Visible = true; System.Diagnostics.Debug.WriteLine("Screenshot Upload Error: " + ex.Message); return; } } else { FailureText.Text = "A payment screenshot is required."; ErrorMessage.Visible = true; return; }
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString; using (SqlConnection con = new SqlConnection(connectionString)) { con.Open(); SqlTransaction transaction = con.BeginTransaction(); try { decimal subtotal = cart.Sum(item => item.Total); decimal finalTotal = subtotal; int? discountId = null; int? redeemedPoints = null; if (Session["AppliedDiscount"] != null) { var discount = (dynamic)Session["AppliedDiscount"]; finalTotal -= subtotal * (discount.Percentage / 100); discountId = discount.ID; } if (Session["RedeemedPoints"] != null) { redeemedPoints = (int)Session["RedeemedPoints"]; decimal pointsToDollarRatio = SettingsService.GetDecimalSetting("PointsValueInDollars", 100.0m); if (pointsToDollarRatio > 0) { finalTotal -= redeemedPoints.Value / pointsToDollarRatio; } } if (finalTotal < 0) finalTotal = 0; string insertOrderQuery = "INSERT INTO Orders (CustomerID, OrderDate, TotalAmount, StatusID, ShippingAddress, DiscountID) OUTPUT INSERTED.OrderID VALUES (@CustomerID, GETDATE(), @TotalAmount, @StatusID, @ShippingAddress, @DiscountID);"; SqlCommand orderCmd = new SqlCommand(insertOrderQuery, con, transaction); orderCmd.Parameters.AddWithValue("@CustomerID", Session["UserID"]); orderCmd.Parameters.AddWithValue("@TotalAmount", finalTotal); orderCmd.Parameters.AddWithValue("@StatusID", 2); orderCmd.Parameters.AddWithValue("@ShippingAddress", ShippingAddressBox.Text.Trim()); orderCmd.Parameters.AddWithValue("@DiscountID", (object)discountId ?? DBNull.Value); int newOrderId = (int)orderCmd.ExecuteScalar(); foreach (var item in cart) { string insertItemsQuery = "INSERT INTO OrderItems (OrderID, ProductID, Quantity, PricePerUnit) VALUES (@OrderID, @ProductID, @Quantity, @PricePerUnit);"; SqlCommand itemCmd = new SqlCommand(insertItemsQuery, con, transaction); itemCmd.Parameters.AddWithValue("@OrderID", newOrderId); itemCmd.Parameters.AddWithValue("@ProductID", item.ProductID); itemCmd.Parameters.AddWithValue("@Quantity", item.Quantity); itemCmd.Parameters.AddWithValue("@PricePerUnit", item.Price); itemCmd.ExecuteNonQuery(); } string insertPaymentQuery = "INSERT INTO Payments (OrderID, PaymentDate, Amount, PaymentMethod, TransactionID, PaymentStatus) VALUES (@OrderID, GETDATE(), @Amount, 'QR Scan/Pay', @TransactionID, 'Pending Verification');"; SqlCommand paymentCmd = new SqlCommand(insertPaymentQuery, con, transaction); paymentCmd.Parameters.AddWithValue("@OrderID", newOrderId); paymentCmd.Parameters.AddWithValue("@Amount", finalTotal); paymentCmd.Parameters.AddWithValue("@TransactionID", screenshotPath); paymentCmd.ExecuteNonQuery(); if (!string.IsNullOrEmpty(prescriptionPath)) { string insertPrescriptionQuery = "INSERT INTO Prescriptions (OrderID, CustomerID, FilePath, Status) VALUES (@OrderID, @CustomerID, @FilePath, 'Pending Review');"; SqlCommand presCmd = new SqlCommand(insertPrescriptionQuery, con, transaction); presCmd.Parameters.AddWithValue("@OrderID", newOrderId); presCmd.Parameters.AddWithValue("@CustomerID", Session["UserID"]); presCmd.Parameters.AddWithValue("@FilePath", prescriptionPath); presCmd.ExecuteNonQuery(); } if (redeemedPoints.HasValue && redeemedPoints.Value > 0) { const string pointsQuery = "INSERT INTO LoyaltyPoints (CustomerID, Points, TransactionType, OrderID, TransactionDate) VALUES (@CustomerID, @Points, 'Redeemed', @OrderID, GETDATE())"; using (SqlCommand pointsCmd = new SqlCommand(pointsQuery, con, transaction)) { pointsCmd.Parameters.AddWithValue("@CustomerID", Session["UserID"]); pointsCmd.Parameters.AddWithValue("@Points", -redeemedPoints.Value); pointsCmd.Parameters.AddWithValue("@OrderID", newOrderId); pointsCmd.ExecuteNonQuery(); } } var staffIds = GetStaffUserIds(con, transaction); string staffNotificationMessage = $"A new order (#{newOrderId}) has been placed and is ready for review."; string staffNotificationLink = $"~/Pharmacist/OrderDetails.aspx?OrderID={newOrderId}"; foreach (var staffId in staffIds) { NotificationService.CreateNotification(staffId, staffNotificationMessage, staffNotificationLink); } string customerNotificationMessage = $"Your order (#{newOrderId}) has been successfully placed and is now under review."; string customerNotificationLink = $"~/Customer/OrderDetails.aspx?OrderID={newOrderId}"; NotificationService.CreateNotification(Convert.ToInt32(Session["UserID"]), customerNotificationMessage, customerNotificationLink); transaction.Commit(); Session["Cart"] = null; Session["AppliedDiscount"] = null; Session["RedeemedPoints"] = null; Response.Redirect("~/OrderSuccess.aspx?OrderID=" + newOrderId); } catch (Exception ex) { transaction.Rollback(); FailureText.Text = "An error occurred while placing your order."; ErrorMessage.Visible = true; System.Diagnostics.Debug.WriteLine("Checkout Error: " + ex.Message); } }
        }
        private List<int> GetStaffUserIds(SqlConnection con, SqlTransaction trans) { var staffIds = new List<int>(); const string query = "SELECT UserID FROM Users WHERE RoleID IN (1, 2)"; using (SqlCommand cmd = new SqlCommand(query, con, trans)) { using (SqlDataReader reader = cmd.ExecuteReader()) { while (reader.Read()) { staffIds.Add(Convert.ToInt32(reader["UserID"])); } } } return staffIds; }
        private bool CartRequiresPrescription(List<CartItem> cart) { if (cart == null || cart.Count == 0) return false; var productIds = cart.Select(item => item.ProductID).ToList(); string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString; using (SqlConnection con = new SqlConnection(connectionString)) { string query = $"SELECT COUNT(*) FROM Products WHERE ProductID IN ({string.Join(",", productIds)}) AND IsPrescriptionRequired = 1"; using (SqlCommand cmd = new SqlCommand(query, con)) { try { con.Open(); return (int)cmd.ExecuteScalar() > 0; } catch (Exception ex) { System.Diagnostics.Debug.WriteLine("Error checking prescription requirement: " + ex.Message); return true; } } } }
        private void ShowMessage(string message, bool isSuccess) { MessageText.Text = message; MessageAlert.Attributes["class"] = isSuccess ? "alert alert-success mt-3" : "alert alert-danger mt-3"; MessagePlaceholder.Visible = true; }
    }
}