//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated. 
// </auto-generated>
//------------------------------------------------------------------------------

namespace MediEase.Admin
{
    public partial class Dashboard
    {
        /// <summary>
        /// btnRefresh control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Button btnRefresh;

        /// <summary>
        /// lblTotalUsers control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblTotalUsers;

        /// <summary>
        /// lblTotalMedicines control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblTotalMedicines;

        /// <summary>
        /// lblTotalOrders control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblTotalOrders;

        /// <summary>
        /// lblMonthlyRevenue control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblMonthlyRevenue;

        /// <summary>
        /// lnkManageUsers control.
        /// </summary>
        protected global::System.Web.UI.WebControls.LinkButton lnkManageUsers;

        /// <summary>
        /// lnkManageMedicines control.
        /// </summary>
        protected global::System.Web.UI.WebControls.LinkButton lnkManageMedicines;

        /// <summary>
        /// lnkViewOrders control.
        /// </summary>
        protected global::System.Web.UI.WebControls.LinkButton lnkViewOrders;

        /// <summary>
        /// lnkSystemSettings control.
        /// </summary>
        protected global::System.Web.UI.WebControls.LinkButton lnkSystemSettings;

        /// <summary>
        /// gvRecentOrders control.
        /// </summary>
        protected global::System.Web.UI.WebControls.GridView gvRecentOrders;

        /// <summary>
        /// gvLowStock control.
        /// </summary>
        protected global::System.Web.UI.WebControls.GridView gvLowStock;
    }
}
