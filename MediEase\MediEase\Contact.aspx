﻿<%--
 =======================================================================
 FILE: Contact.aspx (Final Version with Form and Map)
 PURPOSE: Displays contact info, a map, and a form to send messages.
 PLACE IN: The root directory of your project.
 =======================================================================
--%>
<%@ Page Title="Contact Us" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="Contact.aspx.cs" Inherits="MediEase.Contact" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-5">
        <div class="text-center mb-5">
             <h1><asp:Literal ID="ContentTitle" runat="server">Contact & Location</asp:Literal></h1>
             <p class="lead">Find us on the map or send us a message directly.</p>
        </div>

        <div class="row mb-5">
            <div class="col-md-7">
                <div class="card shadow-sm h-100">
                    <div class="card-body p-0">
                        <iframe 
                            src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3437.363403319811!2d80.**************!3d29.56631198205423!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x39a37a6b8f3d4e6d%3A0x6b8f3d4e6d6b8f3d!2sDehimandu%2C%20Baitadi!5e0!3m2!1sen!2snp!4v1623308940521!5m2!1sen!2snp"
                            width="100%" 
                            height="450" 
                            style="border:0;" 
                            allowfullscreen="" 
                            loading="lazy"></iframe>
                    </div>
                </div>
            </div>
            <div class="col-md-5">
                <div class="card shadow-sm h-100">
                     <div class="card-body">
                        <asp:Literal ID="ContentBody" runat="server"></asp:Literal>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header">
                        <h4 class="mb-0">Send us a Message</h4>
                    </div>
                    <div class="card-body">
                         <asp:PlaceHolder ID="FormView" runat="server">
                            <div class="form-group">
                                <label>Your Name</label>
                                <asp:TextBox ID="NameBox" runat="server" CssClass="form-control" placeholder="John Doe"></asp:TextBox>
                                <asp:RequiredFieldValidator runat="server" ControlToValidate="NameBox" ErrorMessage="Your name is required." CssClass="text-danger" Display="Dynamic" />
                            </div>
                            <div class="form-group">
                                <label>Your Email</label>
                                <asp:TextBox ID="EmailBox" runat="server" CssClass="form-control" TextMode="Email" placeholder="<EMAIL>"></asp:TextBox>
                                <asp:RequiredFieldValidator runat="server" ControlToValidate="EmailBox" ErrorMessage="Your email is required." CssClass="text-danger" Display="Dynamic" />
                            </div>
                             <div class="form-group">
                                <label>Subject</label>
                                <asp:TextBox ID="SubjectBox" runat="server" CssClass="form-control" placeholder="e.g., Question about an order"></asp:TextBox>
                                <asp:RequiredFieldValidator runat="server" ControlToValidate="SubjectBox" ErrorMessage="A subject is required." CssClass="text-danger" Display="Dynamic" />
                            </div>
                            <div class="form-group">
                                <label>Message</label>
                                <asp:TextBox ID="MessageBox" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="5"></asp:TextBox>
                                 <asp:RequiredFieldValidator runat="server" ControlToValidate="MessageBox" ErrorMessage="A message is required." CssClass="text-danger" Display="Dynamic" />
                            </div>
                            <asp:Button ID="SubmitButton" runat="server" Text="Send Message" CssClass="btn btn-primary" OnClick="SubmitButton_Click" />
                        </asp:PlaceHolder>
                        <asp:PlaceHolder ID="SuccessMessage" runat="server" Visible="false">
                            <div class="alert alert-success">
                                <h5>Thank you!</h5>
                                <p>Your message has been sent successfully. We will get back to you shortly.</p>
                            </div>
                        </asp:PlaceHolder>
                    </div>
                </div>
            </div>
        </div>
    </div>
</asp:Content>