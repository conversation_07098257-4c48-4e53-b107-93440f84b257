﻿<%--
 =======================================================================
 FILE: AutoRefills.aspx
 PURPOSE: Allows a customer to set up and manage automatic refills.
 PLACE IN: The 'Customer' folder in your project root.
 =======================================================================
--%>
<%@ Page Title="Automatic Refills" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="AutoRefills.aspx.cs" Inherits="MediEase.Customer.AutoRefills" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>Automatic Refills</h1>
            <a href="Dashboard.aspx" class="btn btn-outline-secondary">&larr; Back to Dashboard</a>
        </div>

        <div class="row">
            <!-- Setup New Auto-Refill -->
            <div class="col-lg-5">
                <div class="card shadow-sm">
                    <div class="card-header">
                        <h4 class="mb-0">Setup a New Auto-Refill</h4>
                    </div>
                    <div class="card-body">
                         <asp:PlaceHolder ID="MessagePlaceholder" runat="server" Visible="false">
                            <div class="alert" id="MessageAlert" runat="server">
                                <asp:Literal ID="MessageText" runat="server"></asp:Literal>
                            </div>
                        </asp:PlaceHolder>
                        <p>Select a medication you have previously purchased to set up a convenient automatic refill schedule.</p>
                        <div class="form-group">
                            <label>Medication to Refill</label>
                            <asp:DropDownList ID="ProductDropDown" runat="server" CssClass="form-control"></asp:DropDownList>
                        </div>
                        <div class="form-group">
                            <label>Refill Frequency</label>
                             <asp:DropDownList ID="FrequencyDropDown" runat="server" CssClass="form-control">
                                 <asp:ListItem Value="30">Every 30 Days</asp:ListItem>
                                 <asp:ListItem Value="60">Every 60 Days</asp:ListItem>
                                 <asp:ListItem Value="90">Every 90 Days</asp:ListItem>
                             </asp:DropDownList>
                        </div>
                        <asp:Button ID="SetupRefillButton" runat="server" Text="Start Auto-Refill" CssClass="btn btn-primary" OnClick="SetupRefillButton_Click" />
                    </div>
                </div>
            </div>

            <!-- Current Auto-Refills -->
            <div class="col-lg-7">
                <h4>Your Current Auto-Refills</h4>
                <hr />
                <asp:GridView ID="AutoRefillsGrid" runat="server"
                    CssClass="table table-hover table-striped"
                    AutoGenerateColumns="False"
                    DataKeyNames="AutoRefillID"
                    GridLines="None"
                    OnRowDeleting="AutoRefillsGrid_RowDeleting"
                    EmptyDataText="You have not set up any automatic refills.">
                    <Columns>
                        <asp:BoundField DataField="Name" HeaderText="Medication" />
                        <asp:BoundField DataField="FrequencyInDays" HeaderText="Frequency" DataFormatString="{0} Days" />
                        <asp:BoundField DataField="NextRefillDate" HeaderText="Next Refill Date" DataFormatString="{0:MMM dd, yyyy}" />
                        <asp:TemplateField HeaderText="Actions">
                            <ItemTemplate>
                                <asp:LinkButton ID="CancelButton" runat="server" CommandName="Delete" CssClass="btn btn-sm btn-danger" OnClientClick="return confirm('Are you sure you want to cancel this auto-refill?');">Cancel</asp:LinkButton>
                            </ItemTemplate>
                        </asp:TemplateField>
                    </Columns>
                </asp:GridView>
            </div>
        </div>
    </div>
</asp:Content>