﻿<%@ Page Title="Log In" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="Login.aspx.cs" Inherits="MediEase.Login" %>
<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card mt-5 shadow-sm">
                <div class="card-body p-4">
                    <h2 class="card-title text-center mb-4">Log In</h2>
                    <asp:PlaceHolder runat="server" ID="ErrorMessage" Visible="false">
                        <div class="alert alert-danger">
                            <asp:Literal runat="server" ID="FailureText" />
                        </div>
                    </asp:PlaceHolder>
                    <div class="form-group">
                        <label>Email</label>
                        <asp:TextBox runat="server" ID="Email" CssClass="form-control" TextMode="Email" />
                        <asp:RequiredFieldValidator runat="server" ControlToValidate="Email" CssClass="text-danger" ErrorMessage="The email field is required." Display="Dynamic" />
                    </div>
                    <div class="form-group">
                        <label>Password</label>
                        <asp:TextBox runat="server" ID="Password" CssClass="form-control" TextMode="Password" />
                        <asp:RequiredFieldValidator runat="server" ControlToValidate="Password" CssClass="text-danger" ErrorMessage="The password field is required." Display="Dynamic" />
                    </div>
                    <div class="form-group">
                        <asp:Button runat="server" ID="LoginButton" OnClick="LogIn_Click" Text="Log in" CssClass="btn btn-primary btn-block" />
                    </div>
                    <div class="text-center">
                        <p><asp:HyperLink runat="server" ID="ForgotPasswordHyperLink">Forgot your password?</asp:HyperLink></p>
                        <p><asp:HyperLink runat="server" ID="RegisterHyperLink">Register as a new user</asp:HyperLink></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</asp:Content>