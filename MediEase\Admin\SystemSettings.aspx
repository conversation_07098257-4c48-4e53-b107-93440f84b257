<%@ Page Language="C#" AutoEventWireup="true" CodeFile="SystemSettings.aspx.cs" Inherits="Admin_SystemSettings" %>

<!DOCTYPE html>
<html lang="en">
<head runat="server">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Settings - MediEase Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <style>
        body { font-family: 'Inter', sans-serif; }
        .sidebar-link { display: flex; align-items: center; padding: 0.75rem 1rem; border-radius: 0.5rem; transition: background-color 0.2s, color 0.2s; }
        .sidebar-link:hover, .sidebar-link.active { background-color: #0d9488; color: white; }
        .sidebar-link i { width: 1.25rem; margin-right: 0.75rem; }
    </style>
</head>
<body class="bg-gray-100">
    <form id="form1" runat="server">
        <div class="flex h-screen bg-gray-100">
            <!-- Sidebar -->
            <aside class="w-64 bg-teal-600 text-white flex-col p-4 hidden md:flex">
                <div class="flex items-center space-x-2 pb-4 border-b border-teal-500">
                    <i class="fas fa-pills text-3xl"></i>
                    <span class="text-2xl font-bold">MediEase</span>
                </div>
                <nav class="mt-6 flex-grow">
                    <a href="Dashboard.aspx" class="sidebar-link"><i class="fas fa-tachometer-alt fa-fw"></i><span>Dashboard</span></a>
                    <a href="UserManagement.aspx" class="sidebar-link"><i class="fas fa-users-cog fa-fw"></i><span>User Management</span></a>
                    <a href="ProductManagement.aspx" class="sidebar-link"><i class="fas fa-capsules fa-fw"></i><span>Products</span></a>
                    <a href="Reports.aspx" class="sidebar-link"><i class="fas fa-chart-line fa-fw"></i><span>Reports</span></a>
                    <a href="SystemSettings.aspx" class="sidebar-link active"><i class="fas fa-cogs fa-fw"></i><span>System Settings</span></a>
                    <a href="AuditLog.aspx" class="sidebar-link"><i class="fas fa-clipboard-list fa-fw"></i><span>Audit Log</span></a>
                </nav>
                <div class="pt-4 border-t border-teal-500">
                    <asp:LinkButton ID="btnLogout" runat="server" OnClick="btnLogout_Click" CssClass="sidebar-link w-full"><i class="fas fa-sign-out-alt fa-fw"></i><span>Logout</span></asp:LinkButton>
                </div>
            </aside>

            <!-- Main Content -->
            <main class="flex-1 flex flex-col">
                <header class="bg-white shadow-sm p-4">
                    <h1 class="text-2xl font-bold text-gray-800">System Settings</h1>
                </header>

                <div class="flex-1 p-6 overflow-y-auto">
                    <div class="max-w-4xl mx-auto bg-white p-8 rounded-lg shadow">
                        
                        <!-- Server Message -->
                        <asp:Literal ID="litMessage" runat="server"></asp:Literal>

                        <div class="space-y-6">
                            <asp:Repeater ID="rptSettings" runat="server">
                                <ItemTemplate>
                                    <div>
                                        <asp:HiddenField ID="hdnSettingKey" runat="server" Value='<%# Eval("SettingKey") %>' />
                                        <asp:HiddenField ID="hdnOldValue" runat="server" Value='<%# Eval("SettingValue") %>' />
                                        
                                        <label class="block text-sm font-medium text-gray-900"><%# Eval("SettingKey") %></label>
                                        <p class="text-sm text-gray-500 mb-2"><%# Eval("Description") %></p>
                                        
                                        <asp:TextBox ID="txtSettingValue" runat="server" Text='<%# Eval("SettingValue") %>' CssClass="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teal-500 focus:border-teal-500 sm:text-sm"></asp:TextBox>
                                    </div>
                                </ItemTemplate>
                            </asp:Repeater>
                        </div>

                        <!-- Action Buttons -->
                        <div class="mt-8 pt-6 border-t border-gray-200 flex justify-end">
                            <asp:Button ID="btnSaveChanges" runat="server" Text="Save Changes" OnClick="btnSaveChanges_Click" CssClass="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500" />
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </form>
</body>
</html>
