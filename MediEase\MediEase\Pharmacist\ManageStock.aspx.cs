﻿/* FILE: Pharmacist/ManageStock.aspx.cs */
using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace MediEase.Pharmacist
{
    public partial class ManageStock : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (Session["UserID"] == null || Session["RoleName"]?.ToString() != "Pharmacist") { Response.Redirect("~/Login.aspx"); return; }
            if (!IsPostBack) { BindStockGrid(); }
        }

        private void BindStockGrid()
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                const string query = "SELECT p.ProductID, p.Name, p.ReorderLevel, ISNULL(SUM(i.QuantityInStock), 0) AS TotalStock FROM Products p LEFT JOIN Inventory i ON p.ProductID = i.ProductID WHERE p.IsActive = 1 GROUP BY p.ProductID, p.Name, p.ReorderLevel ORDER BY p.Name;";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    try
                    {
                        con.Open();
                        SqlDataAdapter sda = new SqlDataAdapter(cmd);
                        DataTable dt = new DataTable();
                        sda.Fill(dt);
                        StockGrid.DataSource = dt;
                        StockGrid.DataBind();
                    }
                    catch (Exception ex) { System.Diagnostics.Debug.WriteLine("Error binding stock grid: " + ex.Message); }
                }
            }
        }

        protected void StockGrid_PageIndexChanging(object sender, GridViewPageEventArgs e)
        {
            StockGrid.PageIndex = e.NewPageIndex;
            BindStockGrid();
        }

        protected void StockGrid_RowDataBound(object sender, GridViewRowEventArgs e)
        {
            if (e.Row.RowType == DataControlRowType.DataRow)
            {
                Label statusLabel = (Label)e.Row.FindControl("StatusLabel");
                int totalStock = Convert.ToInt32(DataBinder.Eval(e.Row.DataItem, "TotalStock"));
                int reorderLevel = Convert.ToInt32(DataBinder.Eval(e.Row.DataItem, "ReorderLevel"));

                if (totalStock == 0) { statusLabel.Text = "Out of Stock"; statusLabel.CssClass = "badge badge-pill badge-danger"; }
                else if (totalStock <= reorderLevel) { statusLabel.Text = "Low Stock"; statusLabel.CssClass = "badge badge-pill badge-warning"; }
                else { statusLabel.Text = "OK"; statusLabel.CssClass = "badge badge-pill badge-success"; }
            }
        }
    }
}