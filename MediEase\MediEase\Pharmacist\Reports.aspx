﻿<%--
 =======================================================================
 FILE: Reports.aspx (Final Version)
 =======================================================================
--%>
<%@ Page Title="Sales & Stock Reports" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="Reports.aspx.cs" Inherits="MediEase.Pharmacist.Reports" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>Reports</h1>
            <a href="Dashboard.aspx" class="btn btn-outline-secondary">&larr; Back to Dashboard</a>
        </div>

        <div class="card shadow-sm">
            <div class="card-header">
                <ul class="nav nav-tabs card-header-tabs">
                    <li class="nav-item"><asp:LinkButton ID="SalesTab" runat="server" CssClass="nav-link" OnClick="Tab_Click" CommandArgument="Sales">Sales Report</asp:LinkButton></li>
                    <li class="nav-item"><asp:LinkButton ID="StockTab" runat="server" CssClass="nav-link" OnClick="Tab_Click" CommandArgument="Stock">Stock Report</asp:LinkButton></li>
                    <li class="nav-item"><asp:LinkButton ID="ExpiringTab" runat="server" CssClass="nav-link" OnClick="Tab_Click" CommandArgument="Expiring">Expiring Soon</asp:LinkButton></li>
                </ul>
            </div>
            <div class="card-body">
                <asp:PlaceHolder ID="SalesReportView" runat="server">
                    <div class="form-row align-items-end">
                        <div class="form-group col-md-5"><label>Start Date</label><asp:TextBox ID="StartDateBox" runat="server" CssClass="form-control" TextMode="Date"></asp:TextBox></div>
                        <div class="form-group col-md-5"><label>End Date</label><asp:TextBox ID="EndDateBox" runat="server" CssClass="form-control" TextMode="Date"></asp:TextBox></div>
                        <div class="form-group col-md-2"><asp:Button ID="GenerateReportButton" runat="server" Text="Generate" CssClass="btn btn-primary btn-block" OnClick="GenerateReportButton_Click" /></div>
                    </div><hr />
                    <asp:PlaceHolder ID="ReportView" runat="server" Visible="false">
                        <h5 class="mb-3">Sales Report for <asp:Literal ID="DateRangeLabel" runat="server"></asp:Literal></h5>
                        <div class="row text-center mb-3">
                            <div class="col-md-6"><div class="card bg-light"><div class="card-body"><h6 class="card-title">Total Sales</h6><p class="h3"><asp:Literal ID="TotalSalesLabel" runat="server"></asp:Literal></p></div></div></div>
                            <div class="col-md-6"><div class="card bg-light"><div class="card-body"><h6 class="card-title">Total Items Sold</h6><p class="h3"><asp:Literal ID="TotalItemsLabel" runat="server"></asp:Literal></p></div></div></div>
                        </div>
                        <asp:GridView ID="SalesReportGrid" runat="server" CssClass="table table-striped" AutoGenerateColumns="False" GridLines="None" EmptyDataText="No sales data found for the selected date range.">
                            <Columns><asp:BoundField DataField="ProductID" HeaderText="Product ID" /><asp:BoundField DataField="Name" HeaderText="Product Name" /><asp:BoundField DataField="TotalQuantity" HeaderText="Units Sold" /><asp:BoundField DataField="TotalRevenue" HeaderText="Total Revenue" DataFormatString="{0:C}" /></Columns>
                        </asp:GridView>
                    </asp:PlaceHolder>
                </asp:PlaceHolder>
                <asp:PlaceHolder ID="StockReportView" runat="server" Visible="false">
                    <h5 class="mb-3">Current Inventory Status Report</h5>
                    <asp:GridView ID="StockReportGrid" runat="server" CssClass="table table-hover table-striped" AutoGenerateColumns="False" GridLines="None" OnRowDataBound="StockReportGrid_RowDataBound">
                        <Columns><asp:BoundField DataField="ProductID" HeaderText="ID" /><asp:BoundField DataField="Name" HeaderText="Product Name" /><asp:BoundField DataField="TotalStock" HeaderText="Total Stock" /><asp:TemplateField HeaderText="Stock Status"><ItemTemplate><asp:Label ID="StockStatusLabel" runat="server" CssClass="badge badge-pill"></asp:Label></ItemTemplate></asp:TemplateField><asp:TemplateField HeaderText="Expiry Status"><ItemTemplate><asp:Label ID="ExpiryStatusLabel" runat="server" CssClass="badge badge-pill"></asp:Label></ItemTemplate></asp:TemplateField></Columns>
                         <EmptyDataTemplate><div class="alert alert-info text-center">No stock data available.</div></EmptyDataTemplate>
                    </asp:GridView>
                </asp:PlaceHolder>
                 <asp:PlaceHolder ID="ExpiringReportView" runat="server" Visible="false">
                    <h5 class="mb-3">Products Expiring Within 90 Days</h5>
                     <asp:GridView ID="ExpiringReportGrid" runat="server" CssClass="table table-hover table-striped" AutoGenerateColumns="False" GridLines="None">
                        <Columns><asp:BoundField DataField="ProductID" HeaderText="ID" /><asp:BoundField DataField="Name" HeaderText="Product Name" /><asp:BoundField DataField="BatchNumber" HeaderText="Batch Number" /><asp:BoundField DataField="QuantityInStock" HeaderText="Quantity in Batch" /><asp:BoundField DataField="ExpiryDate" HeaderText="Expiry Date" DataFormatString="{0:MMM dd, yyyy}" /></Columns>
                         <EmptyDataTemplate><div class="alert alert-success text-center">No products are expiring soon.</div></EmptyDataTemplate>
                    </asp:GridView>
                 </asp:PlaceHolder>
            </div>
        </div>
    </div>
</asp:Content>