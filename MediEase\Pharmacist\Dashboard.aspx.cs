using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web.UI;

public partial class Pharmacist_Dashboard : System.Web.UI.Page
{
    protected void Page_Load(object sender, EventArgs e)
    {
        // --- SECURITY CHECK ---
        // If the user is not logged in or is not a Pharmacist, redirect to the login page.
        if (Session["UserId"] == null || Session["UserRole"]?.ToString() != "Pharmacist")
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        if (!IsPostBack)
        {
            LoadDashboardData();
        }
    }

    /// <summary>
    /// Loads all data required for the pharmacist dashboard.
    /// </summary>
    private void LoadDashboardData()
    {
        string firstName = Session["UserFirstName"]?.ToString() ?? "Pharmacist";

        // Set welcome message and user initial
        litWelcomeMessage.Text = $"Welcome, {firstName}!";
        litUserInitial.Text = $"<div class='w-10 h-10 rounded-full bg-teal-500 text-white flex items-center justify-center font-bold text-lg'>{firstName[0]}</div>";

        string connectionString = ConfigurationManager.ConnectionStrings["MediEaseDB"].ConnectionString;
        using (SqlConnection con = new SqlConnection(connectionString))
        {
            try
            {
                con.Open();
                BindDashboardStats(con);
                BindRecentOrders(con);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Pharmacist Dashboard Load Error: {ex.Message}");
                litWelcomeMessage.Text = "Error loading dashboard data.";
            }
        }
    }

    /// <summary>
    /// Fetches and binds key statistics for the pharmacist.
    /// </summary>
    private void BindDashboardStats(SqlConnection con)
    {
        string query = @"
            SELECT 
                (SELECT COUNT(OrderId) FROM Orders WHERE Status = 'Pending') AS NewOrders,
                (SELECT COUNT(PrescriptionId) FROM Prescriptions WHERE Status = 'Pending') AS PendingPrescriptions,
                (SELECT COUNT(MedicineId) FROM Medicines WHERE StockQuantity <= ReorderLevel) AS LowStockItems,
                (SELECT COUNT(OrderId) FROM Orders WHERE Status = 'Processing') AS OrdersToShip;";

        using (SqlCommand cmd = new SqlCommand(query, con))
        {
            using (SqlDataReader reader = cmd.ExecuteReader())
            {
                if (reader.Read())
                {
                    litNewOrders.Text = reader["NewOrders"].ToString();
                    litPendingPrescriptions.Text = reader["PendingPrescriptions"].ToString();
                    litLowStockItems.Text = reader["LowStockItems"].ToString();
                    litOrdersToShip.Text = reader["OrdersToShip"].ToString();
                }
            }
        }
    }

    /// <summary>
    /// Fetches the top 10 recent orders requiring action and binds them to the GridView.
    /// </summary>
    private void BindRecentOrders(SqlConnection con)
    {
        string query = @"
            SELECT TOP 10 
                o.OrderId, 
                o.OrderNumber, 
                o.OrderDate, 
                o.TotalAmount, 
                o.Status,
                u.FirstName + ' ' + u.LastName AS CustomerName
            FROM Orders o
            INNER JOIN Users u ON o.CustomerId = u.UserId
            WHERE o.Status IN ('Pending', 'Processing')
            ORDER BY o.OrderDate ASC"; // Show oldest orders first

        using (SqlDataAdapter sda = new SqlDataAdapter(query, con))
        {
            DataTable dt = new DataTable();
            sda.Fill(dt);
            gvRecentOrders.DataSource = dt;
            gvRecentOrders.DataBind();
        }
    }

    /// <summary>
    /// Handles the click event for the logout button.
    /// </summary>
    protected void btnLogout_Click(object sender, EventArgs e)
    {
        Session.Clear();
        Session.Abandon();
        Response.Redirect("~/Login.aspx");
    }

    /// <summary>
    /// Helper function to return a Tailwind CSS class based on the order status for styling.
    /// </summary>
    protected string GetStatusClass(string status)
    {
        switch (status.ToLower())
        {
            case "pending":
                return "bg-yellow-100 text-yellow-800";
            case "processing":
                return "bg-blue-100 text-blue-800";
            case "shipped":
                return "bg-purple-100 text-purple-800";
            case "delivered":
                return "bg-green-100 text-green-800";
            case "cancelled":
                return "bg-red-100 text-red-800";
            default:
                return "bg-gray-100 text-gray-800";
        }
    }
}
