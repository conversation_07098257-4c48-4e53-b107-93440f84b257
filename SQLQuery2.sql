﻿/*
 =======================================================================
 MediEase: Database Seeding Script
 PURPOSE: Populates the database with sample data for testing.
 HOW TO USE: Run this script in SQL Server Management Studio against
             your MediEase database.
 =======================================================================
*/

-- Clear existing data to prevent errors if run multiple times (optional)
-- Use with caution, as this will delete all data in these tables.
/*
DELETE FROM Inventory;
DELETE FROM Pricing;
DELETE FROM Products;
DELETE FROM Categories;
DELETE FROM Brands;
DELETE FROM UserProfiles;
DELETE FROM Users WHERE UserID > 1; -- Keep the original admin
*/

PRINT 'Starting to seed database...';

-- =======================================================================
-- 1. SEED USERS & USER PROFILES
-- =======================================================================
-- The original Admin user (UserID=1) and basic Roles are already created by the schema script.
-- Let's add a Pharmacist and two Customers.

-- Pharmacist User
IF NOT EXISTS (SELECT 1 FROM Users WHERE Email = '<EMAIL>')
BEGIN
    INSERT INTO Users (Email, Password, RoleID, IsActive) VALUES ('<EMAIL>', 'PharmacistPass123', 2, 1);
    DECLARE @PharmacistUserID INT = SCOPE_IDENTITY();
    INSERT INTO UserProfiles (UserID, FirstName, LastName, PhoneNumber) VALUES (@PharmacistUserID, 'Susan', 'Miller', '555-0101');
    PRINT 'Pharmacist user created.';
END

-- Customer 1
IF NOT EXISTS (SELECT 1 FROM Users WHERE Email = '<EMAIL>')
BEGIN
    INSERT INTO Users (Email, Password, RoleID, IsActive) VALUES ('<EMAIL>', 'JohnPass123', 3, 1);
    DECLARE @Customer1UserID INT = SCOPE_IDENTITY();
    INSERT INTO UserProfiles (UserID, FirstName, LastName, PhoneNumber, Address, DateOfBirth) VALUES (@Customer1UserID, 'John', 'Doe', '555-0102', '123 Maple Street, Anytown', '1985-05-20');
    PRINT 'Customer 1 (John Doe) created.';
END

-- Customer 2
IF NOT EXISTS (SELECT 1 FROM Users WHERE Email = '<EMAIL>')
BEGIN
    INSERT INTO Users (Email, Password, RoleID, IsActive) VALUES ('<EMAIL>', 'JanePass123', 3, 1);
    DECLARE @Customer2UserID INT = SCOPE_IDENTITY();
    INSERT INTO UserProfiles (UserID, FirstName, LastName, PhoneNumber, Address, DateOfBirth) VALUES (@Customer2UserID, 'Jane', 'Smith', '555-0103', '456 Oak Avenue, Sometown', '1992-11-30');
    PRINT 'Customer 2 (Jane Smith) created.';
END
GO


-- =======================================================================
-- 2. SEED BRANDS & CATEGORIES
-- =======================================================================

IF NOT EXISTS (SELECT 1 FROM Brands WHERE BrandName = 'MediHealth') INSERT INTO Brands (BrandName) VALUES ('MediHealth');
IF NOT EXISTS (SELECT 1 FROM Brands WHERE BrandName = 'Wellness Co') INSERT INTO Brands (BrandName) VALUES ('Wellness Co');
IF NOT EXISTS (SELECT 1 FROM Brands WHERE BrandName = 'PharmaCare') INSERT INTO Brands (BrandName) VALUES ('PharmaCare');
IF NOT EXISTS (SELECT 1 FROM Brands WHERE BrandName = 'HealthFirst') INSERT INTO Brands (BrandName) VALUES ('HealthFirst');
PRINT 'Brands seeded.';

IF NOT EXISTS (SELECT 1 FROM Categories WHERE CategoryName = 'Pain Relief') INSERT INTO Categories (CategoryName, Description) VALUES ('Pain Relief', 'Medications for relieving various types of pain.');
IF NOT EXISTS (SELECT 1 FROM Categories WHERE CategoryName = 'Vitamins & Supplements') INSERT INTO Categories (CategoryName, Description) VALUES ('Vitamins & Supplements', 'Products to supplement your diet and health.');
IF NOT EXISTS (SELECT 1 FROM Categories WHERE CategoryName = 'Cold & Flu') INSERT INTO Categories (CategoryName, Description) VALUES ('Cold & Flu', 'Remedies for common cold and flu symptoms.');
IF NOT EXISTS (SELECT 1 FROM Categories WHERE CategoryName = 'Allergy Relief') INSERT INTO Categories (CategoryName, Description) VALUES ('Allergy Relief', 'Medications for seasonal and other allergies.');
PRINT 'Categories seeded.';
GO


-- =======================================================================
-- 3. SEED PRODUCTS, PRICING, AND INVENTORY
-- =======================================================================
-- We'll declare variables for IDs to make the script easier to read.
DECLARE @AdminID INT = (SELECT UserID FROM Users WHERE Email = '<EMAIL>');

-- Product 1: Paracetamol
IF NOT EXISTS (SELECT 1 FROM Products WHERE Name = 'Paracetamol')
BEGIN
    DECLARE @Brand1 INT = (SELECT BrandID FROM Brands WHERE BrandName = 'MediHealth');
    DECLARE @Cat1 INT = (SELECT CategoryID FROM Categories WHERE CategoryName = 'Pain Relief');
    INSERT INTO Products (Name, Description, CategoryID, BrandID, Form, Strength, IsPrescriptionRequired, ApprovedByAdminID)
    VALUES ('Paracetamol', 'Effective for headaches, muscle aches, and fever.', @Cat1, @Brand1, 'Tablet', '500mg', 0, @AdminID);
    
    DECLARE @Product1ID INT = SCOPE_IDENTITY();
    INSERT INTO Pricing (ProductID, Price, SetByUserID) VALUES (@Product1ID, 5.99, @AdminID);
    INSERT INTO Inventory (ProductID, BatchNumber, ExpiryDate, QuantityInStock, ReorderLevel, LastUpdatedByUserID)
    VALUES (@Product1ID, 'BATCH-P500-001', '2026-12-31', 250, 50, @AdminID);
    PRINT 'Product: Paracetamol created.';
END

-- Product 2: Vitamin C
IF NOT EXISTS (SELECT 1 FROM Products WHERE Name = 'Vitamin C Chewables')
BEGIN
    DECLARE @Brand2 INT = (SELECT BrandID FROM Brands WHERE BrandName = 'Wellness Co');
    DECLARE @Cat2 INT = (SELECT CategoryID FROM Categories WHERE CategoryName = 'Vitamins & Supplements');
    INSERT INTO Products (Name, Description, CategoryID, BrandID, Form, Strength, IsPrescriptionRequired, ApprovedByAdminID)
    VALUES ('Vitamin C Chewables', 'Boosts immune system. Orange flavor.', @Cat2, @Brand2, 'Tablet', '1000mg', 0, @AdminID);
    
    DECLARE @Product2ID INT = SCOPE_IDENTITY();
    INSERT INTO Pricing (ProductID, Price, SetByUserID) VALUES (@Product2ID, 12.50, @AdminID);
    INSERT INTO Inventory (ProductID, BatchNumber, ExpiryDate, QuantityInStock, ReorderLevel, LastUpdatedByUserID)
    VALUES (@Product2ID, 'BATCH-VC1000-001', '2025-08-31', 150, 30, @AdminID);
    PRINT 'Product: Vitamin C created.';
END

-- Product 3: Ibuprofen
IF NOT EXISTS (SELECT 1 FROM Products WHERE Name = 'Ibuprofen')
BEGIN
    DECLARE @Brand3 INT = (SELECT BrandID FROM Brands WHERE BrandName = 'HealthFirst');
    DECLARE @Cat3 INT = (SELECT CategoryID FROM Categories WHERE CategoryName = 'Pain Relief');
    INSERT INTO Products (Name, Description, CategoryID, BrandID, Form, Strength, IsPrescriptionRequired, ApprovedByAdminID)
    VALUES ('Ibuprofen', 'Anti-inflammatory drug for pain relief.', @Cat3, @Brand3, 'Capsule', '200mg', 0, @AdminID);
    
    DECLARE @Product3ID INT = SCOPE_IDENTITY();
    INSERT INTO Pricing (ProductID, Price, SetByUserID) VALUES (@Product3ID, 8.75, @AdminID);
    INSERT INTO Inventory (ProductID, BatchNumber, ExpiryDate, QuantityInStock, ReorderLevel, LastUpdatedByUserID)
    VALUES (@Product3ID, 'BATCH-IB200-001', '2026-10-31', 300, 50, @AdminID);
    PRINT 'Product: Ibuprofen created.';
END

-- Product 4: Loratadine
IF NOT EXISTS (SELECT 1 FROM Products WHERE Name = 'Loratadine Allergy Relief')
BEGIN
    DECLARE @Brand4 INT = (SELECT BrandID FROM Brands WHERE BrandName = 'PharmaCare');
    DECLARE @Cat4 INT = (SELECT CategoryID FROM Categories WHERE CategoryName = 'Allergy Relief');
    INSERT INTO Products (Name, Description, CategoryID, BrandID, Form, Strength, IsPrescriptionRequired, ApprovedByAdminID)
    VALUES ('Loratadine Allergy Relief', '24-hour non-drowsy allergy relief.', @Cat4, @Brand4, 'Tablet', '10mg', 0, @AdminID);
    
    DECLARE @Product4ID INT = SCOPE_IDENTITY();
    INSERT INTO Pricing (ProductID, Price, SetByUserID) VALUES (@Product4ID, 15.99, @AdminID);
    INSERT INTO Inventory (ProductID, BatchNumber, ExpiryDate, QuantityInStock, ReorderLevel, LastUpdatedByUserID)
    VALUES (@Product4ID, 'BATCH-LR10-001', '2025-05-31', 120, 25, @AdminID);
    PRINT 'Product: Loratadine created.';
END

-- Product 5: Cold & Flu Syrup
IF NOT EXISTS (SELECT 1 FROM Products WHERE Name = 'Nighttime Cold & Flu Syrup')
BEGIN
    DECLARE @Brand5 INT = (SELECT BrandID FROM Brands WHERE BrandName = 'MediHealth');
    DECLARE @Cat5 INT = (SELECT CategoryID FROM Categories WHERE CategoryName = 'Cold & Flu');
    INSERT INTO Products (Name, Description, CategoryID, BrandID, Form, Strength, IsPrescriptionRequired, ApprovedByAdminID)
    VALUES ('Nighttime Cold & Flu Syrup', 'Multi-symptom relief to help you sleep.', @Cat5, @Brand5, 'Syrup', '250ml', 0, @AdminID);
    
    DECLARE @Product5ID INT = SCOPE_IDENTITY();
    INSERT INTO Pricing (ProductID, Price, SetByUserID) VALUES (@Product5ID, 11.25, @AdminID);
    INSERT INTO Inventory (ProductID, BatchNumber, ExpiryDate, QuantityInStock, ReorderLevel, LastUpdatedByUserID)
    VALUES (@Product5ID, 'BATCH-CFS250-001', '2025-09-30', 90, 20, @AdminID);
    PRINT 'Product: Cold & Flu Syrup created.';
END

-- Add 5 more products for a total of 10
-- Product 6
IF NOT EXISTS (SELECT 1 FROM Products WHERE Name = 'Aspirin')
BEGIN
    INSERT INTO Products (Name, Description, CategoryID, BrandID, Form, Strength, IsPrescriptionRequired, ApprovedByAdminID) VALUES ('Aspirin', 'Low dose for heart health.', (SELECT CategoryID FROM Categories WHERE CategoryName = 'Pain Relief'), (SELECT BrandID FROM Brands WHERE BrandName = 'HealthFirst'), 'Tablet', '81mg', 0, @AdminID);
    DECLARE @Product6ID INT = SCOPE_IDENTITY();
    INSERT INTO Pricing (ProductID, Price, SetByUserID) VALUES (@Product6ID, 9.99, @AdminID);
    INSERT INTO Inventory (ProductID, BatchNumber, ExpiryDate, QuantityInStock, ReorderLevel, LastUpdatedByUserID) VALUES (@Product6ID, 'BATCH-AS81-001', '2027-01-31', 500, 100, @AdminID);
    PRINT 'Product: Aspirin created.';
END

-- Product 7
IF NOT EXISTS (SELECT 1 FROM Products WHERE Name = 'Vitamin D3')
BEGIN
    INSERT INTO Products (Name, Description, CategoryID, BrandID, Form, Strength, IsPrescriptionRequired, ApprovedByAdminID) VALUES ('Vitamin D3', 'Supports bone health and immune function.', (SELECT CategoryID FROM Categories WHERE CategoryName = 'Vitamins & Supplements'), (SELECT BrandID FROM Brands WHERE BrandName = 'Wellness Co'), 'Softgel', '5000 IU', 0, @AdminID);
    DECLARE @Product7ID INT = SCOPE_IDENTITY();
    INSERT INTO Pricing (ProductID, Price, SetByUserID) VALUES (@Product7ID, 18.00, @AdminID);
    INSERT INTO Inventory (ProductID, BatchNumber, ExpiryDate, QuantityInStock, ReorderLevel, LastUpdatedByUserID) VALUES (@Product7ID, 'BATCH-VD5000-001', '2026-06-30', 180, 40, @AdminID);
    PRINT 'Product: Vitamin D3 created.';
END

-- Product 8
IF NOT EXISTS (SELECT 1 FROM Products WHERE Name = 'Cough Drops')
BEGIN
    INSERT INTO Products (Name, Description, CategoryID, BrandID, Form, Strength, IsPrescriptionRequired, ApprovedByAdminID) VALUES ('Cough Drops', 'Soothing honey and lemon flavor.', (SELECT CategoryID FROM Categories WHERE CategoryName = 'Cold & Flu'), (SELECT BrandID FROM Brands WHERE BrandName = 'PharmaCare'), 'Lozenge', '30 drops', 0, @AdminID);
    DECLARE @Product8ID INT = SCOPE_IDENTITY();
    INSERT INTO Pricing (ProductID, Price, SetByUserID) VALUES (@Product8ID, 4.50, @AdminID);
    INSERT INTO Inventory (ProductID, BatchNumber, ExpiryDate, QuantityInStock, ReorderLevel, LastUpdatedByUserID) VALUES (@Product8ID, 'BATCH-CD30-001', '2025-11-30', 400, 80, @AdminID);
    PRINT 'Product: Cough Drops created.';
END

-- Product 9 (Prescription Required)
IF NOT EXISTS (SELECT 1 FROM Products WHERE Name = 'Amoxicillin')
BEGIN
    INSERT INTO Products (Name, Description, CategoryID, BrandID, Form, Strength, IsPrescriptionRequired, ApprovedByAdminID) VALUES ('Amoxicillin', 'Antibiotic for treating bacterial infections.', (SELECT CategoryID FROM Categories WHERE CategoryName = 'Pain Relief'), (SELECT BrandID FROM Brands WHERE BrandName = 'MediHealth'), 'Capsule', '500mg', 1, @AdminID);
    DECLARE @Product9ID INT = SCOPE_IDENTITY();
    INSERT INTO Pricing (ProductID, Price, SetByUserID) VALUES (@Product9ID, 25.00, @AdminID);
    INSERT INTO Inventory (ProductID, BatchNumber, ExpiryDate, QuantityInStock, ReorderLevel, LastUpdatedByUserID) VALUES (@Product9ID, 'BATCH-AMX500-001', '2026-02-28', 75, 15, @AdminID);
    PRINT 'Product: Amoxicillin (Prescription) created.';
END

-- Product 10
IF NOT EXISTS (SELECT 1 FROM Products WHERE Name = 'Calcium + Magnesium')
BEGIN
    INSERT INTO Products (Name, Description, CategoryID, BrandID, Form, Strength, IsPrescriptionRequired, ApprovedByAdminID) VALUES ('Calcium + Magnesium', 'For strong bones and muscle function.', (SELECT CategoryID FROM Categories WHERE CategoryName = 'Vitamins & Supplements'), (SELECT BrandID FROM Brands WHERE BrandName = 'Wellness Co'), 'Tablet', '600mg', 0, @AdminID);
    DECLARE @Product10ID INT = SCOPE_IDENTITY();
    INSERT INTO Pricing (ProductID, Price, SetByUserID) VALUES (@Product10ID, 14.20, @AdminID);
    INSERT INTO Inventory (ProductID, BatchNumber, ExpiryDate, QuantityInStock, ReorderLevel, LastUpdatedByUserID) VALUES (@Product10ID, 'BATCH-CM600-001', '2025-07-31', 220, 45, @AdminID);
    PRINT 'Product: Calcium + Magnesium created.';
END
GO

PRINT 'Database seeding complete.';

