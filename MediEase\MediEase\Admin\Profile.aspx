﻿<%--
 =======================================================================
 FILE: Profile.aspx (Final Version)
 =======================================================================
--%>
<%@ Page Title="My Profile" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="Profile.aspx.cs" Inherits="MediEase.Admin.Profile" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>My Admin Profile</h1>
             <a href="Dashboard.aspx" class="btn btn-outline-secondary">&larr; Back to Dashboard</a>
        </div>

        <asp:PlaceHolder ID="MessagePlaceholder" runat="server" Visible="false">
            <div class="alert" id="MessageAlert" runat="server">
                <asp:Literal ID="MessageText" runat="server"></asp:Literal>
            </div>
        </asp:PlaceHolder>

        <div class="row">
            <div class="col-lg-7">
                <div class="card shadow-sm mb-4">
                    <div class="card-header"><h4 class="mb-0">My Details</h4></div>
                    <div class="card-body">
                        <div class="form-row">
                            <div class="form-group col-md-6"><label>First Name</label><asp:TextBox ID="FirstNameBox" runat="server" CssClass="form-control"></asp:TextBox></div>
                             <div class="form-group col-md-6"><label>Last Name</label><asp:TextBox ID="LastNameBox" runat="server" CssClass="form-control"></asp:TextBox></div>
                        </div>
                        <div class="form-group"><label>Phone Number</label><asp:TextBox ID="PhoneBox" runat="server" CssClass="form-control"></asp:TextBox></div>
                         <div class="form-group"><label>Address</label><asp:TextBox ID="AddressBox" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="3"></asp:TextBox></div>
                         <div class="form-group"><label>Date of Birth</label><asp:TextBox ID="DobBox" runat="server" CssClass="form-control" TextMode="Date"></asp:TextBox></div>
                        <asp:Button ID="SaveChangesButton" runat="server" Text="Save Changes" CssClass="btn btn-primary" OnClick="SaveChangesButton_Click" />
                    </div>
                </div>
            </div>
             <div class="col-lg-5">
                <div class="card shadow-sm mb-4">
                    <div class="card-header"><h4 class="mb-0">Change Password</h4></div>
                    <div class="card-body">
                        <div class="form-group"><label>Current Password</label><asp:TextBox ID="CurrentPasswordBox" runat="server" CssClass="form-control" TextMode="Password"></asp:TextBox></div>
                         <div class="form-group"><label>New Password</label><asp:TextBox ID="NewPasswordBox" runat="server" CssClass="form-control" TextMode="Password"></asp:TextBox></div>
                         <div class="form-group"><label>Confirm New Password</label><asp:TextBox ID="ConfirmNewPasswordBox" runat="server" CssClass="form-control" TextMode="Password"></asp:TextBox><asp:CompareValidator runat="server" ControlToValidate="ConfirmNewPasswordBox" ControlToCompare="NewPasswordBox" ErrorMessage="New passwords do not match." CssClass="text-danger" Display="Dynamic" /></div>
                        <asp:Button ID="UpdatePasswordButton" runat="server" Text="Update Password" CssClass="btn btn-warning" OnClick="UpdatePasswordButton_Click" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</asp:Content>