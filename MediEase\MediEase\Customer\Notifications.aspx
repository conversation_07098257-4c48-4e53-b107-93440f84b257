﻿<%--
 =======================================================================
 FILE: Notifications.aspx
 PURPOSE: Displays a complete history of all notifications for the user.
 =======================================================================
--%>
<%@ Page Title="All Notifications" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="Notifications.aspx.cs" Inherits="MediEase.Customer.Notifications" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>Notification History</h1>
            <a href="Dashboard.aspx" class="btn btn-outline-secondary">&larr; Back to Dashboard</a>
        </div>
        <div class="card shadow-sm">
            <div class="card-body">
                <asp:Repeater ID="AllNotificationsRepeater" runat="server">
                    <HeaderTemplate>
                        <div class="list-group list-group-flush">
                    </HeaderTemplate>
                    <ItemTemplate>
                        <a href="<%# Eval("LinkUrl") %>" class='list-group-item list-group-item-action <%# !Convert.ToBoolean(Eval("IsRead")) ? "font-weight-bold" : "" %>'>
                            <div class="d-flex w-100 justify-content-between">
                                <p class="mb-1"><%# Eval("Message") %></p>
                                <small><%# Convert.ToDateTime(Eval("CreatedDate")).ToString("MMM dd, yyyy") %></small>
                            </div>
                        </a>
                    </ItemTemplate>
                    <FooterTemplate>
                        </div>
                    </FooterTemplate>
                </asp:Repeater>
                 <asp:PlaceHolder ID="NoHistoryPlaceholder" runat="server" Visible="false">
                    <div class="text-center p-5 text-muted">You do not have any notifications yet.</div>
                </asp:PlaceHolder>
            </div>
        </div>
    </div>
</asp:Content>