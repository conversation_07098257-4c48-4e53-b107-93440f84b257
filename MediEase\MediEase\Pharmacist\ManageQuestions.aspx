﻿<%--
 =======================================================================
 FILE: ManageQuestions.aspx
 PURPOSE: Allows pharmacists to view and answer questions submitted by
          customers.
 PLACE IN: The 'Pharmacist' folder in your project root.
 =======================================================================
--%>
<%@ Page Title="Manage Customer Questions" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="ManageQuestions.aspx.cs" Inherits="MediEase.Pharmacist.ManageQuestions" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>Manage Customer Questions</h1>
            <a href="Dashboard.aspx" class="btn btn-outline-secondary">&larr; Back to Dashboard</a>
        </div>

        <div class="card shadow-sm">
            <div class="card-header">
                <!-- Nav tabs -->
                <ul class="nav nav-tabs card-header-tabs">
                    <li class="nav-item">
                        <asp:LinkButton ID="UnansweredTab" runat="server" CssClass="nav-link" OnClick="Tab_Click" CommandArgument="Unanswered">Unanswered</asp:LinkButton>
                    </li>
                    <li class="nav-item">
                        <asp:LinkButton ID="AnsweredTab" runat="server" CssClass="nav-link" OnClick="Tab_Click" CommandArgument="Answered">Answered</asp:LinkButton>
                    </li>
                </ul>
            </div>
            <div class="card-body">
                <asp:GridView ID="QuestionsGrid" runat="server"
                    CssClass="table table-hover table-striped"
                    AutoGenerateColumns="False"
                    DataKeyNames="QuestionID"
                    GridLines="None"
                    AllowPaging="True"
                    OnPageIndexChanging="QuestionsGrid_PageIndexChanging"
                    PageSize="10">
                    <Columns>
                        <asp:BoundField DataField="QuestionID" HeaderText="ID" />
                        <asp:BoundField DataField="CustomerName" HeaderText="Customer" />
                        <asp:BoundField DataField="Subject" HeaderText="Subject" />
                        <asp:BoundField DataField="QuestionDate" HeaderText="Date Asked" DataFormatString="{0:MMM dd, yyyy}" />
                        <asp:HyperLinkField DataNavigateUrlFields="QuestionID"
                            DataNavigateUrlFormatString="~/Pharmacist/AnswerQuestion.aspx?QuestionID={0}"
                            Text="View &amp; Answer"
                            ControlStyle-CssClass="btn btn-sm btn-primary" />
                    </Columns>
                    <EmptyDataTemplate>
                        <div class="alert alert-info text-center">
                            There are no questions in this category.
                        </div>
                    </EmptyDataTemplate>
                </asp:GridView>
            </div>
        </div>
    </div>
</asp:Content>
