﻿using System;
using System.Web;
using System.Web.Routing;

namespace MediEase
{
    public class Global : HttpApplication
    {
        void Application_Start(object sender, EventArgs e)
        {
            // Code that runs on application startup
            // This line calls the RouteConfig class, which should be
            // defined separately in its own file (e.g., App_Start/RouteConfig.cs)
            RouteConfig.RegisterRoutes(RouteTable.Routes);
        }
    }

    // NOTE: The RouteConfig class definition has been removed from this file
    // to prevent the "already contains a definition" error.
    // Ensure you have a file like 'App_Start/RouteConfig.cs' with the following content:
    /*
    using System.Web.Routing;

    namespace MediEase
    {
        public static class RouteConfig
        {
            public static void RegisterRoutes(RouteCollection routes)
            {
                routes.MapPageRoute("Default", "", "~/Default.aspx");
            }
        }
    }
    */
}
